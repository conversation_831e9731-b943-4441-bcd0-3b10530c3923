As rotinas devem ser executadas na seguinte ordem:
apolice
endosso
liquidacaoPremio
sinistro
movimentoSinistro

Comandos para executar a aplicação:

Grupo/Ramo => VIDA

java -jar seguros-registradorasusep-batch.jar --layout=apolice --grupoRamo=VIDA --complemento=

java -jar seguros-registradorasusep-batch.jar --layout=endosso --grupoRamo=VIDA --complemento=

java -jar seguros-registradorasusep-batch.jar --layout=liquidacaoPremio --grupoRamo=VIDA --complemento=

java -jar seguros-registradorasusep-batch.jar --layout=sinistro --grupoRamo=VIDA --complemento=

java -jar seguros-registradorasusep-batch.jar --layout=movimentoSinistro --grupoRamo=VIDA --complemento=


Grupo/Ramo => RE

java -jar seguros-registradorasusep-batch.jar --layout=apolice --grupoRamo=RE --complemento=

java -jar seguros-registradorasusep-batch.jar --layout=endosso --grupoRamo=RE --complemento=

java -jar seguros-registradorasusep-batch.jar --layout=liquidacaoPremio --grupoRamo=RE --complemento=

java -jar seguros-registradorasusep-batch.jar --layout=sinistro --grupoRamo=RE --complemento=

java -jar seguros-registradorasusep-batch.jar --layout=movimentoSinistro --grupoRamo=RE --complemento=


Grupo/Ramo => AUTO

java -jar seguros-registradorasusep-batch.jar --layout=apolice --grupoRamo=AUTO --complemento=

java -jar seguros-registradorasusep-batch.jar --layout=complementar --grupoRamo=AUTO --complemento=CTR

java -jar seguros-registradorasusep-batch.jar --layout=endosso --grupoRamo=AUTO --complemento=

java -jar seguros-registradorasusep-batch.jar --layout=complementar --grupoRamo=AUTO --complemento=EDS

java -jar seguros-registradorasusep-batch.jar --layout=liquidacaoPremio --grupoRamo=AUTO --complemento=

java -jar seguros-registradorasusep-batch.jar --layout=sinistro --grupoRamo=AUTO --complemento=

java -jar seguros-registradorasusep-batch.jar --layout=movimentoSinistro --grupoRamo=AUTO --complemento=

OBS: Caso utilizar arquivo de configuração externo adicionar o seguinte comando => --spring.config.location=file:///{caminho para o arquivo}\application.yml
