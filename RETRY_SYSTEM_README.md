# Sistema de Retry Automático para Jobs Batch

## 📋 Visão Geral

Este sistema implementa um mecanismo robusto de retry automático para resolver o problema de falhas na primeira execução dos jobs batch em produção. O sistema opera em duas camadas:

1. **Retry no nível do Job** - Reexecuta o job completo em caso de falha
2. **Retry no nível do Step** - Reexecuta steps individuais e operações específicas

## 🚀 Funcionalidades Implementadas

### ✅ JobRetryStarter
- Substitui o `JobStarter` original
- Executa múltiplas tentativas do job completo
- Log detalhado de cada tentativa
- Configurável via properties

### ✅ EnhancedBatchWarmup
- Warmup aprimorado com múltiplas tentativas
- Verificação de múltiplos endpoints de saúde
- Não falha o job se o warmup falhar
- Verificação de prontidão da aplicação

### ✅ RetryConfiguration
- Políticas de retry configuráveis
- Backoff exponencial
- Wrappers para Reader, Processor e Writer
- Skip policy para itens problemáticos

### ✅ RetryJobBuilder
- Utilitário para construir jobs com retry
- Simplifica a aplicação do sistema
- Configuração centralizada

## ⚙️ Configuração

### application.yml
```yaml
# Configurações de Retry para Jobs
job:
  retry:
    enabled: true          # Habilita/desabilita o sistema de retry
    maxAttempts: 3         # Número máximo de tentativas
    delaySeconds: 30       # Intervalo entre tentativas em segundos

# Configurações de Retry para Steps
step:
  retry:
    enabled: true          # Habilita/desabilita retry nos steps
    maxAttempts: 3         # Número máximo de tentativas por step
    initialInterval: 2000  # Intervalo inicial em ms
    maxInterval: 10000     # Intervalo máximo em ms
    multiplier: 2.0        # Multiplicador para backoff exponencial

# Configurações de Warmup
warmup:
  enabled: true           # Habilita/desabilita warmup
  maxAttempts: 5          # Número máximo de tentativas de warmup
  delaySeconds: 3         # Intervalo entre tentativas de warmup
  timeoutSeconds: 10      # Timeout para cada tentativa
```

## 📝 Como Usar

### Para Jobs que Precisam de Warmup (como Apólice)
```java
@Bean
public Job apoliceJob(Step apoliceStep) {
    return jobBuilderFactory
            .get("apoliceJob")
            .start(enhancedBatchWarmup.enhancedWarmupBatchStep())
            .next(enhancedBatchWarmup.applicationReadinessStep())
            .next(apoliceStep)
            .incrementer(new RunIdIncrementer())
            .build();
}
```

### Para Jobs Simples (usando RetryJobBuilder)
```java
@Bean
public Job endossoJob(Step endossoStep) {
    return retryJobBuilder.buildSimpleJobWithRetry("endossoJob", endossoStep);
}
```

### Para Steps com Retry
```java
@Bean
public Step apoliceStep(ItemReader<Integer> reader,
                       ItemProcessor<Integer, Integer> processor,
                       ItemWriter<Integer> writer) {
    return retryJobBuilder.buildStepWithRetry(
        "apoliceStep", reader, processor, writer, 1);
}
```

## 🔍 Logs e Monitoramento

O sistema produz logs detalhados para facilitar o monitoramento:

```
=== TENTATIVA 1 de 3 ===
🔥 Iniciando warmup aprimorado da plataforma...
✅ Warmup bem-sucedido na tentativa 1
✅ Aplicação está pronta para processamento
📊 Resultado da tentativa 1: Status=COMPLETED
✅ Job executado com SUCESSO na tentativa 1
```

Em caso de falha:
```
❌ Tentativa 1 falhou com status: FAILED
💥 Erro detalhado (tentativa 1): Connection refused
⏱️ Aguardando 30 segundos antes da próxima tentativa...
=== TENTATIVA 2 de 3 ===
```

## 🎯 Benefícios

1. **Resiliência**: Jobs não falham por problemas temporários
2. **Transparência**: Logs detalhados para debugging
3. **Flexibilidade**: Configuração granular por ambiente
4. **Compatibilidade**: Funciona com jobs existentes
5. **Performance**: Warmup inteligente reduz falhas

## 🔧 Personalização

### Desabilitar Retry para Jobs Específicos
```yaml
job:
  retry:
    enabled: false
```

### Configurar Retry Apenas para Steps
```yaml
job:
  retry:
    enabled: false
step:
  retry:
    enabled: true
```

### Ajustar Timeouts por Ambiente
```yaml
# Produção
warmup:
  maxAttempts: 10
  delaySeconds: 5

# Desenvolvimento  
warmup:
  maxAttempts: 2
  delaySeconds: 1
```

## 🚨 Exceções que NÃO são Retentadas

- `RotinaException` - Erros de negócio
- `IllegalArgumentException` - Parâmetros inválidos
- `IllegalStateException` - Estado inválido

## 📈 Próximos Passos

1. Aplicar o sistema para todos os jobs do projeto
2. Configurar alertas baseados nos logs
3. Ajustar configurações baseado no comportamento em produção
4. Implementar métricas de sucesso/falha

## 🔄 Migração dos Jobs Existentes

Para migrar um job existente:

1. Substitua `JobBuilderFactory` por `RetryJobBuilder`
2. Use `buildJobWithRetry()` ou `buildSimpleJobWithRetry()`
3. Configure as properties necessárias
4. Teste em ambiente de desenvolvimento

Exemplo de migração:
```java
// ANTES
return jobBuilderFactory.get("meuJob").start(step).build();

// DEPOIS  
return retryJobBuilder.buildSimpleJobWithRetry("meuJob", step);
```
