package br.com.banestes.sgrs.sro.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import br.com.banestes.common.auth.oauth2.OAuth2ClientCredentials;
import br.com.banestes.sgrs.registradorasusep.config.SsoConfig;

@Configuration
public class OAuth2Configuration
{
    @Bean
    public OAuth2ClientCredentials oAuth2ClientCredentials(SsoConfig ssoConfig) {
        return new OAuth2ClientCredentials(
            ssoConfig.getUrl(),
            ssoConfig.getRealm(),
            ssoConfig.getClientId(),
            ssoConfig.getClientSecret(),
            ssoConfig.getGrantType(),
            ssoConfig.getUsername(),
            ssoConfig.getPassword(),
            ssoConfig.getScope()
        );
    }
}
