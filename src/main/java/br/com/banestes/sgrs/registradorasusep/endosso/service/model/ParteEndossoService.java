package br.com.banestes.sgrs.registradorasusep.endosso.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.endosso.repository.ParteEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.model.endosso.ParteEndosso;

import java.util.List;

@Service
public class ParteEndossoService {
    private final ParteEndossoRepository parteRepository;

    public ParteEndossoService(ParteEndossoRepository parteRepository) {
        this.parteRepository = parteRepository;
    }

    public List<ParteEndosso> findAllByIdtCtlProcAndIdtCtrApolice(Integer idtCtlProc, Long idtEdsEndosso) {
        return parteRepository.findAllByIdtCtlProcAndIdtEdsEndosso(idtCtlProc, idtEdsEndosso);
    }

}
