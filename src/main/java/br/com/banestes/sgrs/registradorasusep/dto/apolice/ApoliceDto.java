package br.com.banestes.sgrs.registradorasusep.dto.apolice;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

import br.com.banestes.sgrs.registradorasusep.dto.AutomovelDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.ObjetoSeguradoDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.RamoPessoaDto;


@Getter
@Setter
public class ApoliceDto {
    private String identificadorRegistro;
    private String codigoSeguradora;
    private String apoliceCodigo;
    private String numeroSusepApolice;
    private String tipoDocumentoEmitido;
    private String certificadoCodigo;
    private String grupoComercial;
    private String ramoComercial;
    private String renovada;
    private String apoliceRenovadaCodigo;
    private String tipoEmissao;
    private String dataEmissao;
    private String dataInicio;
    private String dataInicioCoberta;
    private String dataTermino;
    private String codigoSeguradoraLider;
    private String apoliceCodigoLider;
    private String codigoFilial;
    private String moedaApolice;
    private Double limiteMaximoGarantia;
    private Double limiteMaximoGarantiaReal;
    private PropostaDto proposta;
    private List<ContraGarantiaDto> contragarantias;
    private List<IntermediarioDto> intermediarios;
    private List<ParteDto> partes;
    private List<ExteriorDto> exteriores;
    private List<CreditoInternoExportacaoDto> creditoInterno;
    private List<StopLossDto> stopLoss;
    private List<ObjetoSeguradoDto> objetosSegurado;
    private PremioApoliceDto premioApolice;
    private List<CreditoInternoExportacaoDto> creditoExportacao;
    private CosseguroDto cosseguro;
    private String coberturaBasica;
    private AutomovelDto automovel;
    private ContribuicaoApolice contribuicaoApolice;
    private List<ParcelaApoliceDto> parcelas;
    private List<RamoPessoaDto> ramosPessoas;
    private ContratoColetivoDto dadosContratoColetivo;
}
