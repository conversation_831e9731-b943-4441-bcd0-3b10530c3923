package br.com.banestes.sgrs.registradorasusep.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.ControleEndosso;

import java.util.List;

@Repository
public interface ControleEndossoRepository extends JpaRepository<ControleEndosso, Long> {

    @Query("select controleEndosso from ControleEndosso controleEndosso "
            + "where (:idtCtlProc is null or controleEndosso.idtCtlProc = :idtCtlProc) "
            + "and (controleEndosso.idcSitProc = 'S') ")
    List<ControleEndosso> listarControleEndossoPendentes(@Param("idtCtlProc") Integer numeroProcessamento);
}
