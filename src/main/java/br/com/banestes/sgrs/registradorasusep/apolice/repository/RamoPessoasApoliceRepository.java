package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.RamoPessoasApolice;

import java.util.List;

@Repository
public interface RamoPessoasApoliceRepository extends JpaRepository<RamoPessoasApolice, Integer> {

    List<RamoPessoasApolice> findAllByIdtCtrObjetoAndIdtCtlProc(Long idtCtrObjeto, Integer idtCtlProc);

}

