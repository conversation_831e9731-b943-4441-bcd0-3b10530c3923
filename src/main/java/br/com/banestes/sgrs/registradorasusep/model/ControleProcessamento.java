package br.com.banestes.sgrs.registradorasusep.model;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Date;
import java.sql.Timestamp;

@Getter
@Setter
@Entity
@Table(name = "SRO_CTL_PROC")
public class ControleProcessamento {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "DES_RMO_PROC")
    private String desRmoProc;
    @Column(name = "IDC_TIP_PROC")
    private String idcTipProc;
    @Column(name = "DAT_PRZ_MVT_PROC")
    private Date datPrzMvtProc;
    @Column(name = "DAT_PRZ_ETQ_PROC")
    private Date datPrzEtqProc;
    @Column(name = "QTD_DIA_PROC")
    private Integer qtdDiaProc;
    @Column(name = "DAT_INI_PROC")
    private Date datIniProc;
    @Column(name = "DAT_FIM_PROC")
    private Date datFimProc;
    @Column(name = "QTD_RTN_PROC")
    private Integer qtdRtnProc;
    @Column(name = "IDC_SIT_PROC")
    private String idcSitProc;
    @Column(name = "NUM_MATR_ULT_ATUALIZACAO")
    private Integer numMatrUltAtualizacao;
    @Column(name = "DAT_HOR_ULT_ATUALIZACAO")
    private Timestamp datHorUltAtualizacao;
}
