package br.com.banestes.sgrs.registradorasusep.liquidacaopremio.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.liquidacaopremio.service.model.LiquidacaoPremioService;
import br.com.banestes.sgrs.registradorasusep.model.premio.Premio;
import br.com.banestes.sgrs.registradorasusep.service.EnvioSusepService;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class TransmissaoLiquidacaoPremioService {
    @Value("${numeroErrosPlataforma}")
    private Integer numeroErrosPlataforma;
    private final LiquidacaoPremioService liquidacaoPremioService;
    private final ConstrutorLiquidacaoPremioService construtorLiquidacaoPremioService;
    private final EnvioSusepService envioSusepService;

    public TransmissaoLiquidacaoPremioService(LiquidacaoPremioService liquidacaoPremioService,
                                              ConstrutorLiquidacaoPremioService construtorLiquidacaoPremioService,
                                              EnvioSusepService envioSusepService) {

        this.liquidacaoPremioService = liquidacaoPremioService;
        this.construtorLiquidacaoPremioService = construtorLiquidacaoPremioService;
        this.envioSusepService = envioSusepService;
    }

    public void transmitirLiquidacaoPremio(Integer idtCtlProc, Character idcOperacao) {
        log.info("Controle de Processo: {}", idtCtlProc);
        if(idcOperacao.toString().equalsIgnoreCase("i")){
            log.info("Operação: INCLUSÃO");
            log.info("Leiaute Selecionado: Liquidação de Premio");
        } else if (idcOperacao.toString().equalsIgnoreCase("a")) {
            log.info("Operação: ALTERACAO");
            log.info("Leiaute Selecionado: Liquidação de Premio");
        }

        final List<Premio> premios = liquidacaoPremioService.listarLiquidacoesPremioTransmissao(idtCtlProc);

        Integer errorCount = 0;

        log.info("Número de liquidações de prêmio a serem processadas: {}", premios.size());
        log.info("Processando Liquidações De Prêmio...");

        for (Premio premio : premios) {
//            log.info("Liquidação de prêmio id: {} - apólice {}", premio.getIdtPrmPremio(), premio.getApoliceCodigo());
            if (!processaLiquidacaoPremio(premio, idcOperacao)) {
                errorCount++;
                if (Objects.equals(errorCount, numeroErrosPlataforma)) {
                    log.info("Processamento interrompido em liquidação de premio: {}",premio.getIdtPrmPremio());
                    return;
                }
            }
        }
        log.info("As liquidações de prêmio foram processadas com sucesso !");
    }

    private boolean processaLiquidacaoPremio(Premio premio, Character idcOperacao) {
        return liquidacaoPremioService.atualizarStatusTransmissao(premio,
            envioSusepService.transmitir(
                construtorLiquidacaoPremioService.construir(premio),
                idcOperacao.equals('I') ? "liquidacao-premio" : "liquidacao-premio/" + premio.getIdentificadorRegistro(),
                idcOperacao.equals('I') ? HttpMethod.POST : HttpMethod.PUT
            ),
            idcOperacao
        );
    }
}
