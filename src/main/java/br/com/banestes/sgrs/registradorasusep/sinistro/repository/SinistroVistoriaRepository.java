package br.com.banestes.sgrs.registradorasusep.sinistro.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroVistoria;

import java.util.List;

@Repository
public interface SinistroVistoriaRepository extends JpaRepository<SinistroVistoria, Integer> {

    List<SinistroVistoria> getAllByIdtCtlProcAndIdtSntSinistro(Integer idtCtlProc, Long idtSntSinistro);

}
