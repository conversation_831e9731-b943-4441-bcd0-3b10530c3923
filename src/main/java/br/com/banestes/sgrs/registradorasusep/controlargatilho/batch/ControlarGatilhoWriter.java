package br.com.banestes.sgrs.registradorasusep.controlargatilho.batch;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemWriter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class ControlarGatilhoWriter {
    @Bean
    public ItemWriter<Integer> controlarGatilhoItemWriter() {
        return retornos -> log.info("ItemWriter: {}", retornos.get(0));
    }
}
