package br.com.banestes.sgrs.registradorasusep.dto.sinistro;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroAuto;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DadoAutoDto {
    private String numeroConvenio;
    private String causaSinistro;
    private String sexoCondutor;
    private String dataNascimento;
    private String paisOcorrenciaSinistro;
    private String cepLocalidadeSinistro;
    private String codigoObjeto;

    public DadoAutoDto(SinistroAuto sinistroAuto) {
        this.numeroConvenio = sinistroAuto.getNumeroConvenio();
        this.causaSinistro = sinistroAuto.getCausaSinistro();
        this.sexoCondutor = sinistroAuto.getSexoCondutor();
        this.dataNascimento = sinistroAuto.getDataNascimento();
        this.paisOcorrenciaSinistro = sinistroAuto.getPaisOcorrenciaSinistro();
        this.cepLocalidadeSinistro = sinistroAuto.getCepLocalidadeSinistro();
        this.codigoObjeto = sinistroAuto.getCodigoObjeto();
    }
}
