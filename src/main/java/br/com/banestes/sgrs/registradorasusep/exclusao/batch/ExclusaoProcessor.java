package br.com.banestes.sgrs.registradorasusep.exclusao.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import br.com.banestes.sgrs.registradorasusep.exclusao.service.ExclusaoService;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoCadastro;

@Slf4j
@Component("exclusaoProcessor")
@RequiredArgsConstructor
public class ExclusaoProcessor implements ItemProcessor<ExclusaoCadastro, Integer> {

    private final ExclusaoService exclusaoService;

    @Override
    public Integer process(ExclusaoCadastro exclusaoCadastro) throws Exception {

        if (exclusaoService.excluirRegistros(exclusaoCadastro)) {
            return 0;
        }

        return 1;
    }
}
