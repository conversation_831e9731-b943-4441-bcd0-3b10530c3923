package br.com.banestes.sgrs.registradorasusep.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import java.sql.Timestamp;

@Slf4j
@Service
public class ExclusaoUpdateService {

    private final JdbcTemplate jdbcTemplateObject;
    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;

    public ExclusaoUpdateService(JdbcTemplate jdbcTemplateObject) {
        this.jdbcTemplateObject = jdbcTemplateObject;
    }

    public void updateExclusaoRegistro(Integer idtExcLeiaute, String mensagemProc, String situacao) {
        log.info(" Registro em SRO_EXC_LEIAUTE {} sendo alterado para a situação {} com mensagem {} ", idtExcLeiaute, situacao, mensagemProc);
        String sql = "update SRO_EXC_LEIAUTE set IDC_SIT_PROC = ?, DAT_HOR_ULT_ATUALIZACAO = ?, DES_MSG_PROC = ? where IDT_EXC_LEIAUTE = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, situacao, new Timestamp(System.currentTimeMillis()), mensagemProc == null ? "" : mensagemProc, idtExcLeiaute);
        }
    }

    public void updateExclusaoCadastroRegistro(Integer idtExcCadastro, String situacao) {
        log.info(" Registro em SRO_EXC_CADASTRO {} sendo alterado para a situação {}", idtExcCadastro, situacao);
        String sql = "update SRO_EXC_CADASTRO set IDC_SIT_PROC = ?, DAT_HOR_ULT_ATUALIZACAO = ? where IDT_EXC_CADASTRO = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, situacao, new Timestamp(System.currentTimeMillis()), idtExcCadastro);
        }
    }
}
