package br.com.banestes.sgrs.registradorasusep.sinistro.batch;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SinistroJobConfig {

    private final JobBuilderFactory jobBuilderFactory;

    public SinistroJobConfig(JobBuilderFactory jobBuilderFactory) {
        this.jobBuilderFactory = jobBuilderFactory;
    }

    @Bean
    public Job sinistroJob(Step sinistroStep) {

        return jobBuilderFactory
                .get("sinistroJob")
                .start(sinistroStep)
                .incrementer(new RunIdIncrementer())
                .build();
    }
}
