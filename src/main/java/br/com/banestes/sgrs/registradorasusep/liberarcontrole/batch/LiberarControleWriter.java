package br.com.banestes.sgrs.registradorasusep.liberarcontrole.batch;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemWriter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class LiberarControleWriter {
    @Bean
    public ItemWriter<Integer> liberarControleItemWriter() {
        return retornos -> log.info("ItemWriter: {}", retornos.get(0));
    }
}
