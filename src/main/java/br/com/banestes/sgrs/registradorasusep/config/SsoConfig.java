package br.com.banestes.sgrs.registradorasusep.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConstructorBinding;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@ConfigurationProperties(prefix = "banestes.sso")
@Getter
@Setter
@ConstructorBinding
public class SsoConfig {

    private static final String GRANT_TYPE = "password";

    public SsoConfig(@Value("${banestes.sso.url}") String url,
                     @Value("${banestes.sso.client-id}") String clientId,
                     @Value("${banestes.sso.client-secret}") String clientSecret,
                     @Value("${banestes.sso.realm}") String realm,
                     @Value("${banestes.sso.username}") String username,
                     @Value("${banestes.sso.password}") String password,
                     @Value("${banestes.sso.scope:}") String scope) {

        this.url = url;
        this.clientId = new String(Base64.getDecoder().decode(clientId), StandardCharsets.ISO_8859_1);
        this.clientSecret = new String(Base64.getDecoder().decode(clientSecret), StandardCharsets.ISO_8859_1);
        this.realm = realm;
        this.username = new String(Base64.getDecoder().decode(username), StandardCharsets.ISO_8859_1);
        this.password = new String(Base64.getDecoder().decode(password), StandardCharsets.ISO_8859_1);
        this.scope = scope;
    }

    private String url;
    private String clientId;
    private String clientSecret;
    private String realm;
    private String username;
    private String password;
    private String scope;

    public String getGrantType() {
        return GRANT_TYPE;
    }
}
