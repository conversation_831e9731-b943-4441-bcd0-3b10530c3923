package br.com.banestes.sgrs.registradorasusep.exclusao.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.exclusao.service.model.ExclusaoLayoutService;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoCadastro;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.service.ExclusaoUpdateService;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExclusaoService {

    @Value("${numeroErrosPlataforma}")
    private Integer numeroErrosPlataforma;
    private final ExclusaoLayoutService exclusaoLayoutService;
    private final EnvioExclusaoService envioExclusaoService;
    private final ExclusaoUpdateService exclusaoUpdateService;

    public ExclusaoService(ExclusaoLayoutService exclusaoLayoutService, EnvioExclusaoService envioExclusaoService, ExclusaoUpdateService exclusaoUpdateService) {
        this.exclusaoLayoutService = exclusaoLayoutService;
        this.envioExclusaoService = envioExclusaoService;
        this.exclusaoUpdateService = exclusaoUpdateService;
    }

    public boolean excluirRegistros(ExclusaoCadastro exclusaoCadastro) {

        log.info("Buscando registros para serem processados para o IDT_EXC_CADASTRO {}.", exclusaoCadastro.getIdtExcCadastro());

        final List<ExclusaoLayout> regsExclusao = exclusaoLayoutService.listarRegistrosExcluir(exclusaoCadastro.getIdtExcCadastro());

        Integer errorCount = 0;

        final List<ExclusaoLayout> regsExclusaoPendentes = regsExclusao.stream().filter(p -> p.getIdcSitProc().equals("S") ||
                p.getIdcSitProc().equals("P")).collect(Collectors.toList());

        log.info("Numero de registros a serem processadas: {}", regsExclusaoPendentes.size());
        log.info("Processando Regitros...");

        for (ExclusaoLayout exclusaoLayout : regsExclusaoPendentes) {
            log.info("Exclusão com IDT_EXC_LEIAUTE: {}", exclusaoLayout.getIdtExcLeiaute());
            if (!processaExclusao(exclusaoLayout)) {
                errorCount++;
                if (Objects.equals(errorCount, numeroErrosPlataforma)) {
                    log.info("Processamento interrompido...");
                    return false;
                }
            }
        }

        if (regsExclusao.stream().allMatch(p -> !p.getIdcSitProc().equals("S") && !p.getIdcSitProc().equals("P"))) {
            exclusaoUpdateService.updateExclusaoCadastroRegistro(exclusaoCadastro.getIdtExcCadastro(), "F");
        } else {
            log.info("Alguns registros em SRO_EXC_LEIAUTE ficaram pendentes ou com erros para o IDT_EXC_CADASTRO {} ", exclusaoCadastro.getIdtExcCadastro());
        }

        log.info("Os registros foram processados com sucesso!");
        return true;

    }

    private boolean processaExclusao(ExclusaoLayout exclusaoLayout) {
        return exclusaoLayoutService.atualizarStatusTransmissao(
                exclusaoLayout,
                envioExclusaoService.transmitir(exclusaoLayout, getEndPointName(exclusaoLayout.getDesLeiaute()))
        );
    }

    private String getEndPointName(String desLayout) {

        String endPoint = null;

        if ("MOVSINISTRO".equals(desLayout)) {
            endPoint = "movimento-sinistro";
        }

        if ("SINISTRO".equals(desLayout)) {
            endPoint = "sinistro";
        }

        if ("PREMIO".equals(desLayout)) {
            endPoint = "liquidacao-premio";
        }

        if ("COMPLEMENTARAUTOEDS".equals(desLayout) || "COMPLEMENTARAUTOCTR".equals(desLayout)) {
            endPoint = "complementar-auto";
        }

        if ("ENDOSSO".equals(desLayout)) {
            endPoint = "endosso";
        }

        if ("APOLICE".equals(desLayout)) {
            endPoint = "apolice";
        }

        return endPoint;
    }

}
