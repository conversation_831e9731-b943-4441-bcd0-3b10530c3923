package br.com.banestes.sgrs.registradorasusep.endosso.mapper;

import br.com.banestes.sgrs.registradorasusep.dto.endosso.*;
import br.com.banestes.sgrs.registradorasusep.model.endosso.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import br.com.banestes.sgrs.registradorasusep.dto.AutomovelDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.ParcelaApoliceDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.PremioApoliceDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.PropostaDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.ObjetoPatrimonialDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.cobertura.CoberturaDto;

import java.util.ArrayList;
import java.util.List;

@Component
public class EndossoMapper {

    public EndossoDto toDto(Endosso endosso,
                            List<ParteEndosso> partes,
                            List<IntermediarioEndosso> intermediarios,
                            List<ObjetoSeguradoCompleto> objetosSeguradosCompletos,
                            List<AutomovelEndosso> automoveisEndosso,
                            List<ParcelaEndosso> parcelas,
                            List<ContratoColetivoEndosso> contratosColetivoEndosso) {

        final EndossoDto endossoDto = new EndossoDto();
        endossoDto.setIdentificadorRegistro(endosso.getIdentificadorRegistro());
        endossoDto.setCodigoSeguradora(endosso.getCodigoSeguradora());
        endossoDto.setApoliceCodigo(endosso.getApoliceCodigo());
        endossoDto.setNumeroSusepApolice(endosso.getNumeroSusepApolice());
        endossoDto.setCertificadoCodigo(endosso.getCertificadoCodigo());
        endossoDto.setCodigoSeguradoraLider(endosso.getCodigoSeguradoraLider());
        endossoDto.setApoliceCodigoLider(endosso.getApoliceCodigoLider());
        endossoDto.setTipoEmissao(endosso.getTipoEmissao());
        endossoDto.setDataEmissao(endosso.getDataEmissao());
        endossoDto.setDataInicioDocumento(endosso.getDataInicioDocumento());
        endossoDto.setDataTerminoDocumento(endosso.getDataTerminoDocumento());
        endossoDto.setCodigoFilial(endosso.getCodigoFilial());
        endossoDto.setMoedaApolice(endosso.getMoedaApolice());
        endossoDto.setLimiteMaximoGarantia(endosso.getLimiteMaximoGarantia());
        endossoDto.setLimiteMaximoGarantiaReal(endosso.getLimiteMaximoGarantiaReal());
        endossoDto.setCoberturaBasica(endosso.getCoberturaBasica());
        endossoDto.setProposta(obterPropostaDto(endosso));
        endossoDto.setPartes(obterListaPartesDto(partes));
        endossoDto.setIntermediarios(obterListaIntermediariosDto(intermediarios));
        endossoDto.setObjetosSegurado(obterListaObjetosSeguradosDto(objetosSeguradosCompletos));
        endossoDto.setPremioApolice(obterPremioApoliceDto(endosso));
        endossoDto.setEndosso(obterEndossoModelDto(endosso));
        endossoDto.setParcelas(obterParcelasApolice(parcelas));

        if (CollectionUtils.isNotEmpty(contratosColetivoEndosso)) {
            endossoDto.setDadosContratoColetivo(obterContratoColetivoDto(contratosColetivoEndosso.get(0)));
        }

        if (CollectionUtils.isNotEmpty(automoveisEndosso)) {
            endossoDto.setAutomovel(obterListaAutomovelDto(automoveisEndosso.get(0)));
        }

        return endossoDto;
    }

    private List<ObjetoSeguradoDto> obterListaObjetosSeguradosDto(List<ObjetoSeguradoCompleto> objetosSeguradosCompletos) {

        final List<ObjetoSeguradoDto> objetosSeguradoEndossoDto = new ArrayList<>(objetosSeguradosCompletos.size());

        for (ObjetoSeguradoCompleto objetoSeguradoCompleto : objetosSeguradosCompletos) {
            ObjetoSeguradoDto objetoSeguradoEndossoDto = new ObjetoSeguradoDto();
            objetoSeguradoEndossoDto.setCodigo(objetoSeguradoCompleto.getObjetoSegurado().getCodigo());
            objetoSeguradoEndossoDto.setDataInicio(objetoSeguradoCompleto.getObjetoSegurado().getDataInicio());
            objetoSeguradoEndossoDto.setDataTermino(objetoSeguradoCompleto.getObjetoSegurado().getDataTermino());
            objetoSeguradoEndossoDto.setDescricaoObjeto(objetoSeguradoCompleto.getObjetoSegurado().getDescricaoObjeto());
            objetoSeguradoEndossoDto.setTipo(objetoSeguradoCompleto.getObjetoSegurado().getTipo());
            objetoSeguradoEndossoDto.setValor(objetoSeguradoCompleto.getObjetoSegurado().getValor());
            objetoSeguradoEndossoDto.setValorReal(objetoSeguradoCompleto.getObjetoSegurado().getValorReal());
            objetoSeguradoEndossoDto.setDescricaoTipo(objetoSeguradoCompleto.getObjetoSegurado().getDescricaoTipo());
            objetoSeguradoEndossoDto.setLocalRisco(objetoSeguradoCompleto.getObjetoSegurado().getLocalRisco());
            objetoSeguradoEndossoDto.setCoberturas(obterListaCoberturasDto(objetoSeguradoCompleto.getCoberturas()));

            if (CollectionUtils.isNotEmpty(objetoSeguradoCompleto.getObjetosPatrimoniais())) {
                objetoSeguradoEndossoDto.setObjetosPatrimoniais(objetoListaPatrimonialDtos(objetoSeguradoCompleto.getObjetosPatrimoniais()));
            }

            if (CollectionUtils.isNotEmpty(objetoSeguradoCompleto.getRamosPessoas())) {
                objetoSeguradoEndossoDto.setRamosPessoas(objetoListaRamosPessoasDtos(objetoSeguradoCompleto.getRamosPessoas()));
            }

            objetosSeguradoEndossoDto.add(objetoSeguradoEndossoDto);
        }
        return objetosSeguradoEndossoDto;
    }

    private List<CoberturaDto> obterListaCoberturasDto(List<CoberturaEndosso> coberturas) {

        final List<CoberturaDto> coberturasDto = new ArrayList<>(coberturas.size());

        for (CoberturaEndosso cobertura : coberturas) {
            CoberturaDto coberturaDto = new CoberturaDto();
            coberturaDto.setCoberturaCaracteristica(cobertura.getCoberturaCaracteristica());
            coberturaDto.setCoberturaPrincipal(cobertura.getCoberturaPrincipal());
            coberturaDto.setCoberturaTipo(cobertura.getCoberturaTipos());
            coberturaDto.setCodigo(cobertura.getCodigo());
            coberturaDto.setDataInicioCobertura(cobertura.getDataInicioCobertura());
            coberturaDto.setDataTerminoCobertura(cobertura.getDataTerminoCobertura());
            coberturaDto.setGrupo(cobertura.getGrupo());
            coberturaDto.setLimiteMaximoIndenizacao(cobertura.getLimiteMaximoIndenizacao());
            coberturaDto.setLimiteMaximoIndenizacaoReal(cobertura.getLimiteMaximoIndenizacaoReal());
            coberturaDto.setNumeroProcesso(cobertura.getNumeroProcesso());
            coberturaDto.setRamo(cobertura.getRamo());
            coberturaDto.setValorPremio(cobertura.getValorPremio());
            coberturaDto.setValorPremioReal(cobertura.getValorPremioReal());
            coberturaDto.setOutrasDescricao(cobertura.getOutrasDescricao());
            coberturaDto.setCoberturaInternaSeguradora(cobertura.getCoberturaInternaSeguradora());
            coberturaDto.setIndiceAtualizacao(cobertura.getIndiceAtualizacao());
            coberturaDto.setPeriodicidadeAtualizacao(cobertura.getPeriodicidadeAtualizacao());
            coberturaDto.setPeriodicidadeUnidade(cobertura.getPeriodicidadeUnidade());
            coberturaDto.setPeriodicidadePremio(cobertura.getPeriodicidadePremio());
            coberturaDto.setDescricaoPeriodicidade(cobertura.getDescricacaoPeriodicidade());
            coberturaDto.setTipoRisco(cobertura.getTipoRisco());
            coberturaDto.setCarenciaPeriodo(cobertura.getCarenciaPeriodo());
            coberturaDto.setCarenciaPeriodicidade(cobertura.getCarenciaPeriodicidade());
            coberturaDto.setCarenciaPeriodicidadeDias(cobertura.getCarenciaPeriodicidadeDias());
            coberturaDto.setCarenciaDataInicio(cobertura.getCarenciaDataInicio());
            coberturaDto.setCarenciaDataTermino(cobertura.getCarenciaDataTermino());
            coberturaDto.setDataInicioPremio(cobertura.getDataInicioPremio());
            coberturaDto.setDataTerminoPremio(cobertura.getDataTerminoPremio());
            coberturaDto.setIof(cobertura.getIof());
            coberturaDto.setCusto(cobertura.getCusto());
            coberturaDto.setCustoReal(cobertura.getCustoReal());
            coberturaDto.setLimiteMaximoIndenizacaoSublimite(cobertura.getLimiteMaximoIndenizacaoSublimite());
            coberturasDto.add(coberturaDto);
        }
        return coberturasDto;
    }

    private List<IntermediarioDto> obterListaIntermediariosDto(List<IntermediarioEndosso> intermediarios) {

        final List<IntermediarioDto> intermediariosDto = new ArrayList<>(intermediarios.size());

        for (IntermediarioEndosso intermediario : intermediarios) {
            intermediariosDto.add(new IntermediarioDto(intermediario));
        }
        return intermediariosDto;
    }

    private List<ParteDto> obterListaPartesDto(List<ParteEndosso> partes) {

        final List<ParteDto> partesDto = new ArrayList<>(partes.size());

        for (ParteEndosso parte : partes) {
            partesDto.add(new ParteDto(parte));
        }
        return partesDto;
    }

    private PropostaDto obterPropostaDto(Endosso endosso) {
        final PropostaDto propostaDto = new PropostaDto();
        propostaDto.setCodigoProposta(endosso.getCodigoProposta());
        propostaDto.setDataAssinatura(endosso.getDataAssinatura());
        propostaDto.setDataProtocolo(endosso.getDataProtocolo());
        return propostaDto;
    }

    private List<ParcelaApoliceDto> obterParcelasApolice(List<ParcelaEndosso> parcelas) {

        final List<ParcelaApoliceDto> parcelasDto = new ArrayList<>(parcelas.size());

        parcelas.forEach(p -> {
            parcelasDto.add(new ParcelaApoliceDto(p.getParcelaNumero(), p.getMoedaParcela(), p.getValor(), p.getValorReal(), p.getDataVencimento()));
        });

        return parcelasDto;
    }

    private PremioApoliceDto obterPremioApoliceDto(Endosso endosso) {
        final PremioApoliceDto premioApoliceDto = new PremioApoliceDto();
        premioApoliceDto.setValorTotal(endosso.getValorTotal());
        premioApoliceDto.setValorTotalReal(endosso.getValorTotalReal());
        premioApoliceDto.setAdicionalFracionamento(endosso.getAdicionalFracionamento());
        premioApoliceDto.setIof(endosso.getIof());
        premioApoliceDto.setNumeroParcelas(endosso.getNumeroParcelas());

        return premioApoliceDto;
    }

    private EndossoModelDto obterEndossoModelDto(Endosso endosso) {
        final EndossoModelDto endossoModelDto = new EndossoModelDto();
        endossoModelDto.setEndossoCodigo(endosso.getEndossoCodigo());
        endossoModelDto.setEndossoDescricao(endosso.getEndossoDescricao());
        endossoModelDto.setEndossoTipo(endosso.getEndossoTipo());
        endossoModelDto.setDataInicioEndosso(endosso.getDataInicioEndosso());
        endossoModelDto.setDataTerminoEndosso(endosso.getDataTerminoEndosso());
        endossoModelDto.setEndossoAverbavel(endosso.getEndossoAverbavel());
        endossoModelDto.setTipoDocumentoEndossado(endosso.getTipoDocumentoEndossado());
        return endossoModelDto;
    }

    private ContratoColetivoDto obterContratoColetivoDto(ContratoColetivoEndosso dadosContratoColetivo){
        final ContratoColetivoDto dadosContratoColetivoDto = new ContratoColetivoDto();
        dadosContratoColetivoDto.setTipoPlano(dadosContratoColetivo.getTipoPlano());
        return dadosContratoColetivoDto;
    }

    private AutomovelDto obterListaAutomovelDto(AutomovelEndosso auto) {

        final AutomovelDto automovelDtoEndosso = new AutomovelDto();
        automovelDtoEndosso.setRedeReparacao(auto.getRedeReparacao());
        automovelDtoEndosso.setTipoPecas(auto.getTipoPecas());
        automovelDtoEndosso.setClassificacaoPecas(auto.getClassificacaoPecas());
        automovelDtoEndosso.setNacionalidadePecas(auto.getNacionalidadePecas());
        automovelDtoEndosso.setTipoVigencia(auto.getTipoVigencia());
        automovelDtoEndosso.setFormasRecompensa(auto.getFormasRecompensa());
        automovelDtoEndosso.setBeneficiosAdicionais(auto.getBeneficiosAdicionais());
        automovelDtoEndosso.setPacotesAssistencia(auto.getPacotesAssitencia());
        automovelDtoEndosso.setRiscoDecorrido(auto.getRiscoDecorrido());

        return automovelDtoEndosso;
    }

    private List<ObjetoPatrimonialDto> objetoListaPatrimonialDtos(List<ObjetoPatrimonialEndosso> objetoPatrimonialEndossos) {

        List<ObjetoPatrimonialDto> values = new ArrayList<>(objetoPatrimonialEndossos.size());

        for (ObjetoPatrimonialEndosso objetoPatrimonialEndosso : objetoPatrimonialEndossos) {
            ObjetoPatrimonialDto objetoPatrimonialDto = new ObjetoPatrimonialDto();
            objetoPatrimonialDto.setTipoImovelSegurado(objetoPatrimonialEndosso.getTipoImovelSegurado());
            objetoPatrimonialDto.setTipoEstruturacaoCondominio(objetoPatrimonialEndosso.getTipoEstruturacaoCondominio());
            objetoPatrimonialDto.setCepObjetoSegurado(objetoPatrimonialEndosso.getCepObjetoSegurado());
            objetoPatrimonialDto.setCodigoCNAE(objetoPatrimonialEndosso.getCodigoCnae());
            values.add(objetoPatrimonialDto);
        }

        return values;
    }

    private List<RamoPessoaDto> objetoListaRamosPessoasDtos(List<RamoPessoasEndosso> objetoRamoPessoasEndossos) {

        List<RamoPessoaDto> values = new ArrayList<RamoPessoaDto>(objetoRamoPessoasEndossos.size());

        for (RamoPessoasEndosso objetoRamoPessoasEndosso : objetoRamoPessoasEndossos) {
            values.add(new RamoPessoaDto(objetoRamoPessoasEndosso));
        }

        return values;
    }
}
