package br.com.banestes.sgrs.registradorasusep.model.sinistro;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_SNT_DOCUMENTO")
public class SinistroDocumento {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_SNT_DOCUMENTO")
    private Long idtSntDocumento;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_SNT_SINISTRO")
    private Long idtSntSinistro;
    @Column(name = "APOLICE_CODIGO")
    private String apoliceCodigo;
    @Column(name = "CERTIFICADO_CODIGO")
    private String certificadoCodigo;
    @Column(name = "NUMERO_ENDOSSO")
    private String numeroEndosso;

}
