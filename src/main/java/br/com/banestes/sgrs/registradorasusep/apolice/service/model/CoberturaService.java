package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.CoberturaRepository;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Cobertura;

import java.util.List;

@Service
public class CoberturaService {
    private final CoberturaRepository coberturaRepository;

    public CoberturaService(CoberturaRepository coberturaRepository) {
        this.coberturaRepository = coberturaRepository;
    }

    public List<Cobertura> findAllByIdtCtlProcAndIdtCtrApoliceAndIdtCtrObjeto(Integer idtCtlProc, long idtCtrObjeto) {
        return coberturaRepository.findAllByIdtCtlProcAndIdtCtrObjeto(idtCtlProc, idtCtrObjeto);
    }

}
