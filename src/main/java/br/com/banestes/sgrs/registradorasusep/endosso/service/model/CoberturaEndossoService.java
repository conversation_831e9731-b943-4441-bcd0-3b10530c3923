package br.com.banestes.sgrs.registradorasusep.endosso.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.endosso.repository.CoberturaEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.model.endosso.CoberturaEndosso;

import java.util.List;

@Service
public class CoberturaEndossoService {
    private final CoberturaEndossoRepository coberturaRepository;

    public CoberturaEndossoService(CoberturaEndossoRepository coberturaRepository) {
        this.coberturaRepository = coberturaRepository;
    }

    public List<CoberturaEndosso> findAllByIdtCtlProcAndIdtEdsEndossoAndIdtCtrObjeto(Integer idtCtlProc, Long idtEdsObjeto) {

        return coberturaRepository.findAllByIdtCtlProcAndIdtEdsObjeto(idtCtlProc, idtEdsObjeto);
    }

}
