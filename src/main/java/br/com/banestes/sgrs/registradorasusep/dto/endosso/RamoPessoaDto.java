package br.com.banestes.sgrs.registradorasusep.dto.endosso;
import br.com.banestes.sgrs.registradorasusep.model.endosso.DependenteEndosso;
import br.com.banestes.sgrs.registradorasusep.model.endosso.RamoPessoasEndosso;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ToString
public class RamoPessoaDto {
    private String grupoCobertura;
    private String ramoCobertura;
    private String coberturaInternaSeguradora;
    private String inclusaoDependentes;
    private String abrangenciaViagem;
    private PrestamistaDto prestamistas;
    private List<DependenteDto> dependentes;

    public RamoPessoaDto(RamoPessoasEndosso ramoPessoa) {
        this.grupoCobertura = ramoPessoa.getGrupoCobertura();
        this.ramoCobertura = ramoPessoa.getRamoCobertura();
        this.coberturaInternaSeguradora = ramoPessoa.getCoberturaInterna();
        this.inclusaoDependentes = ramoPessoa.getIncluiDependentes();
        this.abrangenciaViagem = ramoPessoa.getAbrangeViagem();

        if (ramoPessoa.getPrestamistas().size() > 0){
            this.prestamistas = new PrestamistaDto(ramoPessoa.getPrestamistas().get(0));
        }

        this.dependentes = new ArrayList<DependenteDto>(ramoPessoa.getDependentes().size());
        for (DependenteEndosso dependente : ramoPessoa.getDependentes()) {
            this.dependentes.add(new DependenteDto(dependente));
        }

    }
}
