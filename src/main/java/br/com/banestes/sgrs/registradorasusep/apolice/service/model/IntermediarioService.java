package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.IntermediarioRepository;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Intermediario;

import java.util.List;

@Service
public class IntermediarioService {
    private final IntermediarioRepository intermediarioRepository;

    public IntermediarioService(IntermediarioRepository intermediarioRepository) {
        this.intermediarioRepository = intermediarioRepository;
    }

    public List<Intermediario> findAllByIdtCtlProcAndIdtCtrApolice(Integer idtCtlProc, Long idtCtrApolice) {
        return intermediarioRepository.findAllByIdtCtlProcAndIdtCtrApolice(idtCtlProc, idtCtrApolice);
    }

}
