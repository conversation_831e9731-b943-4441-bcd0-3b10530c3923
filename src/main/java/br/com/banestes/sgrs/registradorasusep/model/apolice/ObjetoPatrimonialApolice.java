package br.com.banestes.sgrs.registradorasusep.model.apolice;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_CTR_PATRIMONIAL")
@AllArgsConstructor
@NoArgsConstructor
public class ObjetoPatrimonialApolice {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTR_PATRIMONIAL")
    private Long idtCtrPatrimonial;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_CTR_OBJETO")
    private Long idtCtrObjeto;
    @Column(name = "TIPO_IMOVEL_SEGURADO")
    private String tipoImovelSegurado;
    @Column(name = "TIPO_ESTRUTURACAO_CONDOMINIO")
    private String tipoEstruturacaoCondominio;
    @Column(name = "CEP_OBJETO_SEGURADO")
    private String cepObjetoSegurado;
    @Column(name = "CODIGO_CNAE")
    private String codigoCnae;

}
