package br.com.banestes.sgrs.registradorasusep.sinistro.batch;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.database.StoredProcedureItemReader;
import org.springframework.batch.item.database.builder.StoredProcedureItemReaderBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.SqlOutParameter;
import org.springframework.jdbc.core.SqlParameter;

import br.com.banestes.sgrs.registradorasusep.constants.Constants;

import javax.sql.DataSource;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;

@Slf4j
@Configuration
public class SinistroReader {

    private static final String PROCEDURE_NAME = "SP_SRO_GERAR_SINISTRO";

    @Value("${grupoRamo:}")
    private String grupoRamo;

    @Bean
    @StepScope
    public StoredProcedureItemReader<Integer> sinistroItemReader(
            @Qualifier("sqlServerReadDataSource") DataSource dataSource) {
        log.info("Lendo procedure: {}", PROCEDURE_NAME);
        return new StoredProcedureItemReaderBuilder<Integer>()
                .name("sinistroItemReader")
                .dataSource(dataSource)
                .procedureName(PROCEDURE_NAME)
                .parameters(
                        new SqlParameter("des_grp_rmo_proc", Types.VARCHAR),
                        new SqlOutParameter("saida", Types.CHAR))
                .rowMapper(new SinistroRowMapper())
                .preparedStatementSetter(new PreparedStatementSetter() {
                    @Override
                    public void setValues(PreparedStatement ps) throws SQLException {
                        ps.setString(Constants.PROCEDURE_INDEX_RAMO, grupoRamo);
                        ps.setString(Constants.PROCEDURE_INDEX_SAIDA, "A");
                    }
                }).build();
    }

}
