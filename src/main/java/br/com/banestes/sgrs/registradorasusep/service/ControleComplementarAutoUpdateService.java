package br.com.banestes.sgrs.registradorasusep.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.ControleComplementarAuto;

import javax.sql.DataSource;
import java.sql.Timestamp;

@Slf4j
@Service
public class ControleComplementarAutoUpdateService {

    private final JdbcTemplate jdbcTemplateObject;
    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;

    public ControleComplementarAutoUpdateService(@Qualifier("sqlServerDataSource") DataSource dataSource, JdbcTemplate jdbcTemplateObject) {
        this.jdbcTemplateObject = jdbcTemplateObject;
        this.jdbcTemplateObject.setDataSource(dataSource);
    }

    public void atualizarSituacao(
        Long idtCtlCmpAuto,
        Integer idtCtlProc,
        String situacaoProcessamento,
        String mensagemErroProcessamento,
        Character idcOperacao
    ) {
        if ("E".equals(situacaoProcessamento)) {
            updateErro(idtCtlCmpAuto, idtCtlProc, mensagemErroProcessamento);
        } else if ("P".equals(situacaoProcessamento)) {
            updateErroPlataforma(idtCtlCmpAuto, idtCtlProc, mensagemErroProcessamento);
        } else {
            updateSucesso(idtCtlCmpAuto, idtCtlProc, idcOperacao);
        }
    }

    public void updateRegistroCorrecaoIngnorar(ControleComplementarAuto controleComplementarAuto) {
        String sql = "UPDATE SRO_CTL_CMP_AUTO SET IDC_SIT_PROC = 'I', DAT_HOR_ULT_ATUALIZACAO = ? WHERE IDT_CTL_CMP_AUTO = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, new Timestamp(System.currentTimeMillis()), controleComplementarAuto.getIdtCtlCmpAutoAtu());
        }
    }

    private void updateSucesso(Long idtCtlCmpAuto, Integer idtCtlProc, Character idcOperacao) {
//        log.info("Nao houve erro na transmissao estatus sendo atualizado para: 'R'");
        String sql = "update SRO_CTL_CMP_AUTO set IDC_SIT_PROC = 'R', DES_MSG_PROC = '', DAT_HOR_ULT_ATUALIZACAO = ? where IDT_CTL_CMP_AUTO = ? and IDT_CTL_PROC = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, new Timestamp(System.currentTimeMillis()), idtCtlCmpAuto, idtCtlProc);
            if (idcOperacao.equals('I')) {
                String sqlUpdateDatHorRegistro = "update SRO_CTL_CMP_AUTO set DAT_HOR_REGISTRO = ? where IDT_CTL_CMP_AUTO = ? and IDT_CTL_PROC = ?";
                jdbcTemplateObject.update(sqlUpdateDatHorRegistro, new Timestamp(System.currentTimeMillis()), idtCtlCmpAuto, idtCtlProc);
            }
        }
    }

    private void updateErro(Long idtCtlCmpAuto, Integer idtCtlProc, String mensagemErroProcessamento) {
//        log.info("Houve erro na transmissao estatus sendo atualizado para: 'E' mensagem: {}", mensagemErroProcessamento);
        String sql = "update SRO_CTL_CMP_AUTO set IDC_SIT_PROC = 'E', DES_MSG_PROC = ?, DAT_HOR_ULT_ATUALIZACAO = ? where IDT_CTL_CMP_AUTO = ? and IDT_CTL_PROC = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, mensagemErroProcessamento, new Timestamp(System.currentTimeMillis()), idtCtlCmpAuto, idtCtlProc);
        }
    }

    private void updateErroPlataforma(Long idtCtlCmpAuto, Integer idtCtlProc, String mensagemErroProcessamento) {
//        log.info("Houve erro de plataforma na transmissao estatus sendo atualizado para: 'P' mensagem: {}", mensagemErroProcessamento);
        String sql = "update SRO_CTL_CMP_AUTO set IDC_SIT_PROC = 'P', DES_MSG_PROC = ?, DAT_HOR_ULT_ATUALIZACAO = ? where IDT_CTL_CMP_AUTO = ? and IDT_CTL_PROC = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, mensagemErroProcessamento, new Timestamp(System.currentTimeMillis()), idtCtlCmpAuto, idtCtlProc);
        }
    }

    public void updateExclusaoRegistro(Long idtCtlApolice, Integer numeroMatr, String situacao) {
        String sql = "update SRO_CTL_CMP_AUTO set IDC_SIT_PROC = ?, DAT_HOR_ULT_ATUALIZACAO = ?, NUM_MATR_ULT_ATUALIZACAO = ? where IDT_CTL_CMP_AUTO = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, situacao, new Timestamp(System.currentTimeMillis()), numeroMatr, idtCtlApolice);
        }
    }

}
