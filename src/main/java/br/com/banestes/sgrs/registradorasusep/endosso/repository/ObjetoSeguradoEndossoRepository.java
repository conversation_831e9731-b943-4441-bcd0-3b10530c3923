package br.com.banestes.sgrs.registradorasusep.endosso.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.endosso.ObjetoSeguradoEndosso;

import java.util.List;

@Repository
public interface ObjetoSeguradoEndossoRepository extends JpaRepository<ObjetoSeguradoEndosso, Integer> {

    List<ObjetoSeguradoEndosso> findAllByIdtCtlProcAndIdtEdsEndosso(Integer idtCtlProc, Long idtEdsEndosso);

}
