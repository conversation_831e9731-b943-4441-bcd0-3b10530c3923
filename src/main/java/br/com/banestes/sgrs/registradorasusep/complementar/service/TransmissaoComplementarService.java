package br.com.banestes.sgrs.registradorasusep.complementar.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.complementar.service.model.ComplementarService;
import br.com.banestes.sgrs.registradorasusep.model.complementar.Complementar;
import br.com.banestes.sgrs.registradorasusep.service.EnvioSusepService;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class TransmissaoComplementarService {

    @Value("${numeroErrosPlataforma}")
    private Integer numeroErrosPlataforma;
    @Value("${complemento:}")
    private String complemento;

    private final ComplementarService complementarService;
    private final ConstrutorComplementarService construtorComplementarService;
    private final EnvioSusepService envioSusepService;

    public TransmissaoComplementarService(ComplementarService complementarService,
                                          ConstrutorComplementarService construtorComplementarService,
                                          EnvioSusepService envioSusepService) {

        this.complementarService = complementarService;
        this.construtorComplementarService = construtorComplementarService;
        this.envioSusepService = envioSusepService;
    }

    public void transmitirComplementares(Integer idtCtlProc, Character idcOperacao) {
        log.info("Controle de Processamento: {}", idtCtlProc);
        if(idcOperacao.toString().equalsIgnoreCase("i")){
            log.info("Operação: INCLUSÃO");
            log.info("Leiaute Selecionado: Complementar");
        } else if (idcOperacao.toString().equalsIgnoreCase("a")) {
            log.info("Operação: ALTERACAO");
            log.info("Leiaute Selecionado: Complementar");
        }

        final List<Complementar> complemetares = complementarService.listarComplementarTransmissao(idtCtlProc);

        Integer errorCount = 0;

        log.info("Numero de complementares a serem processadas: {}", complemetares.size());
        log.info("Processando Complementares...");

        for (Complementar comp : complemetares) {
//            log.info("Complementar codigo: {}", comp.getIdtCmpAuto());
            if (!processaApolice(comp, idcOperacao)) {
                errorCount++;
                if (Objects.equals(errorCount, numeroErrosPlataforma)) {
                    log.info("Processamento interrompido na Complementar de numero {}", comp.getIdtCmpAuto());
                    return;
                }
            }
        }
        log.info("As Complemetares foram processadas com sucesso !");
    }

    private boolean processaApolice(Complementar complementar, Character idcOperacao) {
        return complementarService.atualizarStatusTransmissao(
            complementar,
            envioSusepService.transmitir(
                construtorComplementarService.construir(complementar),
                idcOperacao.equals('I') ? "complementar-auto" : "complementar-auto/" + complementar.getIdentificadorRegistro(),
                idcOperacao.equals('I') ? HttpMethod.POST : HttpMethod.PUT
            ),
            idcOperacao
        );
    }
}
