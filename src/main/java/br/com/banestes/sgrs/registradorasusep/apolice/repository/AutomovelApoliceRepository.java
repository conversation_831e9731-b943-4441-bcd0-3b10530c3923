package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.AutomovelApolice;

import java.util.List;

public interface AutomovelApoliceRepository extends JpaRepository<AutomovelApolice, Integer> {
    List<AutomovelApolice> findAllByIdtCtlProcAndIdtCtrApolice(Integer idtCtlProc, Long idtCtrApolice);
}
