package br.com.banestes.sgrs.registradorasusep.endosso.service;

import br.com.banestes.sgrs.registradorasusep.endosso.repository.ContratoColetivoEndossoRepository;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.dto.endosso.EndossoDto;
import br.com.banestes.sgrs.registradorasusep.endosso.mapper.EndossoMapper;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.ParcelaEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.AutomovelEndossoService;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.IntermediarioEndossoService;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.ParteEndossoService;
import br.com.banestes.sgrs.registradorasusep.model.endosso.AutomovelEndosso;
import br.com.banestes.sgrs.registradorasusep.model.endosso.Endosso;

import java.util.List;

@Service
public class ConstrutorEndossoService {

    @Value("${grupoRamo:}")
    private String grupoRamo;
    private final ConstrutorObjetoSeguradoCompletoEndossoService construtorObjetoSeguradoCompletoService;
    private final EndossoMapper endossoMapper;
    private final IntermediarioEndossoService intermediarioService;
    private final ParteEndossoService parteService;
    private final AutomovelEndossoService automovelEndossoService;
    private final ParcelaEndossoRepository parcelaEndossoRepository;
    private final ContratoColetivoEndossoRepository contratoColetivoEndossoRepository;

    public ConstrutorEndossoService(ConstrutorObjetoSeguradoCompletoEndossoService construtorObjetoSeguradoCompletoService,
                                    EndossoMapper endossoMapper,
                                    IntermediarioEndossoService intermediarioService,
                                    ParteEndossoService parteService,
                                    AutomovelEndossoService automovelEndossoService,
                                    ParcelaEndossoRepository parcelaEndossoRepository,
                                    ContratoColetivoEndossoRepository contratoColetivoEndossoRepository) {

        this.construtorObjetoSeguradoCompletoService = construtorObjetoSeguradoCompletoService;
        this.endossoMapper = endossoMapper;
        this.intermediarioService = intermediarioService;
        this.parteService = parteService;
        this.automovelEndossoService = automovelEndossoService;
        this.parcelaEndossoRepository = parcelaEndossoRepository;
        this.contratoColetivoEndossoRepository = contratoColetivoEndossoRepository;
    }

    public EndossoDto construir(Endosso endosso) {

        List<AutomovelEndosso> automoveis = null;

        if ("AUTO".equals(grupoRamo)) {
            automoveis = automovelEndossoService.findAllByIdtCtlProcAndIdtEdsEndosso(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso());
        }

        return endossoMapper.toDto(endosso,
                parteService.findAllByIdtCtlProcAndIdtCtrApolice(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()),
                intermediarioService.findAllByIdtCtlProcAndIdtEdsEndosso(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()),
                construtorObjetoSeguradoCompletoService.obterObjetosSeguradosCompletosEndosso(endosso),
                automoveis,
                parcelaEndossoRepository.findAllByIdtCtlProcAndIdtEdsEndosso(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()),
                contratoColetivoEndossoRepository.findAllByidtEdsEndossoAndIdtCtlProc(endosso.getIdtEdsEndosso(), endosso.getIdtCtlProc()));
    }

}
