package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.AdicionalRepository;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Adicional;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Cobertura;

import java.util.List;

@Service
public class AdicionalService {
    private final AdicionalRepository adicionalRepository;

    public AdicionalService(AdicionalRepository adicionalRepository) {
        this.adicionalRepository = adicionalRepository;
    }

    public List<Adicional> findAllByIdtCtlProcAndIdtCtrApoliceAndIdtCtrObjeto(Cobertura cobertura) {
        return adicionalRepository.findAllByIdtCtlProcAndIdtCtrObjeto(cobertura.getIdtCtlProc(),
                cobertura.getIdtCtrObjeto());
    }

}
