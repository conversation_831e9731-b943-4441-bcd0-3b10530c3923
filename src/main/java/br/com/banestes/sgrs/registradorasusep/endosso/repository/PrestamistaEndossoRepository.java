package br.com.banestes.sgrs.registradorasusep.endosso.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.endosso.PrestamistaEndosso;

import java.util.List;

@Repository
public interface PrestamistaEndossoRepository extends JpaRepository<PrestamistaEndosso, Integer> {

    List<PrestamistaEndosso> findAllByidtEdsRmoPessoaAndIdtCtlProc(Long idtEdsRmoPessoa, Integer idtCtlProc);

}
