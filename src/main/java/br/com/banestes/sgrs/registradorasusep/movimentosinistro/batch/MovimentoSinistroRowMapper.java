package br.com.banestes.sgrs.registradorasusep.movimentosinistro.batch;

import org.springframework.jdbc.core.RowMapper;

import br.com.banestes.sgrs.registradorasusep.helper.BatchHelper;

import java.sql.ResultSet;

public class MovimentoSinistroRowMapper implements RowMapper<Integer> {

    @Override
    public Integer mapRow(ResultSet rs, int rowNum) {
        return BatchHelper.extrairRetornoStoredProcedeure(rs);
    }

}
