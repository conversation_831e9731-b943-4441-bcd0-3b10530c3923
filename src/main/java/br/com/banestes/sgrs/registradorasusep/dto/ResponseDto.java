package br.com.banestes.sgrs.registradorasusep.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.List;

@Getter
@Setter
@ToString
public class ResponseDto {
    private String message;
    private Integer code;
    private String status;
    private String objectName;
    private List<ErroDto> errors;

    public ResponseDto() {

    }

    public ResponseDto(Integer code, String status, String message) {
        this.code = code;
        this.status = status;
        this.message = message;
    }
}
