package br.com.banestes.sgrs.registradorasusep.sinistro.batch;

import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SinistroStepConfig {
    private final StepBuilderFactory stepBuilderFactory;

    public SinistroStepConfig(StepBuilderFactory stepBuilderFactory) {
        this.stepBuilderFactory = stepBuilderFactory;
    }

    @Bean
    public Step sinistroStep(
            ItemReader<Integer> sinistroItemReader,
            @Qualifier("sinistroProcessor") ItemProcessor<Integer, Integer> sinistroItemProcessor,
            ItemWriter<Integer> sinistroItemWriter) {

        return stepBuilderFactory
                .get("sinistroStep")
                .<Integer, Integer>chunk(1)
                .reader(sinistroItemReader)
                .processor(sinistroItemProcessor)
                .writer(sinistroItemWriter)
                .build();
    }
}
