package br.com.banestes.sgrs.registradorasusep.endosso.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.endosso.repository.ObjetoSeguradoEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.model.endosso.ObjetoSeguradoEndosso;

import java.util.List;

@Service
public class ObjetoSeguradoEndossoService {
    private final ObjetoSeguradoEndossoRepository objetoSeguradoRepository;

    public ObjetoSeguradoEndossoService(ObjetoSeguradoEndossoRepository objetoSeguradoRepository) {
        this.objetoSeguradoRepository = objetoSeguradoRepository;
    }

    public List<ObjetoSeguradoEndosso> findAllByIdtCtlProcAndIdtEdsEndosso(Integer idtCtlProc, Long idtEdsEndosso) {
        return objetoSeguradoRepository.findAllByIdtCtlProcAndIdtEdsEndosso(idtCtlProc, idtEdsEndosso);
    }

}
