package br.com.banestes.sgrs.registradorasusep.exclusao.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;

import java.util.List;

@Repository
public interface ExclusaoLayoutRepository extends JpaRepository<ExclusaoLayout, Integer> {

    @Query("SELECT a"
            + " FROM ExclusaoLayout a"
            + " INNER JOIN ExclusaoCadastro c"
            + " ON a.idtExcCadastro = c.idtExcCadastro"
            + " WHERE (c.idtExcCadastro = :idtExcCadastro)"
            //+ " AND (a.idcSitProc = 'S' OR a.idcSitProc = 'P')"
            + " ORDER BY a.seqProcLeiaute")
    List<ExclusaoLayout> listarRegistrosExcluir(@Param("idtExcCadastro") Integer idtExcCadastro);

}
