package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.Franquia;

import java.util.List;

@Repository
public interface FranquiaRepository extends JpaRepository<Franquia, Integer> {

    List<Franquia> findAllByIdtCtlProcAndIdtCtrObjetoSeg(Integer idtCtlProc, Long idtCtrObjeto);

}
