package br.com.banestes.sgrs.registradorasusep.sinistro.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroCobertura;

import java.util.List;

@Repository
public interface SinistroCoberturaRepository extends JpaRepository<SinistroCobertura, Integer> {

    List<SinistroCobertura> getAllByIdtSntDocumento(Long getAllByIdtSntDocumento);
}
