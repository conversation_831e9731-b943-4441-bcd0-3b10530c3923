package br.com.banestes.sgrs.registradorasusep.complementar.mapper;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import br.com.banestes.sgrs.registradorasusep.dto.complementar.ComplementarCoberturaDto;
import br.com.banestes.sgrs.registradorasusep.dto.complementar.ComplementarDto;
import br.com.banestes.sgrs.registradorasusep.dto.complementar.ComplementarPessoaDto;
import br.com.banestes.sgrs.registradorasusep.model.complementar.Complementar;
import br.com.banestes.sgrs.registradorasusep.model.complementar.ComplementarCobertura;
import br.com.banestes.sgrs.registradorasusep.model.complementar.ComplementarPessoa;

import java.util.ArrayList;
import java.util.List;

@Component
public class ComplementarMapper {

    public ComplementarDto toDto(Complementar complementar, List<ComplementarCobertura> coberturas, List<ComplementarPessoa> pessoasAssociadas) {
        final ComplementarDto complementarDto = new ComplementarDto();
        complementarDto.setIdentificadorRegistro(complementar.getIdentificadorRegistro());
        complementarDto.setApoliceCodigo(complementar.getApoliceCodigo());
        complementarDto.setEndossoCodigo(complementar.getEndossoCodigo());
        complementarDto.setObjetoSeguradoCodigo(complementar.getObjetoSeguradoCodigo());
        complementarDto.setTipo(complementar.getTipo());
        complementarDto.setDescricaoTipo(complementar.getDescricaoTipo());
        complementarDto.setDescricaoObjeto(complementar.getDescricaoObjeto());
        complementarDto.setIdentificacaoExataVeiculo(complementar.getIdentificacaoExataVeiculo());
        complementarDto.setModalidadeCasco(complementar.getModalidadeCasco());
        complementarDto.setPercentualTabelaReferencia(complementar.getPercentualTabelaReferencia());
        complementarDto.setTabelaValorMedio(complementar.getTabelaValorMedio());
        complementarDto.setCodigoModelo(complementar.getCodigoModelo());
        complementarDto.setAnoModelo(complementar.getAnoModelo());
        complementarDto.setCategoriaTarifaria(complementar.getCategoriaTarifaria());
        complementarDto.setCepRisco(complementar.getCepRisco());
        complementarDto.setCodigoUtilizacao(complementar.getCodigoUtilizacao());
        complementarDto.setCepLocalidadeDestino(complementar.getCepLocalidadeDestino());
        complementarDto.setCepLocalidadePernoite(complementar.getCepLocalidadePernoite());
        complementarDto.setPercentualDescontoBonus(complementar.getPercentualDescontoBonus());
        complementarDto.setClasseBonus(complementar.getClasseBonus());

        complementarDto.setCoberturas(obterCoberturas(coberturas));

        if (CollectionUtils.isNotEmpty(pessoasAssociadas)) {
            complementarDto.setPessoasAssociadas(obterComplementarPessoa(pessoasAssociadas));
        }

        complementarDto.setCodigoSeguradora(complementar.getCodigoSeguradora());
        complementarDto.setCertificadoCodigo(complementar.getCertificadoCodigo());

        return complementarDto;
    }

    private List<ComplementarCoberturaDto> obterCoberturas(List<ComplementarCobertura> coberturas) {
        final List<ComplementarCoberturaDto> coberturasDto = new ArrayList<>(coberturas.size());

        for (ComplementarCobertura cobertura : coberturas) {
            ComplementarCoberturaDto dto = new ComplementarCoberturaDto();
            dto.setGrupo(cobertura.getGrupo());
            dto.setRamo(cobertura.getRamo());
            dto.setCodigo(cobertura.getCodigo());
            dto.setOutrasDescricao(cobertura.getOutrasDescricao());
            dto.setCoberturaInternaSeguradora(cobertura.getCoberturaInternaSeguradora());
            dto.setNumeroProcesso(cobertura.getNumeroProcesso());
            dto.setLimiteMaximoIndenizacao(cobertura.getLimiteMaximoIndenizacao());
            dto.setLimiteMaximoIndenizacaoReal(cobertura.getLimiteMaximoIndenizacaoReal());
            dto.setDataInicio(cobertura.getDataInicio());
            dto.setDataTermino(cobertura.getDataTermino());
            dto.setIndiceAtualizacao(cobertura.getIndiceAtualizacao());
            dto.setPeriodicidadeAtualizacao(cobertura.getPeriodicidadeAtualizacao());
            dto.setPeriodicidadeUnidade(cobertura.getPeriodicidadeUnidade());
            dto.setCoberturaPrincipal(cobertura.getCoberturaPrincipal());
            dto.setCoberturaCaracteristica(cobertura.getCoberturaCaracteristica());
            dto.setCoberturaTipo(cobertura.getCoberturaTipo());
            dto.setCarenciaPeriodo(cobertura.getCarenciaPeriodo());
            dto.setCarenciaPeriodicidade(cobertura.getCarenciaPeriocidade());
            dto.setCarenciaPeriodicidadeDias(cobertura.getCarenciaPeriocidadeDias());
            dto.setCarenciaDataInicio(cobertura.getCarenciaDataInicio());
            dto.setCarenciaDataTermino(cobertura.getCarenciaDataTermino());
            dto.setValorPremio(cobertura.getValorPremio());
            dto.setValorPremioReal(cobertura.getValorPremioReal());
            dto.setIof(cobertura.getIof());
            dto.setCusto(cobertura.getCusto());
            dto.setCustoReal(cobertura.getCustoReal());
            dto.setTipoIndenizacao(cobertura.getTipoIndenizacao());
            dto.setPercentualIndenizacaoParcial(cobertura.getPercentualIndenizacaoParcial());
            dto.setPercentualLmi(cobertura.getPercentualLmi());
            dto.setDiasCobertura(cobertura.getDiasCobertura());
            dto.setCoberturaVinculada(cobertura.getCoberturaVinculada());
            dto.setFranquias(null);
            dto.setPos(null);
            dto.setPeriodicidadePremio(cobertura.getPeriodicidadePremio());

            coberturasDto.add(dto);
        }

        return coberturasDto;
    }

    private List<ComplementarPessoaDto> obterComplementarPessoa(List<ComplementarPessoa> pessoasAssociadas) {
        final List<ComplementarPessoaDto> complementarPessoasDto = new ArrayList<>(pessoasAssociadas.size());

        for (ComplementarPessoa complementar : pessoasAssociadas) {
            ComplementarPessoaDto dto = new ComplementarPessoaDto();
            dto.setDocumento(complementar.getDocumento());
            dto.setSexoCondutor(complementar.getSexoCondutor());
            dto.setDataNascimento(complementar.getDataNascimento());
            dto.setTempoHabilitacao(complementar.getTempoHabilitacao());

            complementarPessoasDto.add(dto);
        }

        return complementarPessoasDto;
    }

}
