package br.com.banestes.sgrs.registradorasusep.model.apolice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.math.BigInteger;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "SRO_CTR_PARCELA")
public class ParcelaApolice {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTR_PARCELA")
    private Long idtCtrParcela;
    @Column(name = "NUM_PARCELA")
    private Integer numParcela;
    @Column(name = "IDT_CTR_APOLICE")
    private Long idtCtrApolice;
    @Column(name = "PARCELA_NUMERO")
    private Integer parcelaNumero;
    @Column(name = "COD_TIP_MOV_PREMIO")
    private String codTipMovPremio;
    @Column(name = "VALOR")
    private BigDecimal valor;
    @Column(name = "MOEDA_PARCELA")
    private String moedaParcela;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "DATA_VENCIMENTO")
    private String dataVencimento;
    @Column(name = "VALOR_REAL")
    private BigDecimal valorReal;


}
