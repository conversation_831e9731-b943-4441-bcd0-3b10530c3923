package br.com.banestes.sgrs.registradorasusep.model.endosso;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.math.BigInteger;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "SRO_EDS_PARCELA")
public class ParcelaEndosso {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_EDS_PARCELA")
    private Long idtEdsParcela;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_EDS_ENDOSSO")
    private Long idtEdsEndosso;
    @Column(name = "MOEDA_PARCELA")
    private String moedaParcela;
    @Column(name = "NUM_PARCELA")
    private Integer numParcela;
    @Column(name = "PARCELA_NUMERO")
    private Integer parcelaNumero;
    @Column(name = "DATA_VENCIMENTO")
    private String dataVencimento;
    @Column(name = "COD_TIP_MOV_PREMIO")
    private String codTipMovPremio;
    @Column(name = "VALOR_REAL")
    private BigDecimal valorReal;
    @Column(name = "VALOR")
    private BigDecimal valor;
}
