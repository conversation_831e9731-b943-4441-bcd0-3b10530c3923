package br.com.banestes.sgrs.registradorasusep.movimentosinistro.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistroAdicional;

import java.util.List;

@Repository
public interface MovimentoSinistroAdicionalRepository extends JpaRepository<MovimentoSinistroAdicional, Integer> {


    List<MovimentoSinistroAdicional> getAllByIdtCtlProcAndIdtSntMovSinistro(Integer idtCtlProc,
                                                                            Long idtSntMovSinistro);

}
