package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.Adicional;

import java.util.List;

@Repository
public interface AdicionalRepository extends JpaRepository<Adicional, Integer> {

    List<Adicional> findAllByIdtCtlProcAndIdtCtrObjeto(Integer idtCtlProc, Long idtCtrObjeto);

}
