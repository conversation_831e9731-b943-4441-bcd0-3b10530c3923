package br.com.banestes.sgrs.registradorasusep.dto.endosso;


import br.com.banestes.sgrs.registradorasusep.dto.apolice.EnderecoDto;
import br.com.banestes.sgrs.registradorasusep.model.endosso.ParteEndosso;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ParteDto {
    public ParteDto(ParteEndosso parte) {
        this.documento = parte.getDocumento();
        this.endereco = new EnderecoDto(parte.getEndereco(), parte.getNumero(),
                parte.getComplemento(), parte.getBairro(), parte.getCidade(),
                parte.getUf(), parte.getPais(), parte.getCep());
        this.nome = parte.getNome();
        this.tipoDocumento = parte.getTipoDocumento();
        this.tipoParte = parte.getTipoParte();
        this.email = parte.getEmail();
        this.sexoSeguradoParticipante = parte.getSexoSeguradoParticipante();
        this.dataNascimento = parte.getDataNascimento();
    }

    private String tipoParte;
    private String documento;
    private String tipoDocumento;
    private String nome;
    private String email;
    private EnderecoDto endereco;
    private String sexoSeguradoParticipante;
    private String dataNascimento;

}
