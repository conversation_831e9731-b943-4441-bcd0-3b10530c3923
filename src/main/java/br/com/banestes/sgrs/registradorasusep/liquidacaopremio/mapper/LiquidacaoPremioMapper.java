package br.com.banestes.sgrs.registradorasusep.liquidacaopremio.mapper;

import org.springframework.stereotype.Component;

import br.com.banestes.sgrs.registradorasusep.dto.premio.LiquidacaoPremioDto;
import br.com.banestes.sgrs.registradorasusep.dto.premio.ValorEstipulanteSeguradoDto;
import br.com.banestes.sgrs.registradorasusep.model.premio.Premio;

@Component
public class LiquidacaoPremioMapper {

    public LiquidacaoPremioDto toDto(Premio premio) {
        final LiquidacaoPremioDto liquidacaoPremioDto = new LiquidacaoPremioDto();

		liquidacaoPremioDto.setIdentificadorRegistro(premio.getIdentificadorRegistro());
		liquidacaoPremioDto.setCodigoSeguradora(premio.getCodigoSeguradora());
		liquidacaoPremioDto.setApoliceCodigo(premio.getApoliceCodigo());
		liquidacaoPremioDto.setCertificadoCodigo(premio.getCertificadoCodigo());
		liquidacaoPremioDto.setEndossoCodigo(premio.getEndossoCodigo());
		liquidacaoPremioDto.setIdentificadorMovimento(premio.getIdentificadorMovimento());
		liquidacaoPremioDto.setMoeda(premio.getMoeda());
		liquidacaoPremioDto.setValorMovimento(premio.getValorMovimento());
		liquidacaoPremioDto.setValorMovimentoReal(premio.getValorMovimentoReal());
		liquidacaoPremioDto.setDataMovimento(premio.getDataMovimento());
		liquidacaoPremioDto.setNumeroParcelaMovimento(premio.getNumeroParcelaMovimento());
		liquidacaoPremioDto.setDataVencimento(premio.getDataVencimento());
		liquidacaoPremioDto.setTipoMovimento(premio.getTipoMovimento());
		liquidacaoPremioDto.setEstipulantesPagador(premio.getEstipulantesPagador());
		liquidacaoPremioDto.setPeriodoPagamento(premio.getPeriodoPagamento());
		liquidacaoPremioDto.setOrigem(premio.getOrigem());
		liquidacaoPremioDto.setDocumentoPagador(premio.getDocumentoPagador());
		liquidacaoPremioDto.setTipoDocumentoPagador(premio.getTipoDocumentoPagador());
		liquidacaoPremioDto.setNomePagador(premio.getNomePagador());
		liquidacaoPremioDto.setCodigoInstituicao(premio.getCodigoInstituicao());
		liquidacaoPremioDto.setMeioPagamento(premio.getMeioPagamento());

		liquidacaoPremioDto.setValorEstipulanteSegurado(madeValorEstipulanteSeguradoDto(premio));

		return liquidacaoPremioDto;
	}

	private ValorEstipulanteSeguradoDto madeValorEstipulanteSeguradoDto(Premio premio) {
		return new ValorEstipulanteSeguradoDto(premio.getValorSegurado(), premio.getValorEstipulante());
	}


}
