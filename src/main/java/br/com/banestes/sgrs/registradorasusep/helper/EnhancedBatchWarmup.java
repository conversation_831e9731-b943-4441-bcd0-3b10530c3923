package br.com.banestes.sgrs.registradorasusep.helper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Configuration
public class EnhancedBatchWarmup {
    
    private final JobBuilderFactory jobBuilderFactory;
    private final StepBuilderFactory stepBuilderFactory;
    private final RestTemplate restTemplate;
    
    @Value("${warmup.maxAttempts:5}")
    private Integer warmupMaxAttempts;
    @Value("${warmup.delaySeconds:3}")
    private Integer warmupDelaySeconds;
    @Value("${warmup.timeoutSeconds:10}")
    private Integer warmupTimeoutSeconds;
    @Value("${api.baseurl:http://localhost:8081}")
    private String apiBaseUrl;

    public EnhancedBatchWarmup(
            JobBuilderFactory jobBuilderFactory,
            StepBuilderFactory stepBuilderFactory,
            RestTemplate restTemplate
    ) {
        this.jobBuilderFactory = jobBuilderFactory;
        this.stepBuilderFactory = stepBuilderFactory;
        this.restTemplate = restTemplate;
    }

    @Bean
    @Primary
    public Tasklet enhancedPlatformWarmupTasklet() {
        return (contribution, chunkContext) -> {
            try {
                log.info("🔥 Iniciando warmup aprimorado da plataforma...");
                
                boolean warmupSuccess = performWarmupWithRetry();
                
                if (!warmupSuccess) {
                    String errorMsg = "Warmup falhou após " + warmupMaxAttempts + " tentativas";
                    log.error("🚨 {}", errorMsg);
                    
                    chunkContext.getStepContext()
                            .getStepExecution()
                            .getExecutionContext()
                            .put("warmupError", errorMsg);
                    
                    // Não falha o job, apenas registra o erro
                    log.warn("⚠️ Continuando execução mesmo com falha no warmup...");
                }
                
                return RepeatStatus.FINISHED;
                
            } catch (Exception e) {
                log.error("💥 Erro crítico no warmup: {}", e.getMessage());
                chunkContext.getStepContext()
                        .getStepExecution()
                        .getExecutionContext()
                        .put("warmupError", e.getMessage());
                
                // Não falha o job, apenas registra o erro
                log.warn("⚠️ Continuando execução mesmo com erro crítico no warmup...");
                return RepeatStatus.FINISHED;
            }
        };
    }

    private boolean performWarmupWithRetry() {
        for (int attempt = 1; attempt <= warmupMaxAttempts; attempt++) {
            try {
                log.info("🔄 Tentativa de warmup {} de {}", attempt, warmupMaxAttempts);
                
                if (attempt > 1) {
                    Thread.sleep(warmupDelaySeconds * 1000L);
                }
                
                // Múltiplas verificações de saúde
                boolean healthCheck1 = sendWarmupRequest("/health", attempt);
                Thread.sleep(1000);
                
                boolean healthCheck2 = sendWarmupRequest("/actuator/health", attempt);
                Thread.sleep(1000);
                
                boolean healthCheck3 = sendWarmupRequest("/health", attempt);
                
                if (healthCheck1 || healthCheck2 || healthCheck3) {
                    log.info("✅ Warmup bem-sucedido na tentativa {}", attempt);
                    
                    // Aguarda um pouco mais para garantir que a aplicação está estável
                    Thread.sleep(2000);
                    return true;
                }
                
                log.warn("❌ Warmup falhou na tentativa {}", attempt);
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("🚨 Warmup interrompido: {}", e.getMessage());
                return false;
            } catch (Exception e) {
                log.error("💥 Erro na tentativa {} de warmup: {}", attempt, e.getMessage());
            }
        }
        
        return false;
    }

    private boolean sendWarmupRequest(String endpoint, int attempt) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> request = new HttpEntity<>(headers);

            String url = apiBaseUrl + endpoint;
            log.debug("🌐 Enviando request para: {}", url);

            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    request,
                    String.class
            );

            boolean isSuccess = response.getStatusCode().is2xxSuccessful();
            
            if (isSuccess) {
                log.info("✅ Health check realizado com sucesso: {} - Status: {}", 
                        endpoint, response.getStatusCode());
            } else {
                log.warn("⚠️ Health check com status não ideal: {} - Status: {}", 
                        endpoint, response.getStatusCode());
            }
            
            return isSuccess;
            
        } catch (Exception e) {
            log.warn("❌ Erro no warmup para {}: {}", endpoint, e.getMessage());
            return false;
        }
    }

    @Bean
    @Primary
    public Step enhancedWarmupBatchStep() {
        return stepBuilderFactory.get("enhancedWarmupBatchStep")
                .tasklet(enhancedPlatformWarmupTasklet())
                .build();
    }
}
