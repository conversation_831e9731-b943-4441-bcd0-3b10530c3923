package br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.cobertura;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CoberturaDto {
    private String codigo;
    private String grupo;
    private String ramo;
    private String outrasDescricao;
    private String coberturaInternaSeguradora;
    private String numeroProcesso;
    private Double limiteMaximoIndenizacao;
    private Double limiteMaximoIndenizacaoReal;
    private String dataInicioCobertura;
    private String dataTerminoCobertura;
    private String indiceAtualizacao;
    private Integer periodicidadeAtualizacao;
    private String periodicidadeUnidade;
    private String periodicidadePremio;
    private String descricaoPeriodicidade;
    private String coberturaPrincipal;
    private String coberturaCaracteristica;
    private String tipoRisco;
    private String coberturaTipo;
    private Integer carenciaPeriodo;
    private String carenciaPeriodicidade;
    private String carenciaPeriodicidadeDias;
    private String carenciaDataInicio;
    private String carenciaDataTermino;
    private String dataInicioPremio;
    private String dataTerminoPremio;
    private Double valorPremio;
    private Double valorPremioReal;
    private Double iof;
    private Double custo;
    private Double custoReal;
    @JsonUnwrapped
    private CoberturaListasDto coberturaListas;
    private String limiteMaximoIndenizacaoSublimite;

}
