package br.com.banestes.sgrs.registradorasusep.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
@Slf4j
public class JobStarter {

    @Value("${layout:}")
    private String layout;
    @Value("${grupoRamo:}")
    private String grupoRamo;
    @Value("${complemento:}")
    private String complemento;
    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;
    private final JobLauncher jobLauncher;
    private final ApplicationContext context;

    public JobStarter(JobLauncher jobLauncher, ApplicationContext context) {
        this.jobLauncher = jobLauncher;
        this.context = context;
    }

    @Bean
    public ExitStatus jobSelector() {
        try {
            if (modoSimulacao) log.info("Modo simulação ativado");
            log.info("Iniciado layout {}, grupoRamo {} e complemento {}", layout, grupoRamo, complemento);

            final List<String> rotinasValidas = new ArrayList<>(Arrays.asList(
                    "apolice",
                    "complementar",
                    "endosso",
                    "liquidacaoPremio",
                    "sinistro",
                    "movimentoSinistro",
                    "exclusao",
                    "liberarControle",
                    "controlarGatilho"));

            final String rotina = rotinasValidas.stream().filter(p -> p.equalsIgnoreCase(layout)).findFirst().orElse(null);

            if (rotina == null) {
                return ExitStatus.FAILED;
            }

            final Job job = this.context.getBean(rotina.concat("Job"), Job.class);

            final JobExecution run = this.jobLauncher.run(job, new JobParameters());

            //Ajuste para forcar o exit code 1 e evitar do opcon colocar o job como Sucesso
            if (run.getExitStatus().getExitCode().equals("FAILED")) {

                String rootCauseMessage = ExceptionUtils.getRootCauseMessage(run.getAllFailureExceptions().get(0));
                rootCauseMessage = rootCauseMessage.substring(rootCauseMessage.indexOf(':') + 1);

                log.error("Ocorreu um erro durante o processamento => {}", rootCauseMessage);

            }
            return run.getExitStatus();

        } catch (JobExecutionException e) {
            log.error("Ocorreu um erro na execução do Job => ", e);
            return ExitStatus.FAILED;
        }
    }
}

