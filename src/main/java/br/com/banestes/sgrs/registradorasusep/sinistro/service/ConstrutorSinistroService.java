package br.com.banestes.sgrs.registradorasusep.sinistro.service;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.dto.sinistro.CoberturaAfetadaDto;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.DocumentosAfetadosDto;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.SinistroDto;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.Sinistro;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroCobertura;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroDocumento;
import br.com.banestes.sgrs.registradorasusep.sinistro.mapper.SinistroMapper;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroAutoRepository;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroBeneficiarioRepository;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroDocumentoRepository;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroCoberturaService;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroEventoService;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroPessoaService;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroVistoriaService;

import java.util.ArrayList;
import java.util.List;

@Service
public class ConstrutorSinistroService {
    private final SinistroMapper sinistroMapper;
    private final SinistroCoberturaService sinistroCoberturaService;
    private final SinistroPessoaService sinistroPessoaService;
    private final SinistroEventoService sinistroEventoService;
    private final SinistroVistoriaService sinistroVistoriaService;
    private final SinistroDocumentoRepository sinistroDocumentoRepository;
    private final SinistroAutoRepository sinistroAutoRepository;
    private final SinistroBeneficiarioRepository sinistroBeneficiarioRepository;


    public ConstrutorSinistroService(SinistroMapper sinistroMapper,
                                     SinistroCoberturaService sinistroCoberturaService,
                                     SinistroPessoaService sinistroPessoaService,
                                     SinistroEventoService sinistroEventoService,
                                     SinistroVistoriaService sinistroVistoriaService,
                                     SinistroDocumentoRepository sinistroDocumentoRepository,
                                     SinistroAutoRepository sinistroAutoRepository,
                                     SinistroBeneficiarioRepository sinistroBeneficiarioRepository) {

        this.sinistroMapper = sinistroMapper;
        this.sinistroCoberturaService = sinistroCoberturaService;
        this.sinistroPessoaService = sinistroPessoaService;
        this.sinistroEventoService = sinistroEventoService;
        this.sinistroVistoriaService = sinistroVistoriaService;
        this.sinistroDocumentoRepository = sinistroDocumentoRepository;
        this.sinistroAutoRepository = sinistroAutoRepository;
        this.sinistroBeneficiarioRepository = sinistroBeneficiarioRepository;
    }

    public SinistroDto construir(Sinistro sinistro) {

        List<SinistroDocumento> documentos = sinistroDocumentoRepository.getAllByIdtCtlProcAndIdtSntSinistro(sinistro.getIdtCtlProc(),
                sinistro.getIdtSntSinistro());

        List<DocumentosAfetadosDto> documentosAfetadosDtos = new ArrayList<>(documentos.size());

        documentos.forEach(p -> {
            List<CoberturaAfetadaDto> coberturaAfetadaDtos = obterListaCoberturasAfetadas(sinistroCoberturaService.getAllByIdtSntDocumento(p.getIdtSntDocumento()));
            documentosAfetadosDtos.add(new DocumentosAfetadosDto(p.getApoliceCodigo(), p.getCertificadoCodigo(), p.getNumeroEndosso(), coberturaAfetadaDtos));
        });


        return sinistroMapper.toDto(
                sinistro,
                sinistroPessoaService.getAllByIdtCtlProcAndIdtSntSinistro(sinistro.getIdtCtlProc(), sinistro.getIdtSntSinistro()),
                sinistroEventoService.getAllByIdtCtlProcAndIdtSntSinistro(sinistro.getIdtCtlProc(), sinistro.getIdtSntSinistro()),
                sinistroVistoriaService.getAllByIdtCtlProcAndIdtSntSinistro(sinistro.getIdtCtlProc(), sinistro.getIdtSntSinistro()),
                documentosAfetadosDtos,
                sinistroAutoRepository.getAllByIdtCtlProcAndIdtSntSinistro(sinistro.getIdtCtlProc(), sinistro.getIdtSntSinistro()),
                sinistroBeneficiarioRepository.getAllByIdtCtlProcAndIdtSntSinistro(sinistro.getIdtCtlProc(), sinistro.getIdtSntSinistro())
        );
    }

    private List<CoberturaAfetadaDto> obterListaCoberturasAfetadas(List<SinistroCobertura> listaSinistroCobertura) {

        final List<CoberturaAfetadaDto> coberturasAfetadasDto = new ArrayList<>(listaSinistroCobertura.size());

        for (SinistroCobertura sinistroCobertura : listaSinistroCobertura) {
            coberturasAfetadasDto.add(new CoberturaAfetadaDto(sinistroCobertura));
        }

        return coberturasAfetadasDto;
    }

}
