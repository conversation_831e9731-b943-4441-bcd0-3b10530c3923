package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.PrestamistaApolice;

import java.util.List;

@Repository
public interface PrestamistaApoliceRepository extends JpaRepository<PrestamistaApolice, Integer> {

    List<PrestamistaApolice> findAllByidtCtrRmoPessoaAndIdtCtlProc(Long idtCtrRmoPessoa, Integer idtCtlProc);

}

