package br.com.banestes.sgrs.registradorasusep.model.endosso;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@ToString
@Table(name = "SRO_EDS_PCT_PRESTAMISTA")
public class PercentualPrestamistaEndosso
{
    @Id
    @Column(name = "IDT_EDS_PCT_PRESTAMISTA")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idtEdsPctPrestamista;

    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;

    @Column(name = "IDT_EDS_PRESTAMISTA", insertable = false, updatable = false)
    private Long idtEdsPrestamista;

    @Column(name = "DOCUMENTO")
    private String documento;

    @Column(name = "PERCENTUAL_CAPITAL_SEGURADO")
    private Double percentualCapitalSegurado;
}
