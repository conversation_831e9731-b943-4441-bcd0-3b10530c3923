package br.com.banestes.sgrs.registradorasusep.endosso.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.endosso.RamoPessoasEndosso;

import java.util.List;

@Repository
public interface RamoPessoasEndossoRepository extends JpaRepository<RamoPessoasEndosso, Integer> {

    List<RamoPessoasEndosso> findAllByIdtEdsObjetoAndIdtCtlProc(Long idtEdsObjeto, Integer idtCtlProc);

}
