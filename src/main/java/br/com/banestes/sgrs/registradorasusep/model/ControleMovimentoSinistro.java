package br.com.banestes.sgrs.registradorasusep.model;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;
import java.sql.Timestamp;

@Getter
@Setter
@Entity
@Table(name = "SRO_CTL_MOV_SINISTRO")
public class ControleMovimentoSinistro {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTL_MOV_SINISTRO")
    private Long idtCtlMovSinistro;
    @Column(name = "COD_EMISSOR")
    private Integer codEmissor;
    @Column(name = "COD_ALS")
    private Integer codAls;
    @Column(name = "COD_EMPRESA")
    private Integer codEmpresa;
    @Column(name = "COD_SEGURADORA")
    private Integer codSeguradora;
    @Column(name = "NUM_SEQ_PCS")
    private Integer numSeqPcs;
    @Column(name = "IDT_PCS_SINISTRO")
    private Integer idtPcsSinistro;
    @Column(name = "IDT_ALS")
    private Integer idtAls;
    @Column(name = "ANO_ALS")
    private Integer anoAls;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_CTL_MOV_SINISTRO_ATU")
    private Long idtCtlMovSinistroAtu;
    @Column(name = "IDC_SIT_PROC")
    private String idcSitProc;
    @Column(name = "DES_MSG_PROC")
    private String desMsgProc;
    @Column(name = "ANO_PCS")
    private Integer anoPcs;
    @Column(name = "DAT_HOR_ULT_ATUALIZACAO")
    private Timestamp datHorUltAtualizacao;
    @Column(name = "NUM_MATR_ULT_ATUALIZACAO")
    private BigInteger numMatrUltAtualizacao;
    @Column(name = "DAT_HOR_REGISTRO")
    private Timestamp datHorRegistro;
}
