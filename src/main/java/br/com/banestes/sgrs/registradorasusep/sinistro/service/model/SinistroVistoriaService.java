package br.com.banestes.sgrs.registradorasusep.sinistro.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroVistoria;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroVistoriaRepository;

import java.util.List;

@Service
public class SinistroVistoriaService {
    private final SinistroVistoriaRepository sinistroVistoriaRepository;

    public SinistroVistoriaService(SinistroVistoriaRepository sinistroVistoriaRepository) {
        this.sinistroVistoriaRepository = sinistroVistoriaRepository;
    }

    public List<SinistroVistoria> getAllByIdtCtlProcAndIdtSntSinistro(Integer idtCtlProc, Long idtSntSinistro) {
        return sinistroVistoriaRepository.getAllByIdtCtlProcAndIdtSntSinistro(idtCtlProc, idtSntSinistro);
    }

}
