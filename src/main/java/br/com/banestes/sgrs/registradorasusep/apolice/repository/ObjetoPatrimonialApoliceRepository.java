package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.ObjetoPatrimonialApolice;

import java.util.List;

public interface ObjetoPatrimonialApoliceRepository extends JpaRepository<ObjetoPatrimonialApolice, Integer> {

    List<ObjetoPatrimonialApolice> findAllByIdtCtrObjetoAndIdtCtlProc(long idtCtrObjeto, Integer idtCtlProc);

}
