package br.com.banestes.sgrs.registradorasusep.complementar.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.complementar.repository.CoberturaAutoRepository;
import br.com.banestes.sgrs.registradorasusep.model.complementar.ComplementarCobertura;

import java.util.List;

@Service
public class CoberturaAutoService {

    private final CoberturaAutoRepository coberturaAutoRepository;

    public CoberturaAutoService(CoberturaAutoRepository coberturaAutoRepository) {
        this.coberturaAutoRepository = coberturaAutoRepository;
    }

    public List<ComplementarCobertura> findAllByIdtCtlProcAndIdtCmpAuto(Integer idtCtlProc, Long idtCmpAuto) {
        return coberturaAutoRepository.findAllByIdtCtlProcAndIdtCmpAuto(idtCtlProc, idtCmpAuto);
    }

}
