package br.com.banestes.sgrs.registradorasusep.model.apolice;

import java.util.List;
import javax.persistence.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@ToString
@Table(name = "SRO_CTR_RMO_PESSOA")
public class RamoPessoasApolice
{
    @Id
    @Column(name = "IDT_CTR_RMO_PESSOA")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idtCtrRmoPessoa;

    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;

    @Column(name = "IDT_CTR_OBJETO")
    private Long idtCtrObjeto;

    @Column(name = "COD_RMO_COBERTURA")
    private Integer codigoRamo;

    @Column(name = "COD_MOD_COBERTURA")
    private Integer codigoModalidade;

    @Column(name = "COD_COBERTURA")
    private Integer codigoCobertura;

    @Column(name = "GRUPO_COBERTURA")
    private String grupoCobertura;

    @Column(name = "RAMO_COBERTURA")
    private String ramoCobertura;

    @Column(name = "COBERTURA_INTERNA_SEGURADORA")
    private String coberturaInterna;

    @Column(name = "INCLUSAO_DEPENDENTES")
    private String incluiDependentes;

    @Column(name = "ABRANGENCIA_VIAGEM")
    private String abrangeViagem;

    @Transient
    private List<PrestamistaApolice> prestamistas;

    @Transient
    private List<DependenteApolice> dependentes;
}
