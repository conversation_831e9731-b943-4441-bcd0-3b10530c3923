package br.com.banestes.sgrs.registradorasusep.model.apolice;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "SRO_CTR_COBERTURA_BENEFICIARIO")
public class Beneficiario {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTR_COBERTURA_BENEFICIARIO")
    private Integer idtCtrCoberturaBeneficiario;
    @Column(name = "IDT_CTR_OBJETO")
    private Long idtCtrObjeto;
    @Column(name = "IDT_CTR_APOLICE")
    private Integer idtCtrApolice;
    @Column(name = "codigo")
    private String codigo;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "outrasDescricao")
    private String outrasDescricao;
    @Column(name = "grupo")
    private String grupo;
    @Column(name = "ramo")
    private String ramo;
    @Column(name = "limiteMaximoIndenizacao")
    private Double limiteMaximoIndenizacao;
    @Column(name = "coberturaInternaSeguradora")
    private String coberturaInternaSeguradora;
    @Column(name = "numeroProcesso")
    private String numeroProcesso;
    @Column(name = "dataTermino")
    private String dataTermino;
    @Column(name = "limiteMaximoIndenizacaoReal")
    private Double limiteMaximoIndenizacaoReal;
    @Column(name = "dataInicio")
    private String dataInicio;
    @Column(name = "periodicidadeAtualizacao")
    private Integer periodicidadeAtualizacao;
    @Column(name = "indiceAtualizacao")
    private String indiceAtualizacao;
    @Column(name = "coberturaCaracteristica")
    private String coberturaCaracteristica;
    @Column(name = "periodicidadeUnidade")
    private String periodicidadeUnidade;
    @Column(name = "coberturaPrincipal")
    private String coberturaPrincipal;
    @Column(name = "carenciaPeriodo")
    private Integer carenciaPeriodo;
    @Column(name = "tipoRisco")
    private String tipoRisco;
    @Column(name = "coberturaTipos")
    private String coberturaTipos;
    @Column(name = "carenciaPeriodicidadeDias")
    private String carenciaPeriodicidadeDias;
    @Column(name = "carenciaPeriodicidade")
    private String carenciaPeriodicidade;
    @Column(name = "valorPremio")
    private Double valorPremio;
    @Column(name = "dataInicioPremio")
    private String dataInicioPremio;
    @Column(name = "carenciaDataInicio")
    private String carenciaDataInicio;
    @Column(name = "carenciaDataTermino")
    private String carenciaDataTermino;
    @Column(name = "custoReal")
    private Double custoReal;
    @Column(name = "dataTerminoPremio")
    private String dataTerminoPremio;
    @Column(name = "custo")
    private Double custo;
    @Column(name = "valorPremioReal")
    private Double valorPremioReal;
    @Column(name = "iof")
    private Double iof;
}
