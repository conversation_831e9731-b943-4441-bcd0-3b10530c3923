package br.com.banestes.sgrs.registradorasusep.service;

import br.com.banestes.sgrs.registradorasusep.exception.RotinaException;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.ControleEndosso;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.repository.ControleEndossoRepository;

import java.util.Optional;

@Service
public class ControleEndossoService {
    private final ControleEndossoRepository controleEndossoRepository;
    private final EndossoUpdateService endossoUpdateService;

    public ControleEndossoService(ControleEndossoRepository controleEndossoRepository,
                                  EndossoUpdateService endossoUpdateService) {

        this.controleEndossoRepository = controleEndossoRepository;
        this.endossoUpdateService = endossoUpdateService;
    }

    public void atualizar(Long idtEdsEndosso, String situacaoProcessamento, String mensagemErroProcessamento, Character idcOperacao) {
        final Optional<ControleEndosso> controleEndossoOpt = controleEndossoRepository.findById(idtEdsEndosso);

        if (controleEndossoOpt.isPresent()) {
            final ControleEndosso controleEndosso = controleEndossoOpt.get();

            endossoUpdateService.atualizarSituacao(
                controleEndosso.getIdtCtlEndosso(),
                controleEndosso.getIdtCtlProc(),
                situacaoProcessamento,
                mensagemErroProcessamento,
                idcOperacao
            );

            if (("R".equals(situacaoProcessamento) || "E".equals(situacaoProcessamento)) &&
                    (controleEndosso.getIdtCtlEndossoAtu() != null && controleEndosso.getIdtCtlEndossoAtu() > 0)) {
                endossoUpdateService.updateRegistroCorrecaoIngnorar(controleEndosso);
            }

            // Qualquer erro de plataforma (P) onde tipo contrato do controle do endosso = AC (coletivo), abortar rotina
            if ("P".equals(situacaoProcessamento) && "AC".equals(controleEndosso.getTipContrato())) {
                throw new RotinaException("Erro em apólice coletiva detectado. Abortando rotina");
            }
        }
    }

    public void atualizarRegistroExclusao(ExclusaoLayout exclusaoLayout, String situacaoProcessamento) {
        final ControleEndosso controleEndosso = controleEndossoRepository.findById(exclusaoLayout.getIdtLeiaute()).orElse(null);

        if (controleEndosso != null) {
            endossoUpdateService.updateExclusaoRegistro(controleEndosso.getIdtCtlEndosso(), exclusaoLayout.getNumMatrUltAtualizacao(), situacaoProcessamento);
        }
    }

}
