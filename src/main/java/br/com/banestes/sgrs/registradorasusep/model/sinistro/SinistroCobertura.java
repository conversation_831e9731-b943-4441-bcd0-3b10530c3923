package br.com.banestes.sgrs.registradorasusep.model.sinistro;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_SNT_COBERTURA")
public class SinistroCobertura {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_SNT_COBERTURA")
    private Long idtSntCobertura;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_SNT_DOCUMENTO")
    private Long idtSntDocumento;
    @Column(name = "COD_RAMO")
    private Integer codRamo;
    @Column(name = "COD_MODALIDADE")
    private Integer codModalidade;
    @Column(name = "COD_COBERTURA")
    private Integer codCobertura;
    @Column(name = "CODIGO_OBJETO")
    private String codigoObjeto;
    @Column(name = "GRUPO")
    private String grupo;
    @Column(name = "RAMO")
    private String ramo;
    @Column(name = "SINISTRO_COBERTURA_CODIGO")
    private String sinistroCoberturaCodigo;
    @Column(name = "SINISTRO_COBERTURA_OUTROS")
    private String sinistroCoberturaOutros;
    @Column(name = "DATA_AVISO_COBERTURA")
    private String dataAvisoCobertura;
    @Column(name = "DATA_REGISTRO_SEGURADORA_COBERTURA")
    private String dataRegistroSeguradoraCobertura;
    @Column(name = "DATA_RECLAMACAO_TERCEIRO_COBERTURA")
    private String dataReclamacaoTerceiroCobertura;
    @Column(name = "COBERTURA_INTERNA_SEGURADORA")
    private String coberturaInternaSeguradora;

}
