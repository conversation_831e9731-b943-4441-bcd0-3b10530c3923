package br.com.banestes.sgrs.registradorasusep.complementar.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.complementar.ComplementarCobertura;

import java.util.List;

@Repository
public interface CoberturaAutoRepository extends JpaRepository<ComplementarCobertura, Integer> {

    List<ComplementarCobertura> findAllByIdtCtlProcAndIdtCmpAuto(Integer idtCtlProc, Long idtCmpAuto);

}
