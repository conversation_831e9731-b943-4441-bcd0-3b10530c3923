package br.com.banestes.sgrs.registradorasusep.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;
import java.sql.Timestamp;

@Getter
@Setter
@ToString
@Entity
@Table(name = "SRO_CTL_APOLICE")
public class ControleApolice {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTL_APOLICE")
    private Long idtCtlApolice;
    @Column(name = "COD_EMPRESA")
    private Integer codEmpresa;
    @Column(name = "COD_MODALIDADE")
    private Integer codModalidade;
    @Column(name = "IDC_SIT_PROC")
    private String idcSitProc;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "NUM_CTR_SUSEP")
    private BigInteger numCtrSusep;
    @Column(name = "DES_MSG_PROC")
    private String desMsgProc;
    @Column(name = "COD_EMISSOR")
    private Integer codEmissor;
    @Column(name = "IDT_CTL_APOLICE_ATU")
    private long idtCtlApoliceAtu;
    @Column(name = "COD_RAMO")
    private Integer codRamo;
    @Column(name = "COD_SEGURADORA")
    private Integer codSeguradora;
    @Column(name = "NUM_MATR_ULT_ATUALIZACAO")
    private Integer numMatrUltAtualizacao;
    @Column(name = "NUM_CONTRATO")
    private BigInteger numContrato;
    @Column(name = "DAT_HOR_ULT_ATUALIZACAO")
    private Timestamp datHorUltAtualizacao;
    @Column(name = "DAT_HOR_REGISTRO")
    private Timestamp datHorRegistro;
    @Column(name = "TIP_CONTRATO")
    private String tipContrato;
}
