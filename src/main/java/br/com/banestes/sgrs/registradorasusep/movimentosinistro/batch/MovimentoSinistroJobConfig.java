package br.com.banestes.sgrs.registradorasusep.movimentosinistro.batch;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MovimentoSinistroJobConfig {

    private final JobBuilderFactory jobBuilderFactory;

    public MovimentoSinistroJobConfig(JobBuilderFactory jobBuilderFactory) {
        this.jobBuilderFactory = jobBuilderFactory;
    }

    @Bean
    public Job movimentoSinistroJob(Step movimentoSinistroStep) {

        return jobBuilderFactory
                .get("movimentoSinistroJob")
                .start(movimentoSinistroStep)
                .incrementer(new RunIdIncrementer())
                .build();
    }
}
