package br.com.banestes.sgrs.registradorasusep.liberarcontrole.batch;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LiberarControleJobConfig {

    private final JobBuilderFactory jobBuilderFactory;

    public LiberarControleJobConfig(JobBuilderFactory jobBuilderFactory) {
        this.jobBuilderFactory = jobBuilderFactory;
    }

    @Bean
    public Job liberarControleJob(Step liberarControleStep) {

        return jobBuilderFactory
                .get("liberarControleJob")
                .start(liberarControleStep)
                .incrementer(new RunIdIncrementer())
                .build();
    }
}
