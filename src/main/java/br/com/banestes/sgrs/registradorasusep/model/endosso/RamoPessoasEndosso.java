package br.com.banestes.sgrs.registradorasusep.model.endosso;

import java.util.List;
import javax.persistence.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@ToString
@Table(name = "SRO_EDS_RMO_PESSOA")
public class RamoPessoasEndosso
{
    @Id
    @Column(name = "IDT_EDS_RMO_PESSOA")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idtEdsRmoPessoa;

    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;

    @Column(name = "IDT_EDS_OBJETO")
    private Long idtEdsObjeto;

    @Column(name = "COD_RMO_COBERTURA")
    private Integer codigoRamo;

    @Column(name = "COD_MOD_COBERTURA")
    private Integer codigoModalidade;

    @Column(name = "COD_COBERTURA")
    private Integer codigoCobertura;

    @Column(name = "GRUPO_COBERTURA")
    private String grupoCobertura;

    @Column(name = "RAMO_COBERTURA")
    private String ramoCobertura;

    @Column(name = "COBERTURA_INTERNA_SEGURADORA")
    private String coberturaInterna;

    @Column(name = "INCLUSAO_DEPENDENTES")
    private String incluiDependentes;

    @Column(name = "ABRANGENCIA_VIAGEM")
    private String abrangeViagem;

    @Transient
    private List<PrestamistaEndosso> prestamistas;

    @Transient
    private List<DependenteEndosso> dependentes;
}
