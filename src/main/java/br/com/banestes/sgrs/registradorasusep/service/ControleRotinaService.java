package br.com.banestes.sgrs.registradorasusep.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.ControleRotina;
import br.com.banestes.sgrs.registradorasusep.repository.ControleRotinaRepository;

@Slf4j
@Service
public class ControleRotinaService {
    private final ControleRotinaRepository controleRotinaRepository;

    public ControleRotinaService(ControleRotinaRepository controleRotinaRepository) {
        this.controleRotinaRepository = controleRotinaRepository;
    }

    public ControleRotina findByIdtCtlRtn(Integer idtCtlRtn) {
        return controleRotinaRepository.findByIdtCtlRtn(idtCtlRtn);
    }

}
