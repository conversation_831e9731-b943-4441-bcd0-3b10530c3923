package br.com.banestes.sgrs.registradorasusep.sinistro.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroCobertura;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroCoberturaRepository;

import java.util.List;

@Service
public class SinistroCoberturaService {
    private final SinistroCoberturaRepository sinistroCoberturaRepository;

    public SinistroCoberturaService(SinistroCoberturaRepository sinistroCoberturaRepository) {
        this.sinistroCoberturaRepository = sinistroCoberturaRepository;
    }

    public List<SinistroCobertura> getAllByIdtSntDocumento(Long getAllByIdtSntDocumento) {
        return sinistroCoberturaRepository.getAllByIdtSntDocumento(getAllByIdtSntDocumento);
    }

}
