package br.com.banestes.sgrs.registradorasusep.model.movimentosinistro;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_SNT_MOV_SINISTRO")
public class MovimentoSinistro {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_SNT_MOV_SINISTRO")
    private Long idtSntMovSinistro;
    @Column(name = "IDT_PCS_SINISTRO")
    private Integer idtPcsSinistro;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDENTIFICADOR_MOVIMENTO")
    private String identificadorMovimento;
    @Column(name = "COD_EMPRESA")
    private Integer codEmpresa;
    @Column(name = "ANO_PCS")
    private Integer anoPcs;
    @Column(name = "IDENTIFICADOR_REGISTRO")
    private String identificadorRegistro;
    @Column(name = "NUM_SEQ_PCS")
    private Integer numSeqPcs;
    @Column(name = "ORIGEM")
    private String origem;
    @Column(name = "COD_ALS")
    private Integer codAls;
    @Column(name = "COD_EMISSOR")
    private Integer codEmissor;
    @Column(name = "IDT_ALS")
    private Integer idtAls;
    @Column(name = "CODIGO_SEGURADORA")
    private String codigoSeguradora;
    @Column(name = "CODIGO_SINISTRO")
    private String codigoSinistro;
    @Column(name = "DATA_MOVIMENTO")
    private String dataMovimento;
    @Column(name = "COD_SEGURADORA")
    private Integer codSeguradora;
    @Column(name = "TIPO_MOVIMENTO")
    private String tipoMovimento;
    @Column(name = "TIPO_OPERACAO_SINISTRO")
    private String tipoOperacaoSinistro;
    @Column(name = "TIPO_SINISTRO")
    private String tipoSinistro;
    @Column(name = "CODIGO_CONTRAPARTE")
    private String codigoContraparte;
    @Column(name = "DOCUMENTO")
    private String documento;
    @Column(name = "ANO_ALS")
    private Integer anoAls;
    @Column(name = "TIPO_DOCUMENTO")
    private String tipoDocumento;
    @Column(name = "RAZAO_SOCIAL")
    private String razaoSocial;
    @Column(name = "CODIGO_INSTITUICAO")
    private String codigoInstituicao;
    @Column(name = "MEIO_PAGAMENTO")
    private String meioPagamento;
    @Column(name = "TIPO_PAGAMENTO")
    private String tipoPagamento;
    @Column(name = "SINISTRO_TIPO_PAGAMENTO_OUTROS")
    private String sinistroTipoPagamentoOutros;
    @Column(name = "MOEDA")
    private String moeda;
    @Column(name = "VALOR_MOVIMENTO")
    private Double valorMovimento;
    @Column(name = "VALOR_MOVIMENTO_REAL")
    private Double valorMovimentoReal;
    @Column(name = "GRUPO")
    private String grupo;
    @Column(name = "RAMO")
    private String ramo;
    @Column(name = "APOLICE_CODIGO")
    private String apoliceCodigo;
    @Column(name = "CERTIFICADO_CODIGO")
    private String certificadoCodigo;
    @Column(name = "NUMERO_ENDOSSO")
    private String numeroEndosso;
    @Column(name = "CODIGO_SINDICATO")
    private String codigoSindicato;
    @Column(name = "CODIGO_CONTRATO")
    private String codigoContrato;
    @Column(name = "FAIXA_CONTRATO")
    private Integer faixaContrato;
    @Column(name = "NUM_ENDOSSO_RESSEGURO")
    private String numEndossoResseguro;
}
