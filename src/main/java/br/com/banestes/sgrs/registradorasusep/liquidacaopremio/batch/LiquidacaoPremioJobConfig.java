package br.com.banestes.sgrs.registradorasusep.liquidacaopremio.batch;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LiquidacaoPremioJobConfig {

    private final JobBuilderFactory jobBuilderFactory;

    public LiquidacaoPremioJobConfig(JobBuilderFactory jobBuilderFactory) {
        this.jobBuilderFactory = jobBuilderFactory;
    }

    @Bean
    public Job liquidacaoPremioJob(Step liquidacaoPremioStep) {

        return jobBuilderFactory
                .get("liquidacaoPremioJob")
                .start(liquidacaoPremioStep)
                .incrementer(new RunIdIncrementer())
                .build();
    }
}
