package br.com.banestes.sgrs.registradorasusep.complementar.batch;

import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ComplementarStepConfig {

    private final StepBuilderFactory stepBuilderFactory;

    public ComplementarStepConfig(StepBuilderFactory stepBuilderFactory) {
        this.stepBuilderFactory = stepBuilderFactory;
    }

    @Bean
    public Step complementarStep(
            ItemReader<Integer> complementarItemReader,
            @Qualifier("complementarProcessor") ItemProcessor<Integer, Integer> complementarItemProcessor,
            ItemWriter<Integer> complementarItemWriter) {

        return stepBuilderFactory
                .get("complementarStep")
                .<Integer, Integer>chunk(1)
                .reader(complementarItemReader)
                .processor(complementarItemProcessor)
                .writer(complementarItemWriter)
                .build();
    }
}
