package br.com.banestes.sgrs.registradorasusep.exclusao.batch;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemWriter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class ExclusaoWriter {

    @Bean
    public ItemWriter<Integer> exclusaoItemWriter() {
        return retornos -> log.info("ItemWriter: {}", retornos.get(0));
    }

}
