package br.com.banestes.sgrs.registradorasusep.endosso.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.endosso.DependenteEndosso;

import java.util.List;

@Repository
public interface DependenteEndossoRepository extends JpaRepository<DependenteEndosso, Integer> {

    List<DependenteEndosso> findAllByidtEdsRmoPessoaAndIdtCtlProc(Long idtEdsRmoPessoa, Integer idtCtlProc);

}
