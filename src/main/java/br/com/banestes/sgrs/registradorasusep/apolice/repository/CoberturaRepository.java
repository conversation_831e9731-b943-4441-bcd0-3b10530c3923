package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.Cobertura;

import java.util.List;

@Repository
public interface CoberturaRepository extends JpaRepository<Cobertura, Integer> {

    List<Cobertura> findAllByIdtCtlProcAndIdtCtrObjeto(Integer idtCtlProc, long idtCtrObjeto);

}
