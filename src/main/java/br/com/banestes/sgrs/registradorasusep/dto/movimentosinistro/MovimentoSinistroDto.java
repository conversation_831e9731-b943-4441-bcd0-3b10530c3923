package br.com.banestes.sgrs.registradorasusep.dto.movimentosinistro;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

@Getter
@Setter
public class MovimentoSinistroDto {
    private String identificadorRegistro;
    private String codigoSeguradora;
    private String grupo;
    private String ramo;
    private String codigoSinistro;
    private String identificadorMovimento;
    private String apoliceCodigo;
    private String certificadoCodigo;
    private String numeroEndosso;
    private String dataMovimento;
    private String tipoMovimento;
    private String tipoOperacaoSinistro;
    private String origem;
    private String tipoSinistro;
    private String codigoContraparte;
    private String documento;
    private String tipoDocumento;
    private String razaoSocial;
    private String codigoInstituicao;
    private String meioPagamento;
    private String tipoPagamento;
    private String sinistroTipoPagamentoOutros;
    private String moeda;
    private Double valorMovimento;
    private Double valorMovimentoReal;
    private List<MovimentoSinistroAdicionalDto> adicionaisDespesa;
    private String codigoSindicato;
    private String codigoContrato;
    private Integer faixaContrato;
    private String numEndossoResseguro;
}
