package br.com.banestes.sgrs.registradorasusep.model.sinistro;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_SNT_AUTO")
public class SinistroAuto {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_SNT_AUTO")
    private Long idtSntAuto;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_SNT_SINISTRO")
    private Long idtSntSinistro;
    @Column(name = "NUMERO_CONVENIO")
    private String numeroConvenio;
    @Column(name = "CAUSA_SINISTRO")
    private String causaSinistro;
    @Column(name = "SEXO_CONDUTOR")
    private String sexoCondutor;
    @Column(name = "DATA_NASCIMENTO")
    private String dataNascimento;
    @Column(name = "PAIS_OCORRENCIA_SINISTRO")
    private String paisOcorrenciaSinistro;
    @Column(name = "CEP_LOCALIDADE_SINISTRO")
    private String cepLocalidadeSinistro;
    @Column(name = "CODIGO_OBJETO")
    private String codigoObjeto;
}
