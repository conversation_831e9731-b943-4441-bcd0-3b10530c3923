package br.com.banestes.sgrs.registradorasusep.model.endosso;

import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@ToString
@Table(name = "SRO_EDS_COLETIVO")
public class ContratoColetivoEndosso
{
    @Id
    @Column(name = "IDT_EDS_COLETIVO")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idtEdsColetivo;

    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;

    @Column(name = "IDT_EDS_ENDOSSO", insertable = false, updatable = false)
    private Long idtEdsEndosso;

    @Column(name = "TIPO_PLANO")
    private String tipoPlano;
}

