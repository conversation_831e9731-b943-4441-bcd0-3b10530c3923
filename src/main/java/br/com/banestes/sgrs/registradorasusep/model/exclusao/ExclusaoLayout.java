package br.com.banestes.sgrs.registradorasusep.model.exclusao;

import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.sql.Timestamp;

@Getter
@Setter
@ToString
@Entity
@Table(name = "SRO_EXC_LEIAUTE")
public class ExclusaoLayout {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_EXC_LEIAUTE")
    private Integer idtExcLeiaute;
    @Column(name = "IDT_EXC_CADASTRO")
    private Integer idtExcCadastro;
    @Column(name = "IDT_LEIAUTE")
    private Long idtLeiaute;
    @Column(name = "DES_LEIAUTE")
    private String desLeiaute;
    @Column(name = "IDENTIFICADOR_REGISTRO")
    private String identificadorRegistro;
    @Column(name = "IDC_REENVIAR_LEIAUTE")
    private String idcReenviarLeiaute;
    @Column(name = "SEQ_PROC_LEIAUTE")
    private Integer seqProcLeiaute;
    @Column(name = "IDC_SIT_PROC")
    private String idcSitProc;
    @Column(name = "DES_MSG_PROC")
    private String desMsgProc;
    @Column(name = "NUM_MATR_ULT_ATUALIZACAO")
    private Integer numMatrUltAtualizacao;
    @Column(name = "DAT_HOR_ULT_ATUALIZACAO")
    private Timestamp datHorUltAtualizacao;
}
