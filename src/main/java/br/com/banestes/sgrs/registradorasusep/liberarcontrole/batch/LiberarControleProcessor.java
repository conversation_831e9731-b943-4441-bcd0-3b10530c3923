package br.com.banestes.sgrs.registradorasusep.liberarcontrole.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component("liberarControleProcessor")
@RequiredArgsConstructor
public class LiberarControleProcessor implements ItemProcessor<Integer, Integer> {
    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;

    @Override
    public Integer process(Integer retornoSP) throws Exception {
       //apenas executa a SP
        return 0;
    }
}
