package br.com.banestes.sgrs.registradorasusep.endosso.batch;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class EndossoJobConfig {

    private final JobBuilderFactory jobBuilderFactory;

    public EndossoJobConfig(JobBuilderFactory jobBuilderFactory) {
        this.jobBuilderFactory = jobBuilderFactory;
    }

    @Bean
    public Job endossoJob(Step endossoStep) {

        return jobBuilderFactory
                .get("endossoJob")
                .start(endossoStep)
                .incrementer(new RunIdIncrementer())
                .build();
    }
}
