package br.com.banestes.sgrs.registradorasusep.endosso.batch;

import br.com.banestes.sgrs.registradorasusep.helper.RetryJobBuilder;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class EndossoJobConfig {

    private final RetryJobBuilder retryJobBuilder;

    public EndossoJobConfig(RetryJobBuilder retryJobBuilder) {
        this.retryJobBuilder = retryJobBuilder;
    }

    @Bean
    public Job endossoJob(Step endossoStep) {
        // Usando o RetryJobBuilder para jobs que não precisam de warmup
        return retryJobBuilder.buildSimpleJobWithRetry("endossoJob", endossoStep);
    }
}
