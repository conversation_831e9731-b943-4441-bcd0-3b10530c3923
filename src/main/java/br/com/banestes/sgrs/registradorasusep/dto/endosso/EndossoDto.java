package br.com.banestes.sgrs.registradorasusep.dto.endosso;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.List;

import br.com.banestes.sgrs.registradorasusep.dto.AutomovelDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.ContraGarantiaDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.ContribuicaoApolice;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.CosseguroDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.CreditoInternoExportacaoDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.ExteriorDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.ParcelaApoliceDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.PremioApoliceDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.PropostaDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.StopLossDto;

@Getter
@Setter
@ToString
public class EndossoDto {
    private String identificadorRegistro;
    private String codigoSeguradora;
    private String apoliceCodigo;
    private String numeroSusepApolice;
    private String tipoDocumentoEmitido;
    private String certificadoCodigo;
    private String codigoSeguradoraLider;
    private String apoliceCodigoLider;
    private String tipoEmissao;
    private String dataEmissao;
    private String dataInicioDocumento;
    private String dataTerminoDocumento;
    private String codigoFilial;
    private String moedaApolice;
    private Double limiteMaximoGarantia;
    private Double limiteMaximoGarantiaReal;
    private String coberturaBasica;
    private List<ContraGarantiaDto> contragarantias;
    private PropostaDto proposta;
    private List<ParteDto> partes;
    private List<IntermediarioDto> intermediarios;
    private List<StopLossDto> stopLoss;
    private List<CreditoInternoExportacaoDto> creditoInterno;
    private List<CreditoInternoExportacaoDto> creditoExportacao;
    private List<ObjetoSeguradoDto> objetosSegurado;
    private List<ExteriorDto> exteriores;
    private PremioApoliceDto premioApolice;
    private CosseguroDto cosseguro;
    private EndossoModelDto endosso;
    private AutomovelDto automovel;
    private ContribuicaoApolice contribuicaoApolice;
    private List<ParcelaApoliceDto> parcelas;
    private ContratoColetivoDto dadosContratoColetivo;
}
