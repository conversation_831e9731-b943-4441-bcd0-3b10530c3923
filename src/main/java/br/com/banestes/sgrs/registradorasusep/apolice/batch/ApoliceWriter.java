package br.com.banestes.sgrs.registradorasusep.apolice.batch;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemWriter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class ApoliceWriter {
    @Bean
    public ItemWriter<Integer> apoliceItemWriter() {
        return retornos -> log.info("ItemWriter: {}", retornos.get(0));
    }
}
