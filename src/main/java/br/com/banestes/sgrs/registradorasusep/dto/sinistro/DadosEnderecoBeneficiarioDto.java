package br.com.banestes.sgrs.registradorasusep.dto.sinistro;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroBeneficiario;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DadosEnderecoBeneficiarioDto {

    private String endereco;
    private String numero;
    private String complemento;
    private String bairro;
    private String cidade;
    private String uf;
    private String pais;
    private String cep;

    public DadosEnderecoBeneficiarioDto(SinistroBeneficiario sinistroBeneficiario) {
        this.endereco = sinistroBeneficiario.getEndereco();
        this.numero = sinistroBeneficiario.getNumero();
        this.complemento = sinistroBeneficiario.getComplemento();
        this.bairro = sinistroBeneficiario.getBairro();
        this.cidade = sinistroBeneficiario.getCidade();
        this.uf = sinistroBeneficiario.getUf();
        this.pais = sinistroBeneficiario.getPais();
        this.cep = sinistroBeneficiario.getCep();
    }
}
