package br.com.banestes.sgrs.registradorasusep.model;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;
import java.sql.Timestamp;

@Getter
@Setter
@Entity
@Table(name = "SRO_CTL_SINISTRO")
public class ControleSinistro {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTL_SINISTRO")
    private Long idtCtlSinistro;
    @Column(name = "COD_EMISSOR")
    private Integer codEmissor;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "NUM_SEQ_PCS")
    private Integer numSeqPcs;
    @Column(name = "DAT_ACOMPANHAMENTO")
    private Timestamp datAcompanhamento;
    @Column(name = "COD_EMPRESA")
    private Integer codEmpresa;
    @Column(name = "COD_RAMO")
    private Integer codRamo;
    @Column(name = "COD_SEGURADORA")
    private Integer codSeguradora;
    @Column(name = "ANO_PCS")
    private Integer anoPcs;
    @Column(name = "COD_TIP_ACOMPANHAMENTO")
    private Integer codTipAcompanhamento;
    @Column(name = "IDT_PCS_SINISTRO")
    private Integer idtPcsSinistro;
    @Column(name = "COD_MODALIDADE")
    private Integer codModalidade;
    @Column(name = "NUM_CONTRATO")
    private BigInteger numContrato;
    @Column(name = "IDT_CTL_SINISTRO_ATU")
    private long idtCtlSinistroAtu;
    @Column(name = "IDC_SIT_PROC")
    private String idcSitProc;
    @Column(name = "DES_MSG_PROC")
    private String desMsgProc;
    @Column(name = "NUM_MATR_ULT_ATUALIZACAO")
    private BigInteger numMatrUltAtualizacao;
    @Column(name = "DAT_HOR_ULT_ATUALIZACAO")
    private Timestamp datHorUltAtualizacao;
    @Column(name = "DAT_HOR_REGISTRO")
    private Timestamp datHorRegistro;
}
