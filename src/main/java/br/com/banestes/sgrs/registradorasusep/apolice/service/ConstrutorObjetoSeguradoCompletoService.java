package br.com.banestes.sgrs.registradorasusep.apolice.service;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.DependenteApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.repository.PercentualPrestamistaApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.repository.PrestamistaApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.model.apolice.*;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.ObjetoPatrimonialApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.ObjetoSeguradoService;

import java.util.ArrayList;
import java.util.List;

@Service
public class ConstrutorObjetoSeguradoCompletoService {

    private final ConstrutorCoberturaCompletaService construtorCoberturaCompletaService;
    private final ObjetoSeguradoService objetoSeguradoService;
    private final ObjetoPatrimonialApoliceRepository objetoPatrimonialApoliceRepository;
    private final RamoPessoasApoliceService ramoPessoasApoliceService;
    private final PrestamistaApoliceRepository prestamistaApoliceRepository;
    private final PercentualPrestamistaApoliceRepository percentualPrestamistaApoliceRepository;
    private final DependenteApoliceRepository dependenteApoliceRepository;

    public ConstrutorObjetoSeguradoCompletoService(ConstrutorCoberturaCompletaService construtorCoberturaCompletaService,
                                                   ObjetoSeguradoService objetoSeguradoService,
                                                   ObjetoPatrimonialApoliceRepository objetoPatrimonialApoliceRepository,
                                                   RamoPessoasApoliceService ramoPessoasApoliceService,
                                                   PrestamistaApoliceRepository prestamistaApoliceRepository,
                                                   PercentualPrestamistaApoliceRepository percentualPrestamistaApoliceRepository,
                                                   DependenteApoliceRepository dependenteApoliceRepository) {

        this.construtorCoberturaCompletaService = construtorCoberturaCompletaService;
        this.objetoSeguradoService = objetoSeguradoService;
        this.objetoPatrimonialApoliceRepository = objetoPatrimonialApoliceRepository;
        this.ramoPessoasApoliceService = ramoPessoasApoliceService;
        this.prestamistaApoliceRepository = prestamistaApoliceRepository;
        this.percentualPrestamistaApoliceRepository = percentualPrestamistaApoliceRepository;
        this.dependenteApoliceRepository = dependenteApoliceRepository;
    }

    public List<ObjetoSeguradoCompleto> obterObjetosSeguradosCompletosApolice(Apolice apolice) {

        final List<ObjetoSegurado> objetosSegurados = objetoSeguradoService.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice());

        final List<ObjetoSeguradoCompleto> objetosSeguradosCompletos = new ArrayList<>(objetosSegurados.size());

        for (ObjetoSegurado objetoSegurado : objetosSegurados) {
            final ObjetoSeguradoCompleto objetoSeguradoCompleto = new ObjetoSeguradoCompleto();
            objetoSeguradoCompleto.setObjetoSegurado(objetoSegurado);
            objetoSeguradoCompleto.setCoberturas(construtorCoberturaCompletaService.obterCoberturasCompletasObjetoSegurado(objetoSegurado));
            objetoSeguradoCompleto.setObjetosPatrimoniais(objetoPatrimonialApoliceRepository.findAllByIdtCtrObjetoAndIdtCtlProc(objetoSegurado.getIdtCtrObjeto(), objetoSegurado.getIdtCtlProc()));

            objetoSeguradoCompleto.setRamosPessoas(ramoPessoasApoliceService.findAllByIdtCtrObjetoAndIdtCtlProc(objetoSegurado.getIdtCtrObjeto(), objetoSegurado.getIdtCtlProc()));
            for(RamoPessoasApolice ramoPessoasApolice : objetoSeguradoCompleto.getRamosPessoas()){
                ramoPessoasApolice.setDependentes(dependenteApoliceRepository.findAllByidtCtrRmoPessoaAndIdtCtlProc(ramoPessoasApolice.getIdtCtrRmoPessoa(), ramoPessoasApolice.getIdtCtlProc()));
                ramoPessoasApolice.setPrestamistas(prestamistaApoliceRepository.findAllByidtCtrRmoPessoaAndIdtCtlProc(ramoPessoasApolice.getIdtCtrRmoPessoa(), ramoPessoasApolice.getIdtCtlProc()));
                for(PrestamistaApolice prestamistaApolice : ramoPessoasApolice.getPrestamistas()){
                    prestamistaApolice.setPercentuais(percentualPrestamistaApoliceRepository.findAllByidtCtrPrestamistaAndIdtCtlProc(prestamistaApolice.getIdtCtrPrestamista(), prestamistaApolice.getIdtCtlProc()));
                }
            }
            objetosSeguradosCompletos.add(objetoSeguradoCompleto);
        }
        return objetosSeguradosCompletos;
    }

}
