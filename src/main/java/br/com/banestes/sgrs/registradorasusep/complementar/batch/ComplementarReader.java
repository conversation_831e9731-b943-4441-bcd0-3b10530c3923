package br.com.banestes.sgrs.registradorasusep.complementar.batch;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.database.StoredProcedureItemReader;
import org.springframework.batch.item.database.builder.StoredProcedureItemReaderBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.SqlOutParameter;
import org.springframework.jdbc.core.SqlParameter;

import br.com.banestes.sgrs.registradorasusep.constants.Constants;

import javax.sql.DataSource;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;

@Slf4j
@Configuration
public class ComplementarReader {
    private static final String PROCEDURE_NAME = "SP_SRO_GERAR_COMPLEMENTAR_AUTO";

    @Value("${complemento:}")
    private String complemento;

    @Bean
    @StepScope
    public StoredProcedureItemReader<Integer> complementarItemReader(
            @Qualifier("sqlServerReadDataSource") DataSource dataSource) {
        log.info("Lendo procedure: {}", PROCEDURE_NAME);
        return new StoredProcedureItemReaderBuilder<Integer>()
                .name("complementarItemReader")
                .dataSource(dataSource)
                .procedureName(PROCEDURE_NAME)
                .parameters(
                        new SqlParameter("tip_cmp", Types.VARCHAR),
                        new SqlOutParameter("saida", Types.CHAR)
                )
                .rowMapper(new ComplementarRowMapper())
                .preparedStatementSetter(new PreparedStatementSetter() {
                    @Override
                    public void setValues(PreparedStatement ps) throws SQLException {
                        ps.setString(Constants.PROCEDURE_INDEX_COMPLEMENTO, complemento);
                        ps.setString(Constants.PROCEDURE_INDEX_SAIDA, "A");
                    }
                }).build();
    }


}
