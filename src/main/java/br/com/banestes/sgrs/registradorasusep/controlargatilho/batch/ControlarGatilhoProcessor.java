package br.com.banestes.sgrs.registradorasusep.controlargatilho.batch;

import br.com.banestes.sgrs.registradorasusep.constants.Constants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import br.com.banestes.sgrs.registradorasusep.exception.RotinaException;
import java.io.File;

@Slf4j
@Component("controlarGatilhoProcessor")
@RequiredArgsConstructor
public class ControlarGatilhoProcessor implements ItemProcessor<Integer, Integer> {
    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;

    @Value("${grupoRamo:}")
    private String grupoRamo;

    @Value("${controlargatilho.filepath}")
    private String filePath;

    @Override
    public Integer process(Integer retornoSP) throws Exception {
        if (retornoSP == Constants.ACAO_GERAR) {
            File file = new File(filePath.concat(Constants.GATILHO_FILE_PREFIX).concat(grupoRamo).concat(".TXT"));
            file.createNewFile();
            return 1;
        }

        if (retornoSP == Constants.ACAO_REMOVER) {
            File file = new File(filePath.concat(Constants.GATILHO_FILE_PREFIX).concat(grupoRamo).concat(".TXT"));
            file.delete();
            return 1;
        }

        if (!modoSimulacao) {
            throw new RotinaException("controlarGatilhoProcessor: Error");
        }
        return 0;
    }
}
