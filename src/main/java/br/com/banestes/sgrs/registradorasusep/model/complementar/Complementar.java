package br.com.banestes.sgrs.registradorasusep.model.complementar;

import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.math.BigInteger;

@Data
@NoArgsConstructor
@Entity
@Table(name = "SRO_CMP_AUTO")
public class Complementar {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CMP_AUTO")
    private Long idtCmpAuto;
    @Column(name = "IDC_SEL_PROC")
    private String idcSelProc;
    @Column(name = "MODALIDADE_CASCO")
    private String modalidadeCasco;
    @Column(name = "NUM_CONTRATO")
    private BigInteger numContrato;
    @Column(name = "COD_EMISSOR")
    private Integer codEmissor;
    @Column(name = "DESCRICAO_OBJETO")
    private String descricaoObjeto;
    @Column(name = "COD_EMPRESA")
    private Integer codEmpresa;
    @Column(name = "COD_RAMO")
    private Integer codRamo;
    @Column(name = "NUM_ITEM")
    private Integer numItem;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "COD_MODALIDADE")
    private Integer codModalidade;
    @Column(name = "APOLICE_CODIGO")
    private String apoliceCodigo;
    @Column(name = "NUM_ENDOSSO")
    private BigInteger numEndosso;
    @Column(name = "OBJETO_SEGURADO_CODIGO")
    private String objetoSeguradoCodigo;
    @Column(name = "NUM_ITM_ACESSORIO")
    private Integer numItmAcessorio;
    @Column(name = "IDENTIFICADOR_REGISTRO")
    private String identificadorRegistro;
    @Column(name = "TABELA_VALOR_MEDIO")
    private String tabelaValorMedio;
    @Column(name = "ENDOSSO_CODIGO")
    private String endossoCodigo;
    @Column(name = "COD_SEGURADORA")
    private Integer codSeguradora;
    @Column(name = "TIPO")
    private String tipo;
    @Column(name = "DESCRICAO_TIPO")
    private String descricaoTipo;
    @Column(name = "IDENTIFICACAO_EXATA_VEICULO")
    private String identificacaoExataVeiculo;
    @Column(name = "PERCENTUAL_TABELA_REFERENCIA")
    private BigDecimal percentualTabelaReferencia;
    @Column(name = "CODIGO_MODELO")
    private String codigoModelo;
    @Column(name = "CODIGO_UTILIZACAO")
    private String codigoUtilizacao;
    @Column(name = "ANO_MODELO")
    private Integer anoModelo;
    @Column(name = "CATEGORIA_TARIFARIA")
    private String categoriaTarifaria;
    @Column(name = "CEP_RISCO")
    private String cepRisco;
    @Column(name = "CEP_LOCALIDADE_DESTINO")
    private String cepLocalidadeDestino;
    @Column(name = "CEP_LOCALIDADE_PERNOITE")
    private String cepLocalidadePernoite;
    @Column(name = "PERCENTUAL_DESCONTO_BONUS")
    private BigDecimal percentualDescontoBonus;
    @Column(name = "CLASSE_BONUS")
    private Integer classeBonus;
    @Column(name = "CODIGO_SEGURADORA")
    private String codigoSeguradora;
    @Column(name = "CERTIFICADO_CODIGO")
    private String certificadoCodigo;

}
