package br.com.banestes.sgrs.registradorasusep.model.complementar;

import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Data
@NoArgsConstructor
@Entity
@Table(name = "SRO_CMP_PESSOA_AUTO")
public class ComplementarPessoa {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CMP_PESSOA")
    private Long idtCmpPessoa;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_CMP_AUTO")
    private Long idtCmpAuto;
    @Column(name = "NUM_CIC_CGC_PESSOA")
    private Integer numCicCgcPessoa;
    @Column(name = "SEQ_CIC_CGC_PESSOA")
    private Integer seqCicCgcPessoa;
    @Column(name = "DOCUMENTO")
    private String documento;
    @Column(name = "SEXO_CONDUTOR")
    private String sexoCondutor;
    @Column(name = "DATA_NASCIMENTO")
    private String dataNascimento;
    @Column(name = "TEMPO_HABILITACAO")
    private Integer tempoHabilitacao;


}
