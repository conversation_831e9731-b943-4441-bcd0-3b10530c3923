package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.ParcelaApolice;

import java.util.List;

@Repository
public interface ParcelaApoliceRepository extends JpaRepository<ParcelaApolice, Integer> {

    List<ParcelaApolice> findAllByIdtCtlProcAndIdtCtrApolice(Integer idtCtlProc, Long idtCtrApolice);

}
