package br.com.banestes.sgrs.registradorasusep.sinistro.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import br.com.banestes.sgrs.registradorasusep.exception.RotinaException;
import br.com.banestes.sgrs.registradorasusep.model.ControleRotina;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.Sinistro;
import br.com.banestes.sgrs.registradorasusep.service.ControleRotinaService;
import br.com.banestes.sgrs.registradorasusep.service.MessageService;
import br.com.banestes.sgrs.registradorasusep.service.RotinaService;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroRepository;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.TransmissaoSinistroService;

import java.util.List;

@Component("sinistroProcessor")
@Slf4j
@RequiredArgsConstructor
public class SinistroProcessor implements ItemProcessor<Integer, Integer> {

    private final SinistroRepository sinistroRepository;
    private final RotinaService rotinaService;
    private final TransmissaoSinistroService transmissaoSinistroService;
    private final ControleRotinaService controleRotinaService;
    private final MessageService messageService;
    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;

    @Override
    public Integer process(Integer idtCtlRtn) throws Exception {

        if (idtCtlRtn == -1) {
            return 1;
        }

        log.info("Controle de Rotina: {}", idtCtlRtn);

        if (idtCtlRtn != 0) {

            final ControleRotina controle = controleRotinaService.findByIdtCtlRtn(idtCtlRtn);
            transmissaoSinistroService.transmitirSinistros(controle.getIdtCtlProc(), controle.getIdcOperacao());

            final List<Sinistro> sinistros = sinistroRepository.listarSinistrosTransmissao(controle.getIdtCtlProc());

            if (CollectionUtils.isEmpty(sinistros)) {
                rotinaService.atualizar(idtCtlRtn);
            } else {
                if (!modoSimulacao) {
                    throw new RotinaException(messageService.message("error.layout.pendencias"));
                }
            }
            return 0;
        }

        throw new RotinaException("Número do controle de rotina inválido");
    }
}
