package br.com.banestes.sgrs.registradorasusep.endosso.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.endosso.repository.RamoPessoasEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.model.endosso.RamoPessoasEndosso;

import java.util.List;

@Service
public class RamoPessoasEndossoService {
    private final RamoPessoasEndossoRepository ramoPessoaEndossoRepository;

    public RamoPessoasEndossoService(RamoPessoasEndossoRepository ramoPessoaEndossoRepository) {
        this.ramoPessoaEndossoRepository = ramoPessoaEndossoRepository;
    }

    public List<RamoPessoasEndosso> findAllByIdtEdsObjetoAndIdtCtlProc(Long idtEdsObjeto, Integer idtCtlProc) {
        return ramoPessoaEndossoRepository.findAllByIdtEdsObjetoAndIdtCtlProc(idtEdsObjeto, idtCtlProc);
    }

}