package br.com.banestes.sgrs.registradorasusep.sinistro.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroBeneficiario;

import java.util.List;

@Repository
public interface SinistroBeneficiarioRepository extends JpaRepository<SinistroBeneficiario, Integer> {
    List<SinistroBeneficiario> getAllByIdtCtlProcAndIdtSntSinistro(Integer idtCtlProc, Long idtSntSinistro);
}

