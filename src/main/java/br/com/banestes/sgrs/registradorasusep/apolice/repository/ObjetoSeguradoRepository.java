package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.ObjetoSegurado;

import java.util.List;

@Repository
public interface ObjetoSeguradoRepository extends JpaRepository<ObjetoSegurado, Long> {

    List<ObjetoSegurado> findAllByIdtCtlProcAndIdtCtrApolice(Integer idtCtlProc, Long idtCtrApolice);

}
