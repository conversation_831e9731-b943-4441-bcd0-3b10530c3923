package br.com.banestes.sgrs.registradorasusep.model.sinistro;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_SNT_BENEFICIARIO")
public class SinistroBeneficiario {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_SNT_BENEFICIARIO")
    private Long idtSntBeneficiario;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_SNT_SINISTRO")
    private Long idtSntSinistro;
    @Column(name = "DOCUMENTO")
    private String documento;
    @Column(name = "TIPO_DOCUMENTO")
    private String tipoDocumento;
    @Column(name = "NUMERO")
    private String numero;
    @Column(name = "NOME")
    private String nome;
    @Column(name = "CEP")
    private String cep;
    @Column(name = "UF")
    private String uf;
    @Column(name = "BAIRRO")
    private String bairro;
    @Column(name = "COMPLEMENTO")
    private String complemento;
    @Column(name = "PAIS")
    private String pais;
    @Column(name = "CIDADE")
    private String cidade;
    @Column(name = "ENDERECO")
    private String endereco;

}
