package br.com.banestes.sgrs.registradorasusep.movimentosinistro.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.service.model.MovimentoSinistroService;
import br.com.banestes.sgrs.registradorasusep.service.EnvioSusepService;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class TransmissaoMovimentoSinistroService {

    @Value("${numeroErrosPlataforma}")
    private Integer numeroErrosPlataforma;
    private final MovimentoSinistroService movimentoSinistroService;
    private final ConstrutorMovimentoSinistroService construtorMovimentoSinistroService;
    private final EnvioSusepService envioSusepService;

    public TransmissaoMovimentoSinistroService(MovimentoSinistroService movimentoSinistroService,
                                               ConstrutorMovimentoSinistroService construtorMovimentoSinistroService,
                                               EnvioSusepService envioSusepService) {

        this.movimentoSinistroService = movimentoSinistroService;
        this.construtorMovimentoSinistroService = construtorMovimentoSinistroService;
        this.envioSusepService = envioSusepService;
    }

    public void transmitirMovimentoSinistro(Integer idtCtlProc, Character idcOperacao) {
        log.info("Controle de Processamento: {}.", idtCtlProc);
        if(idcOperacao.toString().equalsIgnoreCase("i")){
            log.info("Operação: INCLUSÃO");
            log.info("Leiaute Selecionado: Movimento Sinistro");
        } else if (idcOperacao.toString().equalsIgnoreCase("a")) {
            log.info("Operação: ALTERACAO");
            log.info("Leiaute Selecionado: Movimento Sinistro");
        }

        final List<MovimentoSinistro> movimentosSinistros = movimentoSinistroService.listarMovimentosSinistroTransmissao(idtCtlProc);

        Integer errorCount = 0;

        log.info("Número de movimentos de sinistro a serem processados: {}", movimentosSinistros.size());
        log.info("Processando Movimentos de Sinistro...");

        for (MovimentoSinistro movimentoSinistro : movimentosSinistros) {
//            log.info("Movimento de sinistro: {}", movimentoSinistro.getIdtSntMovSinistro());
            if (!processaMovimentoSinistro(movimentoSinistro, idcOperacao)) {
                errorCount++;
                if (Objects.equals(errorCount, numeroErrosPlataforma)) {
                    log.info("Processamento interrompido no movimento de sinistro: {}", movimentoSinistro.getIdtSntMovSinistro());
                    return;
                }
            }
        }
        log.info("Os movimentos de sinistro foram processados com sucesso !");
    }

    private boolean processaMovimentoSinistro(MovimentoSinistro movimentoSinistro, Character idcOperacao) {
        return movimentoSinistroService.atualizarStatusTransmissao(movimentoSinistro,
            envioSusepService.transmitir(
                construtorMovimentoSinistroService.construir(movimentoSinistro),
                idcOperacao.equals('I') ? "movimento-sinistro" : "movimento-sinistro/" + movimentoSinistro.getIdentificadorRegistro(),
                idcOperacao.equals('I') ? HttpMethod.POST : HttpMethod.PUT
            ),
            idcOperacao
        );
    }
}
