package br.com.banestes.sgrs.registradorasusep.model.movimentosinistro;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_SNT_MOV_ADICIONAL")
public class MovimentoSinistroAdicional {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_SNT_MOV_ADICIONAL")
    private Long idtSntMovAdicional;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_SNT_MOV_SINISTRO")
    private Long idtSntMovSinistro;
    @Column(name = "TIPO_ADICIONAL")
    private String tipoAdicional;
    @Column(name = "VALOR_MOVIMENTO_ADICIONAL")
    private Double valorMovimentoAdicional;
    @Column(name = "VALOR_MOVIMENTO_ADICIONAL_REAL")
    private Double valorMovimentoAdicionalReal;
}
