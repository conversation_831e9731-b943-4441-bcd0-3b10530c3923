package br.com.banestes.sgrs.registradorasusep.movimentosinistro.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistro;

import java.util.List;

@Repository
public interface MovimentoSinistroRepository extends JpaRepository<MovimentoSinistro, Integer> {

    @Query("SELECT m"
            + " FROM MovimentoSinistro m"
            + " INNER JOIN ControleMovimentoSinistro c"
            + " ON m.idtSntMovSinistro = c.idtCtlMovSinistro"
            + " WHERE (c.idtCtlProc = :idtCtlProc)"
            + " AND (c.idcSitProc = 'S' OR c.idcSitProc = 'P')")
    List<MovimentoSinistro> listarMovimentosSinistroTransmissao(@Param("idtCtlProc") Integer idtCtlProc);

}
