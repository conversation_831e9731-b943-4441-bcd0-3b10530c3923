package br.com.banestes.sgrs.registradorasusep.dto.apolice;

import br.com.banestes.sgrs.registradorasusep.model.apolice.Parte;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ParteDto {
    public ParteDto(Parte parte) {
        this.nome = parte.getNome();
        this.sexoSeguradoParticipante = parte.getSexoSeguradoParticipante();
        this.tipoDocumento = parte.getTipoDocumento();
        this.email = parte.getEmail();
        this.tipoParte = parte.getTipoParte();
        this.documento = parte.getDocumento();
        this.dataNascimento = parte.getDataNascimento();
        this.endereco = new EnderecoDto(parte.getEndereco(),
                parte.getNumero(),
                parte.getComplemento(),
                parte.getBairro(),
                parte.getCidade(),
                parte.getUf(),
                parte.getPais(),
                parte.getCep());
    }

    private String tipoParte;
    private String documento;
    private String sexoSeguradoParticipante;
    private String tipoDocumento;
    private String nome;
    private String email;
    private String dataNascimento;
    private EnderecoDto endereco;
}
