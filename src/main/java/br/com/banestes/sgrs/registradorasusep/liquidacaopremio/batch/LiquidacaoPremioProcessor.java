package br.com.banestes.sgrs.registradorasusep.liquidacaopremio.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import br.com.banestes.sgrs.registradorasusep.exception.RotinaException;
import br.com.banestes.sgrs.registradorasusep.liquidacaopremio.repository.PremioRepository;
import br.com.banestes.sgrs.registradorasusep.liquidacaopremio.service.TransmissaoLiquidacaoPremioService;
import br.com.banestes.sgrs.registradorasusep.model.ControleRotina;
import br.com.banestes.sgrs.registradorasusep.model.premio.Premio;
import br.com.banestes.sgrs.registradorasusep.service.ControleRotinaService;
import br.com.banestes.sgrs.registradorasusep.service.MessageService;
import br.com.banestes.sgrs.registradorasusep.service.RotinaService;

import java.util.List;

@Component("liquidacaoPremioProcessor")
@Slf4j
@RequiredArgsConstructor
public class LiquidacaoPremioProcessor implements ItemProcessor<Integer, Integer> {

    private final PremioRepository premioRepository;
    private final RotinaService rotinaService;
    private final TransmissaoLiquidacaoPremioService transmissaoLiquidacaoPremioService;
    private final ControleRotinaService controleRotinaService;
    private final MessageService messageService;
    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;

    @Override
    public Integer process(Integer idtCtlRtn) throws Exception {

        if (idtCtlRtn == -1) {
            return 1;
        }

        log.info("Controle de Rotina: {}", idtCtlRtn);

        if (idtCtlRtn != 0) {
            final ControleRotina controle = controleRotinaService.findByIdtCtlRtn(idtCtlRtn);
            transmissaoLiquidacaoPremioService.transmitirLiquidacaoPremio(controle.getIdtCtlProc(), controle.getIdcOperacao());

            final List<Premio> premios = premioRepository.listarLiquidacoesPremioTransmissao(controle.getIdtCtlProc());

            if (CollectionUtils.isEmpty(premios)) {
                rotinaService.atualizar(idtCtlRtn);
            } else {
                if (!modoSimulacao) {
                    throw new RotinaException(messageService.message("error.layout.pendencias"));
                }
            }

            return 0;
        }

        throw new RotinaException("Número do controle de rotina inválido");
    }
}
