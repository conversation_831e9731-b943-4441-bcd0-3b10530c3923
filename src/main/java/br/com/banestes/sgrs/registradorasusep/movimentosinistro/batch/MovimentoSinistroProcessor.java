package br.com.banestes.sgrs.registradorasusep.movimentosinistro.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import br.com.banestes.sgrs.registradorasusep.exception.RotinaException;
import br.com.banestes.sgrs.registradorasusep.model.ControleRotina;
import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.repository.MovimentoSinistroRepository;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.service.TransmissaoMovimentoSinistroService;
import br.com.banestes.sgrs.registradorasusep.service.ControleRotinaService;
import br.com.banestes.sgrs.registradorasusep.service.MessageService;
import br.com.banestes.sgrs.registradorasusep.service.RotinaService;

import java.util.List;

@Component("movimentoSinistroProcessor")
@Slf4j
@RequiredArgsConstructor
public class MovimentoSinistroProcessor implements ItemProcessor<Integer, Integer> {

    private final MovimentoSinistroRepository movimentoSinistroRepository;
    private final RotinaService rotinaService;
    private final TransmissaoMovimentoSinistroService transmissaoMovimentoSinistroService;
    private final ControleRotinaService controleRotinaService;
    private final MessageService messageService;
    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;

    @Override
    public Integer process(Integer idtCtlRtn) throws Exception {

        if (idtCtlRtn == -1) {
            return 1;
        }

        log.info("Controle de Rotina: {}", idtCtlRtn);

        if (idtCtlRtn != 0) {
            final ControleRotina controle = controleRotinaService.findByIdtCtlRtn(idtCtlRtn);
            transmissaoMovimentoSinistroService.transmitirMovimentoSinistro(controle.getIdtCtlProc(), controle.getIdcOperacao());

            final List<MovimentoSinistro> movimentoSinistros = movimentoSinistroRepository.listarMovimentosSinistroTransmissao(controle.getIdtCtlProc());

            if (CollectionUtils.isEmpty(movimentoSinistros)) {
                rotinaService.atualizar(idtCtlRtn);
            } else {
                if (!modoSimulacao) {
                    throw new RotinaException(messageService.message("error.layout.pendencias"));
                }
            }

            return 0;
        }

        throw new RotinaException("Número do controle de rotina inválido");
    }
}
