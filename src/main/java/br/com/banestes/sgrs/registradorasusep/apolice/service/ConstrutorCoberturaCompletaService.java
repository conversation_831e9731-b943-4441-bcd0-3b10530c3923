package br.com.banestes.sgrs.registradorasusep.apolice.service;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.apolice.service.model.CoberturaService;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Cobertura;
import br.com.banestes.sgrs.registradorasusep.model.apolice.CoberturaCompleta;
import br.com.banestes.sgrs.registradorasusep.model.apolice.ObjetoSegurado;

import java.util.ArrayList;
import java.util.List;

@Service
public class ConstrutorCoberturaCompletaService {
    private final CoberturaService coberturaService;

    public ConstrutorCoberturaCompletaService(CoberturaService coberturaService) {
        this.coberturaService = coberturaService;
    }

    public List<CoberturaCompleta> obterCoberturasCompletasObjetoSegurado(ObjetoSegurado objetoSegurado) {

        final List<Cobertura> coberturas = coberturaService.findAllByIdtCtlProcAndIdtCtrApoliceAndIdtCtrObjeto(
                objetoSegurado.getIdtCtlProc(),
                objetoSegurado.getIdtCtrObjeto());

        final List<CoberturaCompleta> coberturasCompletas = new ArrayList<>(coberturas.size());

        for (Cobertura cobertura : coberturas) {
            final CoberturaCompleta coberturaCompleta = new CoberturaCompleta();
            coberturaCompleta.setCobertura(cobertura);
            coberturasCompletas.add(coberturaCompleta);
        }
        return coberturasCompletas;
    }

}
