package br.com.banestes.sgrs.registradorasusep.model.apolice;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_CTR_COBERTURA")
public class Cobertura {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTR_COBERTURA")
    private Long idtCtrCobertura;
    @Column(name = "IDT_CTR_OBJETO")
    private Long idtCtrObjeto;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "COD_COBERTURA")
    private Integer codCobertura;
    @Column(name = "GRUPO")
    private String grupo;
    @Column(name = "COBERTURA_INTERNA_SEGURADORA")
    private String coberturaInternaSeguradora;
    @Column(name = "RAMO")
    private String ramo;
    @Column(name = "OUTRAS_DESCRICAO")
    private String outrasDescricao;
    @Column(name = "CODIGO")
    private String codigo;
    @Column(name = "NUMERO_PROCESSO")
    private String numeroProcesso;
    @Column(name = "DATA_INICIO_COBERTURA")
    private String dataInicioCobertura;
    @Column(name = "LIMITE_MAXIMO_INDENIZACAO")
    private Double limiteMaximoIndenizacao;
    @Column(name = "DATA_TERMINO_COBERTURA")
    private String dataTerminoCobertura;
    @Column(name = "LIMITE_MAXIMO_INDENIZACAO_REAL")
    private Double limiteMaximoIndenizacaoReal;
    @Column(name = "INDICE_ATUALIZACAO")
    private String indiceAtualizacao;
    @Column(name = "PERIODICIDADE_ATUALIZACAO")
    private Integer periodicidadeAtualizacao;
    @Column(name = "PERIODICIDADE_PREMIO")
    private String periodicidadePremio;
    @Column(name = "DESCRICAO_PERIODICIDADE")
    private String descricaoPeriodicidade;
    @Column(name = "PERIODICIDADE_UNIDADE")
    private String periodicidadeUnidade;
    @Column(name = "COBERTURA_PRINCIPAL")
    private String coberturaPrincipal;
    @Column(name = "TIPO_RISCO")
    private String tipoRisco;
    @Column(name = "COBERTURA_CARACTERISTICA")
    private String coberturaCaracteristica;
    @Column(name = "COBERTURA_TIPO")
    private String coberturaTipos;
    @Column(name = "CARENCIA_PERIODICIDADE")
    private String carenciaPeriodicidade;
    @Column(name = "CARENCIA_PERIODO")
    private Integer carenciaPeriodo;
    @Column(name = "CARENCIA_PERIODICIDADE_DIAS")
    private String carenciaPeriodicidadeDias;
    @Column(name = "CARENCIA_DATA_TERMINO")
    private String carenciaDataTermino;
    @Column(name = "CARENCIA_DATA_INICIO")
    private String carenciaDataInicio;
    @Column(name = "DATA_INICIO_PREMIO")
    private String dataInicioPremio;
    @Column(name = "CUSTO")
    private Double custo;
    @Column(name = "DATA_TERMINO_PREMIO")
    private String dataTerminoPremio;
    @Column(name = "VALOR_PREMIO")
    private Double valorPremio;
    @Column(name = "CUSTO_REAL")
    private Double custoReal;
    @Column(name = "VALOR_PREMIO_REAL")
    private Double valorPremioReal;
    @Column(name = "LIMITE_MAXIMO_INDENIZACAO_SUBLIMITE")
    private String limiteMaximoIndenizacaoSublimite;
    @Column(name = "IOF")
    private Double iof;

}
