package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.Apolice;

import java.util.List;

@Repository
public interface ApoliceRepository extends JpaRepository<Apolice, Integer> {

    @Query("SELECT a"
            + " FROM Apolice a"
            + " INNER JOIN ControleApolice c"
            + " ON a.idtCtrApolice = c.idtCtlApolice"
            + " WHERE (c.idtCtlProc = :idtCtlProc)"
            + " AND (c.idcSitProc = 'S' OR c.idcSitProc = 'P')")
    List<Apolice> listarApolicesTransmissao(@Param("idtCtlProc") Integer idtCtlProc);

}
