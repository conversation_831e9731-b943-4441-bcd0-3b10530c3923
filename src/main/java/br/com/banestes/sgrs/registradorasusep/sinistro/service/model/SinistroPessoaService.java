package br.com.banestes.sgrs.registradorasusep.sinistro.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroPessoa;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroPessoaRepository;

import java.util.List;

@Service
public class SinistroPessoaService {
    private final SinistroPessoaRepository sinistroPessoaRepository;

    public SinistroPessoaService(SinistroPessoaRepository sinistroPessoaRepository) {
        this.sinistroPessoaRepository = sinistroPessoaRepository;
    }

    public List<SinistroPessoa> getAllByIdtCtlProcAndIdtSntSinistro(Integer idtCtlProc, Long idtSntSinistro) {
        return sinistroPessoaRepository.getAllByIdtCtlProcAndIdtSntSinistro(idtCtlProc, idtSntSinistro);
    }

}
