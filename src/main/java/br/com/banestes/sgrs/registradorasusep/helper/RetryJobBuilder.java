package br.com.banestes.sgrs.registradorasusep.helper;

import br.com.banestes.sgrs.registradorasusep.config.RetryConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.step.skip.SkipPolicy;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.RetryPolicy;
import org.springframework.retry.backoff.BackOffPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;

/**
 * Utilitário para construir Jobs e Steps com retry automático
 */
@Slf4j
@Component
public class RetryJobBuilder {

    private final JobBuilderFactory jobBuilderFactory;
    private final StepBuilderFactory stepBuilderFactory;
    private final EnhancedBatchWarmup enhancedBatchWarmup;
    private final RetryPolicy stepRetryPolicy;
    private final BackOffPolicy stepBackOffPolicy;
    private final SkipPolicy customSkipPolicy;
    private final RetryTemplate retryTemplate;

    @Value("${step.retry.enabled:true}")
    private Boolean stepRetryEnabled;

    @Value("${warmup.enabled:true}")
    private Boolean warmupEnabled;

    public RetryJobBuilder(JobBuilderFactory jobBuilderFactory,
                          StepBuilderFactory stepBuilderFactory,
                          EnhancedBatchWarmup enhancedBatchWarmup,
                          RetryPolicy stepRetryPolicy,
                          BackOffPolicy stepBackOffPolicy,
                          SkipPolicy customSkipPolicy,
                          RetryTemplate retryTemplate) {
        this.jobBuilderFactory = jobBuilderFactory;
        this.stepBuilderFactory = stepBuilderFactory;
        this.enhancedBatchWarmup = enhancedBatchWarmup;
        this.stepRetryPolicy = stepRetryPolicy;
        this.stepBackOffPolicy = stepBackOffPolicy;
        this.customSkipPolicy = customSkipPolicy;
        this.retryTemplate = retryTemplate;
    }

    /**
     * Constrói um Job com warmup e retry automático
     */
    public Job buildJobWithRetry(String jobName, Step mainStep) {
        if (warmupEnabled) {
            return jobBuilderFactory
                    .get(jobName)
                    .incrementer(new RunIdIncrementer())
                    .start(enhancedBatchWarmup.enhancedWarmupBatchStep())
                    .next(enhancedBatchWarmup.applicationReadinessStep())
                    .next(mainStep)
                    .build();
        } else {
            return jobBuilderFactory
                    .get(jobName)
                    .incrementer(new RunIdIncrementer())
                    .start(mainStep)
                    .build();
        }
    }

    /**
     * Constrói um Job simples sem warmup mas com retry
     */
    public Job buildSimpleJobWithRetry(String jobName, Step mainStep) {
        return jobBuilderFactory
                .get(jobName)
                .start(mainStep)
                .incrementer(new RunIdIncrementer())
                .build();
    }

    /**
     * Constrói um Step com retry automático
     */
    public <I, O> Step buildStepWithRetry(String stepName,
                                         ItemReader<I> reader,
                                         ItemProcessor<I, O> processor,
                                         ItemWriter<O> writer,
                                         int chunkSize) {

        // Aplica wrappers de retry se habilitado
        if (stepRetryEnabled) {
            log.info("✅ Step '{}' configurado com retry automático", stepName);
            return stepBuilderFactory
                    .get(stepName)
                    .<I, O>chunk(chunkSize)
                    .reader(new RetryConfiguration.RetryableItemReader<>(reader, retryTemplate))
                    .processor(new RetryConfiguration.RetryableItemProcessor<>(processor, retryTemplate))
                    .writer(new RetryConfiguration.RetryableItemWriter<>(writer, retryTemplate))
                    .retryPolicy(stepRetryPolicy)
                    .backOffPolicy(stepBackOffPolicy)
                    .skipPolicy(customSkipPolicy)
                    .build();
        } else {
            log.info("ℹ️ Step '{}' configurado SEM retry", stepName);
            return stepBuilderFactory
                    .get(stepName)
                    .<I, O>chunk(chunkSize)
                    .reader(reader)
                    .processor(processor)
                    .writer(writer)
                    .build();
        }
    }

    /**
     * Constrói um Step simples (sem processor) com retry
     */
    public <T> Step buildSimpleStepWithRetry(String stepName,
                                           ItemReader<T> reader,
                                           ItemWriter<T> writer,
                                           int chunkSize) {

        if (stepRetryEnabled) {
            return stepBuilderFactory
                    .get(stepName)
                    .<T, T>chunk(chunkSize)
                    .reader(new RetryConfiguration.RetryableItemReader<>(reader, retryTemplate))
                    .writer(new RetryConfiguration.RetryableItemWriter<>(writer, retryTemplate))
                    .retryPolicy(stepRetryPolicy)
                    .backOffPolicy(stepBackOffPolicy)
                    .skipPolicy(customSkipPolicy)
                    .build();
        } else {
            return stepBuilderFactory
                    .get(stepName)
                    .<T, T>chunk(chunkSize)
                    .reader(reader)
                    .writer(writer)
                    .build();
        }
    }

    /**
     * Método para verificar se o retry está habilitado
     */
    public boolean isRetryEnabled() {
        return stepRetryEnabled;
    }

    /**
     * Método para verificar se o warmup está habilitado
     */
    public boolean isWarmupEnabled() {
        return warmupEnabled;
    }

    /**
     * Método para obter estatísticas de configuração
     */
    public String getRetryConfiguration() {
        return String.format("Retry: %s, Warmup: %s", 
                stepRetryEnabled ? "HABILITADO" : "DESABILITADO",
                warmupEnabled ? "HABILITADO" : "DESABILITADO");
    }
}
