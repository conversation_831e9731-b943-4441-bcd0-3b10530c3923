package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.PercentualPrestamistaApolice;

import java.util.List;

@Repository
public interface PercentualPrestamistaApoliceRepository extends JpaRepository<PercentualPrestamistaApolice, Integer> {

    List<PercentualPrestamistaApolice> findAllByidtCtrPrestamistaAndIdtCtlProc(Long idtCtrPrestamista, Integer idtCtlProc);

}
