package br.com.banestes.sgrs.registradorasusep.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.ControleApolice;

import java.util.List;

@Repository
public interface ControleApoliceRepository extends JpaRepository<ControleApolice, Long> {

    @Query("select controleApolice from ControleApolice controleApolice "
            + "where (:idtCtlProc is null or controleApolice.idtCtlProc = :idtCtlProc) "
            + "and (controleApolice.idcSitProc = 'S') ")
    List<ControleApolice> listarControleApolicePendentes(@Param("idtCtlProc") Integer numeroProcessamento);

    List<ControleApolice> findAllByIdtCtlProc(Integer numeroProcessamento);
}
