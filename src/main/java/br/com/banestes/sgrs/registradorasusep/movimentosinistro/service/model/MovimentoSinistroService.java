package br.com.banestes.sgrs.registradorasusep.movimentosinistro.service.model;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.helper.ApiResponseHelper;
import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.repository.MovimentoSinistroRepository;
import br.com.banestes.sgrs.registradorasusep.service.ControleMovimentoSinistroService;

import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class MovimentoSinistroService {
    private final MovimentoSinistroRepository movimentoSinistroRepository;
    private final ControleMovimentoSinistroService controleMovimentoSinistroService;

    public MovimentoSinistroService(MovimentoSinistroRepository movimentoSinistroRepository,
                                    ControleMovimentoSinistroService controleMovimentoSinistroService) {

        this.movimentoSinistroRepository = movimentoSinistroRepository;
        this.controleMovimentoSinistroService = controleMovimentoSinistroService;
    }

    public List<MovimentoSinistro> listarMovimentosSinistroTransmissao(Integer idtCtlProc) {
        return movimentoSinistroRepository.listarMovimentosSinistroTransmissao(idtCtlProc);
    }

    @Transactional
    public boolean atualizarStatusTransmissao(MovimentoSinistro movimentoSinistro, ResponseDto resposta, Character idcOperacao) {
        final List<Integer> httpCodeErrors = Arrays.asList(403, 415, 500, 501, 502, 503, 504, 600);
        String situacaoProcessamento = "R";
        String mensagemErroProcessamento = null;
        if (resposta.getCode() != HttpStatus.OK.value() && resposta.getCode() != HttpStatus.CREATED.value()) {

            if (httpCodeErrors.contains(resposta.getCode())) {

                situacaoProcessamento = "P";
                mensagemErroProcessamento = ApiResponseHelper.getErrorMessage(resposta);
                controleMovimentoSinistroService.atualizar(movimentoSinistro.getIdtSntMovSinistro(), situacaoProcessamento, mensagemErroProcessamento, idcOperacao);

                log.info("Erro: {} - tente novamente mais tarde.", resposta.getMessage());
                return false;
            }

            // erro de negócio
            situacaoProcessamento = "E";
            mensagemErroProcessamento = ApiResponseHelper.getErrorMessage(resposta);
        }
        controleMovimentoSinistroService.atualizar(movimentoSinistro.getIdtSntMovSinistro(), situacaoProcessamento, mensagemErroProcessamento, idcOperacao);
        return true;
    }

}
