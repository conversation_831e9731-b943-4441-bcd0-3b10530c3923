package br.com.banestes.sgrs.registradorasusep.endosso.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.endosso.repository.IntermediarioEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.model.endosso.IntermediarioEndosso;

import java.util.List;

@Service
public class IntermediarioEndossoService {
    private final IntermediarioEndossoRepository intermediarioRepository;

    public IntermediarioEndossoService(IntermediarioEndossoRepository intermediarioRepository) {
        this.intermediarioRepository = intermediarioRepository;
    }

    public List<IntermediarioEndosso> findAllByIdtCtlProcAndIdtEdsEndosso(Integer idtCtlProc, Long idtEdsEndosso) {
        return intermediarioRepository.findAllByIdtCtlProcAndIdtEdsEndosso(idtCtlProc, idtEdsEndosso);
    }

}
