package br.com.banestes.sgrs.registradorasusep.model.endosso;

import java.util.List;
import javax.persistence.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@ToString
@Table(name = "SRO_EDS_PRESTAMISTA")
public class PrestamistaEndosso
{
    @Id
    @Column(name = "IDT_EDS_PRESTAMISTA")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idtEdsPrestamista;

    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;

    @Column(name = "IDT_EDS_RMO_PESSOA", insertable = false, updatable = false)
    private Long idtEdsRmoPessoa;

    @Column(name = "MODELO_CAPITAL")
    private String modeloCapital;

    @Column(name = "PRESTAMISTA_TIPO")
    private String tipo;

    @Column(name = "TIPO_DOCUMENTO")
    private String tipoDocumento;

    @Column(name = "DOCUMENTO")
    private String documento;

    @Column(name = "NOME")
    private String nome;

    @Column(name = "TIPO_OBRIGACAO")
    private String tipoObrigacao;

    @Column(name = "DESCRICAO_OBRIGACAO")
    private String descricaoObrigacao;

    @Transient
    private List<PercentualPrestamistaEndosso> percentuais;
}
