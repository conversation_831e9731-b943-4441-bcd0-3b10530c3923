package br.com.banestes.sgrs.registradorasusep.model;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;
import java.sql.Timestamp;

@Getter
@Setter
@Entity
@Table(name = "SRO_CTL_ENDOSSO")
public class ControleEndosso {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTL_ENDOSSO")
    private Long idtCtlEndosso;
    @Column(name = "IDT_CTL_ENDOSSO_ATU")
    private Long idtCtlEndossoAtu;
    @Column(name = "NUM_CONTRATO")
    private BigInteger numContrato;
    @Column(name = "COD_RAMO")
    private Integer codRamo;
    @Column(name = "COD_SEGURADORA")
    private Integer codSeguradora;
    @Column(name = "COD_EMISSOR")
    private Integer codEmissor;
    @Column(name = "COD_MODALIDADE")
    private Integer codModalidade;
    @Column(name = "IDC_SIT_PROC")
    private String idcSitProc;
    @Column(name = "COD_EMPRESA")
    private Integer codEmpresa;
    @Column(name = "DES_MSG_PROC")
    private String desMsgProc;
    @Column(name = "NUM_ENDOSSO")
    private BigInteger numEndosso;
    @Column(name = "NUM_MATR_ULT_ATUALIZACAO")
    private Integer numMatrUltAtualizacao;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "DAT_HOR_ULT_ATUALIZACAO")
    private Timestamp datHorUltAtualizacao;
    @Column(name = "DAT_HOR_REGISTRO")
    private Timestamp datHorRegistro;
    @Column(name = "TIP_CONTRATO")
    private String tipContrato;
}
