package br.com.banestes.sgrs.registradorasusep.endosso.repository;

import org.springframework.data.jpa.repository.JpaRepository;

import br.com.banestes.sgrs.registradorasusep.model.endosso.ObjetoPatrimonialEndosso;

import java.util.List;

public interface ObjetoPatrimonialEndossoRepository extends JpaRepository<ObjetoPatrimonialEndosso, Integer> {

    List<ObjetoPatrimonialEndosso> findAllByIdtEdsObjetoAndIdtCtlProc(Long idtEdsObjeto, Integer idtCtlProc);

}
