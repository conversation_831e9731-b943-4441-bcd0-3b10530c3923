package br.com.banestes.sgrs.registradorasusep.model.premio;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "SRO_PRM_PREMIO")
public class Premio {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_PRM_PREMIO")
    private Long idtPrmPremio;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDENTIFICADOR_REGISTRO")
    private String identificadorRegistro;
    @Column(name = "CODIGO_SEGURADORA")
    private String codigoSeguradora;
    @Column(name = "APOLICE_CODIGO")
    private String apoliceCodigo;
    @Column(name = "CERTIFICADO_CODIGO")
    private String certificadoCodigo;
    @Column(name = "ENDOSSO_CODIGO")
    private String endossoCodigo;
    @Column(name = "IDENTIFICADOR_MOVIMENTO")
    private String identificadorMovimento;
    @Column(name = "MOEDA")
    private String moeda;
    @Column(name = "VALOR_MOVIMENTO")
    private BigDecimal valorMovimento;
    @Column(name = "VALOR_MOVIMENTO_REAL")
    private BigDecimal valorMovimentoReal;
    @Column(name = "DATA_MOVIMENTO")
    private String dataMovimento;
    @Column(name = "NUMERO_PARCELA_MOVIMENTO")
    private Integer numeroParcelaMovimento;
    @Column(name = "DATA_VENCIMENTO")
    private String dataVencimento;
    @Column(name = "TIPO_MOVIMENTO")
    private String tipoMovimento;
    @Column(name = "ESTIPULANTES_PAGADOR")
    private String estipulantesPagador;
    @Column(name = "PERIODO_PAGAMENTO")
    private String periodoPagamento;
    @Column(name = "ORIGEM")
    private String origem;
    @Column(name = "DOCUMENTO_PAGADOR")
    private String documentoPagador;
    @Column(name = "TIPO_DOCUMENTO_PAGADOR")
    private String tipoDocumentoPagador;
    @Column(name = "NOME_PAGADOR")
    private String nomePagador;
    @Column(name = "CODIGO_INSTITUICAO")
    private String codigoInstituicao;
    @Column(name = "MEIO_PAGAMENTO")
    private String meioPagamento;
    @Column(name="VALOR_SEGURADO")
    private BigDecimal valorSegurado;
    @Column(name="VALOR_ESTIPULANTE")
    private BigDecimal valorEstipulante;
}
