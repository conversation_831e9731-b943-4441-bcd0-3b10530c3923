package br.com.banestes.sgrs.registradorasusep.dto.sinistro;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroCobertura;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class CoberturaAfetadaDto {
    private String codigoObjeto;
    private String grupo;
    private String ramo;
    private String sinistroCoberturaCodigo;
    private String sinistroCoberturaOutros;
    private String dataAvisoCobertura;
    private String dataRegistroSeguradoraCobertura;
    private String dataReclamacaoTerceiroCobertura;
    private String coberturaInternaSeguradora;

    public CoberturaAfetadaDto(SinistroCobertura sinistroCobertura) {
        this.codigoObjeto = sinistroCobertura.getCodigoObjeto();
        this.grupo = sinistroCobertura.getGrupo();
        this.ramo = sinistroCobertura.getRamo();
        this.sinistroCoberturaCodigo = sinistroCobertura.getSinistroCoberturaCodigo();
        this.sinistroCoberturaOutros = sinistroCobertura.getSinistroCoberturaOutros();
        this.dataAvisoCobertura = sinistroCobertura.getDataAvisoCobertura();
        this.dataRegistroSeguradoraCobertura = sinistroCobertura.getDataRegistroSeguradoraCobertura();
        this.dataReclamacaoTerceiroCobertura = sinistroCobertura.getDataReclamacaoTerceiroCobertura();
        this.coberturaInternaSeguradora = sinistroCobertura.getCoberturaInternaSeguradora();
    }
}
