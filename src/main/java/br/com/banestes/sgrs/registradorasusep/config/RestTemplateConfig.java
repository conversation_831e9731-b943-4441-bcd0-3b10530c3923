package br.com.banestes.sgrs.registradorasusep.config;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import br.com.banestes.sgrs.registradorasusep.error.ApiErrorHandler;

import java.time.Duration;

@Configuration
public class RestTemplateConfig {

    private static final long TIME_OUT = 60L;
    private final RestTemplateBuilder restTemplateBuilder;
    private final ApiErrorHandler apiErrorHandler;

    public RestTemplateConfig(RestTemplateBuilder restTemplateBuilder, ApiErrorHandler apiErrorHandler) {
        this.restTemplateBuilder = restTemplateBuilder;
        this.apiErrorHandler = apiErrorHandler;
    }

    @Bean
    public RestTemplate restTemplate() {
        restTemplateBuilder.errorHandler(apiErrorHandler);
        restTemplateBuilder.setConnectTimeout(Duration.ofSeconds(TIME_OUT));
        restTemplateBuilder.setReadTimeout(Duration.ofSeconds(TIME_OUT));
        return restTemplateBuilder.build();
    }
}
