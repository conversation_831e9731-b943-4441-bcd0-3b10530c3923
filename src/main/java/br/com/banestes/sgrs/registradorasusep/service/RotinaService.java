package br.com.banestes.sgrs.registradorasusep.service;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import javax.sql.DataSource;
import java.sql.Connection;

@Slf4j
@Service
public class RotinaService {

    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;

    public static final Integer SLEEP_TIME = 90000;
    private final JdbcTemplate jdbcTemplateObject;

    public RotinaService(@Qualifier("sqlServerDataSource") DataSource dataSource, JdbcTemplate jdbcTemplateObject) {
        this.jdbcTemplateObject = jdbcTemplateObject;
        this.jdbcTemplateObject.setDataSource(dataSource);
    }

    @SneakyThrows
    public void atualizar(Integer numeroProcessamento) {
        String sql = "update SRO_CTL_ROTINA set IDC_SIT_PROC = 'R' where IDT_CTL_RTN = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, numeroProcessamento);
            try (Connection connection = jdbcTemplateObject.getDataSource().getConnection()) {
                connection.commit();
            }
        }
        Thread.sleep(SLEEP_TIME);
        log.info("rotina atualizada com sucesso !");
    }

    @SneakyThrows
    public void atualizarErro(Integer numeroProcessamento) {
        String sql = "update SRO_CTL_ROTINA set IDC_SIT_PROC = 'P' where IDT_CTL_RTN = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, numeroProcessamento);
            try (Connection connection = jdbcTemplateObject.getDataSource().getConnection()) {
                connection.commit();
            }
        }
        Thread.sleep(SLEEP_TIME);
        log.info("rotina atualizada com sucesso !");
    }

}
