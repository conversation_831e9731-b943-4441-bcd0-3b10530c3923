package br.com.banestes.sgrs.registradorasusep.apolice.batch;

import br.com.banestes.sgrs.registradorasusep.constants.Constants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.ApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.service.TransmissaoApoliceService;
import br.com.banestes.sgrs.registradorasusep.exception.RotinaException;
import br.com.banestes.sgrs.registradorasusep.model.ControleRotina;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Apolice;
import br.com.banestes.sgrs.registradorasusep.service.ControleRotinaService;
import br.com.banestes.sgrs.registradorasusep.service.MessageService;
import br.com.banestes.sgrs.registradorasusep.service.RotinaService;

import java.io.File;
import java.util.List;

@Slf4j
@Component("apoliceProcessor")
@RequiredArgsConstructor
public class ApoliceProcessor implements ItemProcessor<Integer, Integer> {
    private final RotinaService rotinaService;
    private final ApoliceRepository apoliceRepository;
    private final TransmissaoApoliceService transmissaoApoliceService;
    private final ControleRotinaService controleRotinaService;
    private final MessageService messageService;
    @Value("${grupoRamo:}")
    private String grupoRamo;
    @Value("${controlargatilho.filepath}")
    private String filePath;
    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;

    @Override
    public Integer process(Integer idtCtlRtn) throws Exception {

        File file = new File(filePath.concat(Constants.GATILHO_FILE_PREFIX).concat(grupoRamo).concat(".TXT"));
        file.delete();

        if (idtCtlRtn == -1) {
            return 1;
        }

        log.info("Controle de Rotina: {}", idtCtlRtn);

        if (idtCtlRtn != 0) {

            final ControleRotina controle = controleRotinaService.findByIdtCtlRtn(idtCtlRtn);
            transmissaoApoliceService.transmitirApolices(controle.getIdtCtlProc(), controle.getIdcOperacao());

            final List<Apolice> apolices = apoliceRepository.listarApolicesTransmissao(controle.getIdtCtlProc());

            if (CollectionUtils.isEmpty(apolices)) {
                rotinaService.atualizar(idtCtlRtn);
            } else {
                if (!modoSimulacao) {
                    throw new RotinaException(messageService.message("error.layout.pendencias"));
                }
            }

            return 0;
        }

        throw new RotinaException("Número do controle de rotina inválido");
    }
}
