package br.com.banestes.sgrs.registradorasusep.complementar.service;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.complementar.mapper.ComplementarMapper;
import br.com.banestes.sgrs.registradorasusep.complementar.service.model.CoberturaAutoService;
import br.com.banestes.sgrs.registradorasusep.complementar.service.model.ComplementarPessoaService;
import br.com.banestes.sgrs.registradorasusep.dto.complementar.ComplementarDto;
import br.com.banestes.sgrs.registradorasusep.model.complementar.Complementar;

@Service
public class ConstrutorComplementarService {

    private final ComplementarMapper complementarMapper;
    private final CoberturaAutoService coberturaAutoService;
    private final ComplementarPessoaService complementarPessoaService;

    public ConstrutorComplementarService(ComplementarMapper complementarMapper,
                                         CoberturaAutoService coberturaAutoService,
                                         ComplementarPessoaService complementarPessoaService) {

        this.complementarMapper = complementarMapper;
        this.coberturaAutoService = coberturaAutoService;
        this.complementarPessoaService = complementarPessoaService;
    }

    public ComplementarDto construir(Complementar complementar) {
        return complementarMapper.toDto(complementar,
                coberturaAutoService.findAllByIdtCtlProcAndIdtCmpAuto(complementar.getIdtCtlProc(), complementar.getIdtCmpAuto()),
                complementarPessoaService.findAllByIdtCtlProcAndIdtCmpAuto(complementar.getIdtCtlProc(), complementar.getIdtCmpAuto()));
    }
}
