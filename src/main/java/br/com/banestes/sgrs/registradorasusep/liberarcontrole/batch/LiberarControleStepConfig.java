package br.com.banestes.sgrs.registradorasusep.liberarcontrole.batch;

import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LiberarControleStepConfig {

    private final StepBuilderFactory stepBuilderFactory;

    public LiberarControleStepConfig(StepBuilderFactory stepBuilderFactory) {
        this.stepBuilderFactory = stepBuilderFactory;
    }

    @Bean
    public Step liberarControleStep(
            ItemReader<Integer> liberarControleItemReader,
            @Qualifier("liberarControleProcessor") ItemProcessor<Integer, Integer> liberarControleItemProcessor,
            ItemWriter<Integer> liberarControleItemWriter) {

        return stepBuilderFactory
                .get("liberarControleStep")
                .<Integer, Integer>chunk(1)
                .reader(liberarControleItemReader)
                .processor(liberarControleItemProcessor)
                .writer(liberarControleItemWriter)
                .build();
    }
}
