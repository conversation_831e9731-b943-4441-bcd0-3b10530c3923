package br.com.banestes.sgrs.registradorasusep.controlargatilho.batch;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ControlarGatilhoJobConfig {

    private final JobBuilderFactory jobBuilderFactory;

    public ControlarGatilhoJobConfig(JobBuilderFactory jobBuilderFactory) {
        this.jobBuilderFactory = jobBuilderFactory;
    }

    @Bean
    public Job controlarGatilhoJob(Step controlarGatilhoStep) {

        return jobBuilderFactory
                .get("controlarGatilhoJob")
                .start(controlarGatilhoStep)
                .incrementer(new RunIdIncrementer())
                .build();
    }
}
