package br.com.banestes.sgrs.registradorasusep.exclusao.batch;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ExclusaoJobConfig {

    private final JobBuilderFactory jobBuilderFactory;

    public ExclusaoJobConfig(JobBuilderFactory jobBuilderFactory) {
        this.jobBuilderFactory = jobBuilderFactory;
    }

    @Bean
    public Job exclusaoJob(Step exclusaoStep) {

        return jobBuilderFactory
                .get("exclusaoJob")
                .start(exclusaoStep)
                .incrementer(new RunIdIncrementer())
                .build();
    }

}
