package br.com.banestes.sgrs.registradorasusep.apolice.service;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.ContratoColetivoApoliceRepository;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.apolice.mapper.ApoliceMapper;
import br.com.banestes.sgrs.registradorasusep.apolice.repository.ParcelaApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.AutomovelApoliceService;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.IntermediarioService;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.ParteService;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.ApoliceDto;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Apolice;
import br.com.banestes.sgrs.registradorasusep.model.apolice.AutomovelApolice;

import java.util.List;

@Service
public class ConstrutorApoliceService {

    @Value("${grupoRamo:}")
    private String grupoRamo;
    private final ApoliceMapper apoliceMapper;
    private final ConstrutorObjetoSeguradoCompletoService construtorObjetoSeguradoCompletoService;
    private final IntermediarioService intermediarioService;
    private final ParteService parteService;
    private final AutomovelApoliceService automovelApoliceService;
    private final ParcelaApoliceRepository parcelaApoliceRepository;
    private final ContratoColetivoApoliceRepository contratoColetivoApoliceRepository;

    public ConstrutorApoliceService(ApoliceMapper apoliceMapper,
                                    ConstrutorObjetoSeguradoCompletoService construtorObjetoSeguradoCompletoService,
                                    IntermediarioService intermediarioService,
                                    ParteService parteService,
                                    AutomovelApoliceService automovelApoliceService,
                                    ParcelaApoliceRepository parcelaApoliceRepository,
                                    ContratoColetivoApoliceRepository contratoColetivoApoliceRepository) {

        this.apoliceMapper = apoliceMapper;
        this.construtorObjetoSeguradoCompletoService = construtorObjetoSeguradoCompletoService;
        this.intermediarioService = intermediarioService;
        this.parteService = parteService;
        this.automovelApoliceService = automovelApoliceService;
        this.parcelaApoliceRepository = parcelaApoliceRepository;
        this.contratoColetivoApoliceRepository = contratoColetivoApoliceRepository;
    }

    public ApoliceDto construir(Apolice apolice) {

        List<AutomovelApolice> automoveis = null;

        if ("AUTO".equals(grupoRamo))
            automoveis = automovelApoliceService.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice());

        return apoliceMapper.toDto(apolice,
                parteService.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice()),
                intermediarioService.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(),
                apolice.getIdtCtrApolice()),
                construtorObjetoSeguradoCompletoService.obterObjetosSeguradosCompletosApolice(apolice),
                automoveis,
                parcelaApoliceRepository.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice()),
                contratoColetivoApoliceRepository.findAllByidtCtrApoliceAndIdtCtlProc(apolice.getIdtCtrApolice(), apolice.getIdtCtlProc()));
    }
}
