package br.com.banestes.sgrs.registradorasusep.dto.complementar;

import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class FranquiaDto {

    private String franquiaTipo;
    private String tipoDescricao;
    private BigDecimal franquiaValor;
    private Integer franquiaPrazo;
    private String franquiaPeriodicidade;
    private String franquiaPeriodicidadeDias;
    private String franquiaDataInicio;
    private String franquiaDataTermino;
    private String franquiaDescricao;
    private String franquiaIndenizacaoIntegral;

}
