package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.BeneficiarioRepository;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Beneficiario;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Cobertura;

import java.util.List;

@Service
public class BeneficiarioService {
    private final BeneficiarioRepository beneficiarioRepository;

    public BeneficiarioService(BeneficiarioRepository beneficiarioRepository) {
        this.beneficiarioRepository = beneficiarioRepository;
    }

    public List<Beneficiario> findAllByIdtCtlProcAndIdtCtrObjeto(Cobertura cobertura) {
        return beneficiarioRepository.findAllByIdtCtlProcAndIdtCtrObjeto(cobertura.getIdtCtlProc(),
                cobertura.getIdtCtrObjeto());
    }

}
