package br.com.banestes.sgrs.registradorasusep.endosso.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.endosso.service.model.EndossoService;
import br.com.banestes.sgrs.registradorasusep.model.endosso.Endosso;
import br.com.banestes.sgrs.registradorasusep.service.EnvioSusepService;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class TransmissaoEndossoService {

    @Value("${numeroErrosPlataforma}")
    private Integer numeroErrosPlataforma;

    private final ConstrutorEndossoService construtorEndossoService;
    private final EndossoService endossoService;
    private final EnvioSusepService envioSusepService;

    public TransmissaoEndossoService(ConstrutorEndossoService construtorEndossoService, EndossoService endossoService, EnvioSusepService envioSusepService) {
        this.construtorEndossoService = construtorEndossoService;
        this.endossoService = endossoService;
        this.envioSusepService = envioSusepService;
    }

    public void transmitirEndossos(Integer idtCtlProc, Character idcOperacao) {
        log.info("Controle de Processamento: {}", idtCtlProc);
        if(idcOperacao.toString().equalsIgnoreCase("i")){
            log.info("Operação: INCLUSÃO");
            log.info("Leiaute Selecionado: Endosso");
        } else if (idcOperacao.toString().equalsIgnoreCase("a")) {
            log.info("Operação: ALTERACAO");
            log.info("Leiaute Selecionado: Endosso");
        }

        final List<Endosso> endossos = endossoService.listarEndossosTransmissao(idtCtlProc);

        Integer errorCount = 0;

        log.info("Número de endossos a serem processados: {} ", endossos.size());
        log.info("Processando Endossos...");

        for (Endosso endosso : endossos) {
            log.info("Endosso idt_eds_endosso: {} - contrato {}", endosso.getIdtEdsEndosso(), endosso.getNumContrato());
            if (!processaEndosso(endosso, idcOperacao)) {
                errorCount++;
                if (Objects.equals(errorCount, numeroErrosPlataforma)) {
                    log.info("Processamento interrompido no Endosso: {}", endosso.getIdtEdsEndosso());
                    return;
                }
            }
        }
        log.info("Os endossos foram processados com sucesso !");
    }

    private boolean processaEndosso(Endosso endosso, Character idcOperacao) {
        return endossoService.atualizarStatusTransmissao(endosso,
            envioSusepService.transmitir(
                construtorEndossoService.construir(endosso),
                idcOperacao.equals('I') ? "endosso" : "endosso/" + endosso.getIdentificadorRegistro(),
                idcOperacao.equals('I') ? HttpMethod.POST : HttpMethod.PUT
            ),
            idcOperacao
        );
    }

}
