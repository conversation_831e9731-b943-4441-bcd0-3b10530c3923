package br.com.banestes.sgrs.registradorasusep.dto.sinistro;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroBeneficiario;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class DadosBeneficiarioFinalDto {
    private String documento;
    private String tipoDocumento;
    private String nome;
    private DadosEnderecoBeneficiarioDto endereco;


    public DadosBeneficiarioFinalDto(SinistroBeneficiario sinistroBeneficiario) {
        this.documento = sinistroBeneficiario.getDocumento();
        this.tipoDocumento = sinistroBeneficiario.getTipoDocumento();
        this.nome = sinistroBeneficiario.getNome();
        this.endereco = new DadosEnderecoBeneficiarioDto(sinistroBeneficiario);
    }
}
