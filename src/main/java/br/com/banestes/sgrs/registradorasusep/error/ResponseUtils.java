package br.com.banestes.sgrs.registradorasusep.error;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class ResponseUtils {

    private static final Integer LIMIT_REGEX = 2;
    private static final Integer GENERIC_ERROR_CODE = 600;
    private final ObjectMapper objectMapper;

    public ResponseDto gerarResponseDto(HttpStatus status, String message) {
        try {

            if (message != null) {
                final String[] resposta = message.replace("\"{", "{").replace("}\"", "}").split(":", LIMIT_REGEX);
                if (resposta.length > 1) {
                    final ResponseDto dto = objectMapper.readValue(resposta[1], ResponseDto.class);
                    log.info("dtp {}", dto);
                    return dto;
                }
            }

            if (status == HttpStatus.CREATED || status == HttpStatus.OK || status == HttpStatus.NO_CONTENT) {
                return new ResponseDto(HttpStatus.OK.value(), "OK", "O registro foi registrado com sucesso.");
            }

        } catch (JsonProcessingException e) {
            log.info("Erro no processamento do json -> ", e);
        }

        return obterResponseDtoErroInesperada(message);
    }

    public ResponseDto gerarResponseDtoExclusao(HttpStatus status, String message, String endPoint) {
        try {

            if (message != null) {
                final String[] resposta = message.replace("\"{", "{").replace("}\"", "}").split(":", LIMIT_REGEX);
                if (resposta.length > 1) {
                    final ResponseDto dto = objectMapper.readValue(resposta[1], ResponseDto.class);
                    log.info("dtp {}", dto);
                    return dto;
                }
            }

            if (status == HttpStatus.NO_CONTENT || status == HttpStatus.OK) {
                return new ResponseDto(HttpStatus.NO_CONTENT.value(), "sucess", String.format("O registro de %s foi excluido com sucesso", endPoint));
            }

        } catch (JsonProcessingException e) {
            log.info("Erro no processamento do json -> ", e);
        }
        return obterResponseDtoErroInesperada(message);
    }

    private ResponseDto obterResponseDtoErroInesperada(String message) {
        final ResponseDto response = new ResponseDto();
        response.setCode(GENERIC_ERROR_CODE);
        response.setMessage("Resposta da MAPS não é um json de erro esperado: " + message);
        return response;
    }

}
