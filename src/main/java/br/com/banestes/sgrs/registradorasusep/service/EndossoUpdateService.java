package br.com.banestes.sgrs.registradorasusep.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.ControleEndosso;

import javax.sql.DataSource;
import java.sql.Timestamp;

@Slf4j
@Service

public class EndossoUpdateService {

    private final JdbcTemplate jdbcTemplateObject;
    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;

    public EndossoUpdateService(@Qualifier("sqlServerDataSource") DataSource dataSource, JdbcTemplate jdbcTemplateObject) {
        this.jdbcTemplateObject = jdbcTemplateObject;
        this.jdbcTemplateObject.setDataSource(dataSource);
    }

    public void atualizarSituacao(
        Long idtCtlEndosso,
        Integer idtCtlProc,
        String situacaoProcessamento,
        String mensagemErroProcessamento,
        Character idcOperacao
    ) {
        if ("E".equals(situacaoProcessamento)) {
            updateErro(idtCtlEndosso, idtCtlProc, mensagemErroProcessamento);
        } else if ("P".equals(situacaoProcessamento)) {
            updateErroPlataforma(idtCtlEndosso, idtCtlProc, mensagemErroProcessamento);
        } else {
            updateSucesso(idtCtlEndosso, idtCtlProc, idcOperacao);
        }
    }

    public void updateRegistroCorrecaoIngnorar(ControleEndosso controleEndosso) {
        String sql = "UPDATE SRO_CTL_ENDOSSO SET IDC_SIT_PROC = 'I' , DAT_HOR_ULT_ATUALIZACAO = ? WHERE IDT_CTL_ENDOSSO = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, new Timestamp(System.currentTimeMillis()), controleEndosso.getIdtCtlEndossoAtu());
        }
    }

    private void updateSucesso(Long idtCtlEndosso, Integer idtCtlProc, Character idcOperacao) {
//        log.info("Nao houve erro na transmissao estatus sendo atualizado para: 'R'");
        String sql = "update SRO_CTL_ENDOSSO set IDC_SIT_PROC = 'R', DES_MSG_PROC = '', DAT_HOR_ULT_ATUALIZACAO = ? where IDT_CTL_ENDOSSO = ? and IDT_CTL_PROC = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, new Timestamp(System.currentTimeMillis()), idtCtlEndosso, idtCtlProc);
            if (idcOperacao.equals('I')) {
                String sqlUpdateDatHorRegistro = "update SRO_CTL_ENDOSSO set DAT_HOR_REGISTRO = ? where IDT_CTL_ENDOSSO = ? and IDT_CTL_PROC = ?";
                jdbcTemplateObject.update(sqlUpdateDatHorRegistro, new Timestamp(System.currentTimeMillis()), idtCtlEndosso, idtCtlProc);
            }
        }
    }

    private void updateErro(Long idtCtlEndosso, Integer idtCtlProc, String mensagemErroProcessamento) {
//        log.info("Houve erro na transmissao estatus sendo atualizado para: 'E' mensagem: {}", mensagemErroProcessamento);
        String sql = "update SRO_CTL_ENDOSSO set IDC_SIT_PROC = 'E', DES_MSG_PROC = ?, DAT_HOR_ULT_ATUALIZACAO = ? where IDT_CTL_ENDOSSO = ? and IDT_CTL_PROC = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, mensagemErroProcessamento, new Timestamp(System.currentTimeMillis()), idtCtlEndosso, idtCtlProc);
        }
    }

    private void updateErroPlataforma(Long idtCtlEndosso, Integer idtCtlProc, String mensagemErroProcessamento) {
//        log.info("Houve erro de plataforma na transmissao estatus sendo atualizado para: 'P' mensagem: {}", mensagemErroProcessamento);
        String sql = "update SRO_CTL_ENDOSSO set IDC_SIT_PROC = 'P', DES_MSG_PROC = ?, DAT_HOR_ULT_ATUALIZACAO = ? where IDT_CTL_ENDOSSO = ? and IDT_CTL_PROC = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, mensagemErroProcessamento, new Timestamp(System.currentTimeMillis()), idtCtlEndosso, idtCtlProc);
        }
    }

    public void updateExclusaoRegistro(Long idtCtlApolice, Integer numeroMatr, String situacao) {
        String sql = "update SRO_CTL_ENDOSSO set IDC_SIT_PROC = ?, DAT_HOR_ULT_ATUALIZACAO = ?, NUM_MATR_ULT_ATUALIZACAO = ? where IDT_CTL_ENDOSSO = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, situacao, new Timestamp(System.currentTimeMillis()), numeroMatr, idtCtlApolice);
        }
    }

}
