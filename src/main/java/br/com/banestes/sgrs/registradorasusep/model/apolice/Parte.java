package br.com.banestes.sgrs.registradorasusep.model.apolice;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_CTR_PARTE")
public class Parte {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTR_PARTE")
    private Long idtCtrParte;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "DOCUMENTO")
    private String documento;
    @Column(name = "BAIRRO")
    private String bairro;
    @Column(name = "IDT_CTR_APOLICE")
    private Long idtCtrApolice;
    @Column(name = "TIPO_DOCUMENTO")
    private String tipoDocumento;
    @Column(name = "PAIS")
    private String pais;
    @Column(name = "NOME")
    private String nome;
    @Column(name = "COMPLEMENTO")
    private String complemento;
    @Column(name = "EMAIL")
    private String email;
    @Column(name = "CEP")
    private String cep;
    @Column(name = "ENDERECO")
    private String endereco;
    @Column(name = "CIDADE")
    private String cidade;
    @Column(name = "NUMERO")
    private String numero;
    @Column(name = "DATA_NASCIMENTO")
    private String dataNascimento;
    @Column(name = "UF")
    private String uf;
    @Column(name = "TIPO_PARTE")
    private String tipoParte;
    @Column(name = "SEXO_SEGURADO_PARTICIPANTE")
    private String sexoSeguradoParticipante;
}
