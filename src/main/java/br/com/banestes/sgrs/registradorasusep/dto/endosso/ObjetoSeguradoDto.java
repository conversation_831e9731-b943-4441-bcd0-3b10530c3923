package br.com.banestes.sgrs.registradorasusep.dto.endosso;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.List;

import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.IdentificacaoAdicionalDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.ObjetoAeronauticoDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.ObjetoMaritimoDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.ObjetoPatrimonialDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.ObjetoResponsabilidadeDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.ObjetoRuralDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.cobertura.CoberturaDto;

@Getter
@Setter
@ToString
public class ObjetoSeguradoDto {
    private String codigo;
    private String tipo;
    private String descricaoTipo;
    private String descricaoObjeto;
    private Double valor;
    private Double valorReal;
    private String dataInicio;
    private String dataTermino;
    private String localRisco;
    private List<CoberturaDto> coberturas;
    private List<IdentificacaoAdicionalDto> identificacoesAdicionais;
    private List<ObjetoMaritimoDto> objetosMaritimos;
    private List<ObjetoAeronauticoDto> objetosAeronauticos;
    private List<ObjetoResponsabilidadeDto> objetosResponsabilidades;
    private List<ObjetoRuralDto> objetosRurais;
    private List<RamoPessoaDto> ramosPessoas;
    private List<ObjetoPatrimonialDto> objetosPatrimoniais;
}
