package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.AutomovelApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.model.apolice.AutomovelApolice;

import java.util.List;

@Service
public class AutomovelApoliceService {

    private final AutomovelApoliceRepository automovelApoliceRepository;

    public AutomovelApoliceService(AutomovelApoliceRepository automovelApoliceRepository) {
        this.automovelApoliceRepository = automovelApoliceRepository;
    }

    public List<AutomovelApolice> findAllByIdtCtlProcAndIdtCtrApolice(Integer idtCtlProc, Long idtCtrApolice) {
        return automovelApoliceRepository.findAllByIdtCtlProcAndIdtCtrApolice(idtCtlProc, idtCtrApolice);
    }

}
