package br.com.banestes.sgrs.registradorasusep.helper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Configuration
public class BatchWarmup {
    JobBuilderFactory jobBuilderFactory;
    StepBuilderFactory stepBuilderFactory;
    RestTemplate restTemplate;

    public BatchWarmup(
            JobBuilderFactory jobBuilderFactory,
            StepBuilderFactory stepBuilderFactory,
            RestTemplate restTemplate
    ) {
        this.jobBuilderFactory = jobBuilderFactory;
        this.stepBuilderFactory = stepBuilderFactory;
        this.restTemplate = restTemplate;
    }

    //Metodo para "acordar" a API antes do processamento de leiautes Apólices
    @Bean
    public Tasklet platformWarmupTasklet() {
        return (contribution, chunkContext) -> {
            try {
                sendWarmupRequest("/health");

                Thread.sleep(2000);

                sendWarmupRequest("/health");

                return RepeatStatus.FINISHED;
            } catch (Exception e) {
                chunkContext.getStepContext()
                        .getStepExecution()
                        .getExecutionContext()
                        .put("warmupError", e.getMessage());
                throw e;
            }
        };
    }
    private void sendWarmupRequest(String endpoint) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> request = new HttpEntity<>( headers);

            restTemplate.exchange(
                    "http://localhost:8081" + endpoint,
                    HttpMethod.GET,
                    request,
                    String.class
            );

            log.info("Health check realizado com sucesso");
        } catch (Exception e) {
            System.err.println("Erro no warmup: " + e.getMessage());
        }
    }

    @Bean
    public Step warmupBatchStep() {
        return stepBuilderFactory.get("warmupBatchStep")
                .tasklet(platformWarmupTasklet())
                .build();
    }
}
