package br.com.banestes.sgrs.registradorasusep.movimentosinistro.mapper;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import br.com.banestes.sgrs.registradorasusep.dto.movimentosinistro.MovimentoSinistroAdicionalDto;
import br.com.banestes.sgrs.registradorasusep.dto.movimentosinistro.MovimentoSinistroDto;
import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistroAdicional;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class MovimentoSinistroMapper {

    public MovimentoSinistroDto toDto(MovimentoSinistro movimentoSinistro,
                                      List<MovimentoSinistroAdicional> adicionaisSinistros) {
        final MovimentoSinistroDto movimentoSinistroDto = new MovimentoSinistroDto();

        movimentoSinistroDto.setIdentificadorRegistro(movimentoSinistro.getIdentificadorRegistro());
        movimentoSinistroDto.setIdentificadorMovimento(movimentoSinistro.getIdentificadorMovimento());
        movimentoSinistroDto.setCodigoSeguradora(movimentoSinistro.getCodigoSeguradora());
        movimentoSinistroDto.setCodigoSinistro(movimentoSinistro.getCodigoSinistro());
        movimentoSinistroDto.setDataMovimento(movimentoSinistro.getDataMovimento());
        movimentoSinistroDto.setTipoMovimento(movimentoSinistro.getTipoMovimento());
        movimentoSinistroDto.setTipoOperacaoSinistro(movimentoSinistro.getTipoOperacaoSinistro());
        movimentoSinistroDto.setOrigem(movimentoSinistro.getOrigem());
        movimentoSinistroDto.setTipoSinistro(movimentoSinistro.getTipoSinistro());
        movimentoSinistroDto.setCodigoContraparte(movimentoSinistro.getCodigoContraparte());
        movimentoSinistroDto.setDocumento(movimentoSinistro.getDocumento());
        movimentoSinistroDto.setTipoDocumento(movimentoSinistro.getTipoDocumento());
        movimentoSinistroDto.setRazaoSocial(movimentoSinistro.getRazaoSocial());
        movimentoSinistroDto.setCodigoInstituicao(movimentoSinistro.getCodigoInstituicao());
        movimentoSinistroDto.setMeioPagamento(movimentoSinistro.getMeioPagamento());
        movimentoSinistroDto.setTipoPagamento(movimentoSinistro.getTipoPagamento());
        movimentoSinistroDto.setSinistroTipoPagamentoOutros(movimentoSinistro.getSinistroTipoPagamentoOutros());
        movimentoSinistroDto.setMoeda(movimentoSinistro.getMoeda());
        movimentoSinistroDto.setValorMovimento(movimentoSinistro.getValorMovimento());
        movimentoSinistroDto.setValorMovimentoReal(movimentoSinistro.getValorMovimentoReal());
        movimentoSinistroDto.setGrupo(movimentoSinistro.getGrupo());
        movimentoSinistroDto.setRamo(movimentoSinistro.getRamo());
        movimentoSinistroDto.setApoliceCodigo(movimentoSinistro.getApoliceCodigo());
        movimentoSinistroDto.setCertificadoCodigo(movimentoSinistro.getCertificadoCodigo());
        movimentoSinistroDto.setNumeroEndosso(movimentoSinistro.getNumeroEndosso());
        movimentoSinistroDto.setCodigoSindicato(movimentoSinistro.getCodigoSindicato());
        movimentoSinistroDto.setCodigoContrato(movimentoSinistro.getCodigoContrato());
        movimentoSinistroDto.setFaixaContrato(movimentoSinistro.getFaixaContrato());
        movimentoSinistroDto.setNumEndossoResseguro(movimentoSinistro.getNumEndossoResseguro());

        if (CollectionUtils.isNotEmpty(adicionaisSinistros)) {
            movimentoSinistroDto.setAdicionaisDespesa(
                    adicionaisSinistros.stream().map(MovimentoSinistroAdicionalDto::new).collect(Collectors.toList()));
        }


        return movimentoSinistroDto;
    }

}
