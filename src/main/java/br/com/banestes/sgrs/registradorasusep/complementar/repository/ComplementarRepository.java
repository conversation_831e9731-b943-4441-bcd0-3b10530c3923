package br.com.banestes.sgrs.registradorasusep.complementar.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.complementar.Complementar;

import java.util.List;

@Repository
public interface ComplementarRepository extends JpaRepository<Complementar, Integer> {

    @Query("SELECT a"
            + " FROM Complementar a"
            + " INNER JOIN ControleComplementarAuto c"
            + " ON a.idtCmpAuto = c.idtCtlCmpAuto"
            + " WHERE (c.idtCtlProc = :idtCtlProc)"
            + " AND (c.idcSitProc = 'S' OR c.idcSitProc = 'P')"
            + " AND ((:complemento = 'CTR' AND c.numEndosso = 0) OR (:complemento = 'EDS' AND c.numEndosso > 0))")
    List<Complementar> listarComplementarTransmissao(@Param("idtCtlProc") Integer idtCtlProc, @Param("complemento") String complemento);
}
