package br.com.banestes.sgrs.registradorasusep.model.endosso;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_EDS_INTERMEDIARIO")
public class IntermediarioEndosso {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_EDS_INTERMEDIARIO")
    private Long idtEdsIntermediario;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_EDS_ENDOSSO")
    private Long idtEdsEndosso;
    @Column(name = "DOCUMENTO")
    private String documento;
    @Column(name = "TIPO")
    private String tipo;
    @Column(name = "CODIGO")
    private String codigo;
    @Column(name = "TIPO_DOCUMENTO")
    private String tipoDocumento;
    @Column(name = "VALOR_COMISSAO_REAL")
    private Double valorComissaoReal;
    @Column(name = "NOME")
    private String nome;
    @Column(name = "VALOR_COMISSAO")
    private Double valorComissao;
    @Column(name = "EMAIL")
    private String email;
    @Column(name = "NUMERO")
    private String numero;
    @Column(name = "COMPLEMENTO")
    private String complemento;
    @Column(name = "ENDERECO")
    private String endereco;
    @Column(name = "CEP")
    private String cep;
    @Column(name = "PAIS")
    private String pais;
    @Column(name = "UF")
    private String uf;
    @Column(name = "CIDADE")
    private String cidade;
    @Column(name = "BAIRRO")
    private String bairro;


}

