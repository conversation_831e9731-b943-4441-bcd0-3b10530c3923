package br.com.banestes.sgrs.registradorasusep.endosso.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.endosso.repository.AutomovelEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.model.endosso.AutomovelEndosso;

import java.util.List;

@Service
public class AutomovelEndossoService {

    private final AutomovelEndossoRepository automovelEndossoRepository;

    public AutomovelEndossoService(AutomovelEndossoRepository automovelEndossoRepository) {
        this.automovelEndossoRepository = automovelEndossoRepository;
    }

    public List<AutomovelEndosso> findAllByIdtCtlProcAndIdtEdsEndosso(Integer idtCtlProc, Long idtEdsEndosso) {
        return automovelEndossoRepository.findAllByIdtCtlProcAndIdtEdsEndosso(idtCtlProc, idtEdsEndosso);
    }

}
