package br.com.banestes.sgrs.registradorasusep.model.sinistro;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_SNT_PESSOA")
public class SinistroPessoa {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_SNT_PESSOA")
    private Long idtSntPessoa;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_SNT_SINISTRO")
    private Long idtSntSinistro;
    @Column(name = "SEQ_VITIMA")
    private Integer seqVitima;
    @Column(name = "DOCUMENTO")
    private String documento;
    @Column(name = "TIPO_DOCUMENTO")
    private String tipoDocumento;
    @Column(name = "NOME")
    private String nome;
}
