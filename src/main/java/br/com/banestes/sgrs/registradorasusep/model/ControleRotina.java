package br.com.banestes.sgrs.registradorasusep.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Getter
@Setter
@ToString
@Entity
@Table(name = "SRO_CTL_ROTINA")
public class ControleRotina {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTL_RTN")
    private Integer idtCtlRtn;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "NUM_RTN_PROC")
    private Short numRtnProc;
    @Column(name = "DES_RTN_PROC")
    private String desRtnProc;
    @Column(name = "IDC_SIT_PROC")
    private Character idcSitProc;
    @Column(name = "DES_MSG_PROC")
    private String desMsgProc;
    @Column(name = "NUM_MATR_ULT_ATUALIZACAO")
    private Double numMatrUltAtualizacao;
    @Temporal(value = TemporalType.TIMESTAMP)
    @Column(name = "DAT_HOR_ULT_ATUALIZACAO")
    private Date datHorUltAtualizacao;
    @Column(name = "IDC_OPERACAO")
    private Character idcOperacao;
}
