package br.com.banestes.sgrs.registradorasusep.model.apolice;

import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@ToString
@Table(name = "SRO_CTR_COLETIVO")
public class ContratoColetivoApolice
{
    @Id
    @Column(name = "IDT_CTR_COLETIVO")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idtCtrColetivo;

    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;

    @Column(name = "IDT_CTR_APOLICE", insertable = false, updatable = false)
    private Long idtCtrApolice;

    @Column(name = "TIPO_PLANO")
    private String tipoPlano;
}

