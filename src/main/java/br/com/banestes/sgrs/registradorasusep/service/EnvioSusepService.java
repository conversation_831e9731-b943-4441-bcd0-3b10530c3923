package br.com.banestes.sgrs.registradorasusep.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.error.ResponseUtils;

@Slf4j
@Service
public class EnvioSusepService {

    private final RestTemplate restTemplate;
    private final String apiBaseUrl;
    private final KeycloakService keycloakService;
    private final ResponseUtils responseUtils;
    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;
    @Autowired
    private ObjectMapper objectMapper;

    public ResponseDto transmitir(Object data, String endpoint, HttpMethod method) {
        if (!modoSimulacao) {
            try {
                final HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                httpHeaders.set("Authorization", "Bearer " + keycloakService.getAccessToken(""));

                final HttpEntity<?> request = new HttpEntity<>(data, httpHeaders);
                final ResponseEntity<String> retorno = restTemplate.exchange(String.format("%s/%s", apiBaseUrl, endpoint), method, request, String.class);
                return responseUtils.gerarResponseDto(retorno.getStatusCode(), retorno.getBody());

            } catch (RestClientException e) {
                log.info("Erro na trasnmissão - mensagem: ", e);
                return responseUtils.gerarResponseDto(null, e.getMessage());
            }
        }
        else {
            try {
                log.info("{}", objectMapper.writeValueAsString(data));
            } catch (JsonProcessingException e){

            };
            return responseUtils.gerarResponseDto(HttpStatus.OK, null);
        }
    }

    public EnvioSusepService(RestTemplate restTemplate,
                             @Value("${api.baseurl}") String apiBaseUrl,
                             KeycloakService keycloakService,
                             ResponseUtils responseUtils) {

        this.apiBaseUrl = apiBaseUrl;
        this.restTemplate = restTemplate;
        this.keycloakService = keycloakService;
        this.responseUtils = responseUtils;
    }

}
