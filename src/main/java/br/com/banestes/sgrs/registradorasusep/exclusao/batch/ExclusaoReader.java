package br.com.banestes.sgrs.registradorasusep.exclusao.batch;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.batch.item.database.builder.JdbcCursorItemReaderBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import br.com.banestes.sgrs.registradorasusep.exclusao.query.Query;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoCadastro;

import javax.sql.DataSource;

@Slf4j
@Configuration
public class ExclusaoReader {

    @Bean
    @StepScope
    public JdbcCursorItemReader<ExclusaoCadastro> exclusaoItemReader(@Qualifier("sqlServerDataSource") DataSource dataSource) {

        log.info("Iniciando Exlusão Layout");
        return new JdbcCursorItemReaderBuilder<ExclusaoCadastro>()
                .name("exclusaoItemReader")
                .dataSource(dataSource)
                .sql(Query.queryForFindRegistrosExclusao())
                .rowMapper(new BeanPropertyRowMapper<ExclusaoCadastro>(ExclusaoCadastro.class))
                .build();
    }


}
