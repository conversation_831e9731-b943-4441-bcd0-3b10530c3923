package br.com.banestes.sgrs.registradorasusep.dto.complementar;

import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class ComplementarCoberturaDto {

    private String grupo;
    private String ramo;
    private String codigo;
    private String outrasDescricao;
    private String coberturaInternaSeguradora;
    private String numeroProcesso;
    private BigDecimal limiteMaximoIndenizacao;
    private BigDecimal limiteMaximoIndenizacaoReal;
    private String dataInicio;
    private String dataTermino;
    private String indiceAtualizacao;
    private Integer periodicidadeAtualizacao;
    private String periodicidadeUnidade;
    private String coberturaPrincipal;
    private String coberturaCaracteristica;
    private String coberturaTipo;
    private Integer carenciaPeriodo;
    private String carenciaPeriodicidade;
    private String carenciaPeriodicidadeDias;
    private String carenciaDataInicio;
    private String carenciaDataTermino;
    private BigDecimal valorPremio;
    private BigDecimal valorPremioReal;
    private BigDecimal iof;
    private BigDecimal custo;
    private BigDecimal custoReal;
    private String tipoIndenizacao;
    private BigDecimal percentualIndenizacaoParcial;
    private BigDecimal percentualLmi;
    private String diasCobertura;
    private String coberturaVinculada;
    private List<FranquiaDto> franquias;
    private List<PosDto> pos;
    private String periodicidadePremio;

}
