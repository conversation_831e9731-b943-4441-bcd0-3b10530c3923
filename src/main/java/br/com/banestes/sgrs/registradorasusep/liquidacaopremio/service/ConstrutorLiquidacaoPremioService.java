package br.com.banestes.sgrs.registradorasusep.liquidacaopremio.service;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.dto.premio.LiquidacaoPremioDto;
import br.com.banestes.sgrs.registradorasusep.liquidacaopremio.mapper.LiquidacaoPremioMapper;
import br.com.banestes.sgrs.registradorasusep.model.premio.Premio;

@Service

public class ConstrutorLiquidacaoPremioService {
    private final LiquidacaoPremioMapper liquidacaoPremioMapper;

    public ConstrutorLiquidacaoPremioService(LiquidacaoPremioMapper liquidacaoPremioMapper) {
        this.liquidacaoPremioMapper = liquidacaoPremioMapper;
    }

    public LiquidacaoPremioDto construir(Premio premio) {
        return liquidacaoPremioMapper.toDto(premio);
    }

}
