package br.com.banestes.sgrs.registradorasusep.liquidacaopremio.batch;

import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LiquidacaoPremioStepConfig {

    private final StepBuilderFactory stepBuilderFactory;

    public LiquidacaoPremioStepConfig(StepBuilderFactory stepBuilderFactory) {
        this.stepBuilderFactory = stepBuilderFactory;
    }

    @Bean
    public Step liquidacaoPremioStep(
            ItemReader<Integer> liquidacaoPremioItemReader,
            @Qualifier("liquidacaoPremioProcessor") ItemProcessor<Integer, Integer> liquidacaoPremioItemProcessor,
            ItemWriter<Integer> liquidacaoPremioItemWriter) {

        return stepBuilderFactory
                .get("liquidacaoPremioStep")
                .<Integer, Integer>chunk(1)
                .reader(liquidacaoPremioItemReader)
                .processor(liquidacaoPremioItemProcessor)
                .writer(liquidacaoPremioItemWriter)
                .build();
    }
}
