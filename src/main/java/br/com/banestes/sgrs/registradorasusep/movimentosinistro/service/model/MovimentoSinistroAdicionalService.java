package br.com.banestes.sgrs.registradorasusep.movimentosinistro.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistroAdicional;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.repository.MovimentoSinistroAdicionalRepository;

import java.util.List;

@Service
public class MovimentoSinistroAdicionalService {
    private final MovimentoSinistroAdicionalRepository movimentoSinistroAdicionalRepository;

    public MovimentoSinistroAdicionalService(MovimentoSinistroAdicionalRepository movimentoSinistroAdicionalRepository) {
        this.movimentoSinistroAdicionalRepository = movimentoSinistroAdicionalRepository;
    }

    public List<MovimentoSinistroAdicional> getAllByIdtCtlProcAndIdtSntMovSinistro(Integer idtCtlProc, Long idtSntMovSinistro) {
        return movimentoSinistroAdicionalRepository.getAllByIdtCtlProcAndIdtSntMovSinistro(idtCtlProc, idtSntMovSinistro);
    }

}
