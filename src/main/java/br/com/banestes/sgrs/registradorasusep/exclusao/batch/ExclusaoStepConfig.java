package br.com.banestes.sgrs.registradorasusep.exclusao.batch;

import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoCadastro;

@Configuration
@RequiredArgsConstructor
public class ExclusaoStepConfig {

    private final StepBuilderFactory stepBuilderFactory;

    @Bean
    public Step exclusaoStep(
            ItemReader<ExclusaoCadastro> exclusaoItemReader,
            @Qualifier("exclusaoProcessor") ItemProcessor<ExclusaoCadastro, Integer> exclusaoItemProcessor,
            ItemWriter<Integer> exclusaoItemWriter) {

        return stepBuilderFactory
                .get("exclusaoStep")
                .<ExclusaoCadastro, Integer>chunk(1)
                .reader(exclusaoItemReader)
                .processor(exclusaoItemProcessor)
                .writer(exclusaoItemWriter)
                .build();
    }

}
