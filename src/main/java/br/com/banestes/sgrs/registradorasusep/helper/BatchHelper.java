package br.com.banestes.sgrs.registradorasusep.helper;

import br.com.banestes.sgrs.registradorasusep.constants.Constants;
import br.com.banestes.sgrs.registradorasusep.exception.RotinaException;
import lombok.extern.slf4j.Slf4j;
import java.sql.ResultSet;
import java.sql.SQLException;

@Slf4j
public final class BatchHelper {

    private static final String IDENTIFICADOR_SUCESSO = "LISTA";

    private BatchHelper() {
        throw new IllegalStateException("Utility class");
    }

    public static Integer extrairRetornoStoredProcedeure(ResultSet rs) {
        Integer codigoRetorno = 0;
        try {
            final String retorno = rs.getString(1);

            if (retorno.contains("ERRO")) {
                log.error("Retorno da Procedure com erro: {}", retorno);
                codigoRetorno = 0;
            }


            if (retorno.contains("OK") && !retorno.contains(IDENTIFICADOR_SUCESSO)) {
                log.warn("Retorno da Procedure: {}", retorno);
                codigoRetorno = -1;
            }

            if (retorno.contains(IDENTIFICADOR_SUCESSO)) {
                log.warn("Retorno da Procedure: {}", retorno);
                codigoRetorno = Integer.valueOf(rs.getString(1).split(" ")[1]);
            }

        } catch (SQLException e) {
            log.error("Exceção ao avaliar o retorno da Procedure: ", e);
        }
        return codigoRetorno;
    }

    public static Integer extrairRetornoStoredProcedureLiberar(ResultSet rs) throws SQLException, RotinaException {
        final String retorno = rs.getString(1);

        if (retorno.contains("ERRO")) {
            log.error("Retorno da Procedure com erro: {}", retorno);
            throw new RotinaException(retorno);
        }

        log.info("Retorno da Procedure: {}", retorno);
        return 0;
    }

    public static Integer extrairRetornoStoredProcedureControlarGatilho(ResultSet rs) throws RotinaException, SQLException {
        final String retorno = rs.getString(1);

        log.info("Retorno da Procedure: {}", retorno);

        if (retorno.startsWith("REMOVER")) {
            return Constants.ACAO_REMOVER;
        }

        if (retorno.startsWith("GERAR")) {
            return Constants.ACAO_GERAR;
        }
        throw new RotinaException(retorno);
    }
}
