package br.com.banestes.sgrs.registradorasusep.service;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.ControleMovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.repository.ControleMovimentoSinistroRepository;

import java.util.Optional;

@Service
public class ControleMovimentoSinistroService {
    private final ControleMovimentoSinistroRepository controleMovimentoSinistroRepository;
    private final MovimentoSinistroUpdateService movimentoSinistroUpdateService;

    public ControleMovimentoSinistroService(ControleMovimentoSinistroRepository controleMovimentoSinistroRepository,
                                            MovimentoSinistroUpdateService movimentoSinistroUpdateService) {

        this.controleMovimentoSinistroRepository = controleMovimentoSinistroRepository;
        this.movimentoSinistroUpdateService = movimentoSinistroUpdateService;
    }

    public void atualizar(Long idtSntMovSinistro, String situacaoProcessamento, String mensagemErroProcessamento, Character idcOperacao) {
        final Optional<ControleMovimentoSinistro> controleMovimentoSinistroOpt = controleMovimentoSinistroRepository
                .findById(idtSntMovSinistro);

        if (controleMovimentoSinistroOpt.isPresent()) {
            final ControleMovimentoSinistro controleMovimentoSinistro = controleMovimentoSinistroOpt.get();

            movimentoSinistroUpdateService.atualizarSituacao(
                controleMovimentoSinistro.getIdtCtlMovSinistro(),
                controleMovimentoSinistro.getIdtCtlProc(),
                situacaoProcessamento,
                mensagemErroProcessamento,
                idcOperacao
            );

            if (("R".equals(situacaoProcessamento) || "E".equals(situacaoProcessamento)) &&
                    (controleMovimentoSinistro.getIdtCtlMovSinistroAtu() != null && controleMovimentoSinistro.getIdtCtlMovSinistroAtu() > 0)) {
                movimentoSinistroUpdateService.updateRegistroCorrecaoIngnorar(controleMovimentoSinistro);
            }

        }
    }

    public void atualizarRegistroExclusao(ExclusaoLayout exclusaoLayout, String situacaoProcessamento) {
        final ControleMovimentoSinistro controleMovimentoSinistro = controleMovimentoSinistroRepository.findById(exclusaoLayout.getIdtLeiaute()).orElse(null);

        if (controleMovimentoSinistro != null) {
            movimentoSinistroUpdateService.updateExclusaoRegistro(controleMovimentoSinistro.getIdtCtlMovSinistro(),
                    exclusaoLayout.getNumMatrUltAtualizacao(),
                    situacaoProcessamento);
        }
    }

}
