package br.com.banestes.sgrs.registradorasusep.dto.sinistro;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroEvento;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DadoEventoDto {
    private String sinistroDescricaoAeroportoOperacao;
    private String especificacaoAeroporto;
    private String sinistroDescricaoEspecificacaoAeroporto;
    private String sinistroDescricaoEvento;
    private String sinistroDescricaoDanos;
    private String codigoObjeto;
    private String grupo;
    private String ramo;

    public DadoEventoDto(SinistroEvento sinistroEvento) {
        this.sinistroDescricaoAeroportoOperacao = sinistroEvento.getSinistroDescricaoAeroportoOperacao();
        this.especificacaoAeroporto = sinistroEvento.getEspecificacaoAeroporto();
        this.sinistroDescricaoEspecificacaoAeroporto = sinistroEvento.getSinistroDescricaoEspecificacaoAeroporto();
        this.sinistroDescricaoEvento = sinistroEvento.getSinistroDescricaoEvento();
        this.sinistroDescricaoDanos = sinistroEvento.getSinistroDescricaoDanos();
        this.codigoObjeto = sinistroEvento.getCodigoObjeto();
        this.grupo = sinistroEvento.getGrupo();
        this.ramo = sinistroEvento.getRamo();
    }
}
