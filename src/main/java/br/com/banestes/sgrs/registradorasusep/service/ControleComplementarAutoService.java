package br.com.banestes.sgrs.registradorasusep.service;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.ControleComplementarAuto;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.repository.ControleComplementarAutoRepository;

import java.util.Optional;

@Service
public class ControleComplementarAutoService {

    private final ControleComplementarAutoRepository controleComplementarAutoRepository;
    private final ControleComplementarAutoUpdateService complementarAutoUpdateService;

    public ControleComplementarAutoService(ControleComplementarAutoRepository controleComplementarAutoRepository,
                                           ControleComplementarAutoUpdateService complementarAutoUpdateService) {

        this.controleComplementarAutoRepository = controleComplementarAutoRepository;
        this.complementarAutoUpdateService = complementarAutoUpdateService;
    }

    public void atualizar(Long idtCtrApolice, String situacaoProcessamento, String mensagemErroProcessamento, Character idcOperacao) {
        final Optional<ControleComplementarAuto> controleComplementarAutoOpt = controleComplementarAutoRepository.findById(idtCtrApolice);

        if (controleComplementarAutoOpt.isPresent()) {
            final ControleComplementarAuto controleComplementarAuto = controleComplementarAutoOpt.get();
            complementarAutoUpdateService.atualizarSituacao(controleComplementarAuto.getIdtCtlCmpAuto(), controleComplementarAuto.getIdtCtlProc(),
                    situacaoProcessamento, mensagemErroProcessamento, idcOperacao);

            if (("R".equals(situacaoProcessamento) || "E".equals(situacaoProcessamento)) &&
                    (controleComplementarAuto.getIdtCtlCmpAutoAtu() != null && controleComplementarAuto.getIdtCtlCmpAutoAtu() > 0)) {
                complementarAutoUpdateService.updateRegistroCorrecaoIngnorar(controleComplementarAuto);
            }
        }
    }

    public void atualizarRegistroExclusao(ExclusaoLayout exclusaoLayout, String situacaoProcessamento) {
        final ControleComplementarAuto controleComplementarAuto = controleComplementarAutoRepository.findById(exclusaoLayout.getIdtLeiaute()).orElse(null);
        if (controleComplementarAuto != null) {
            complementarAutoUpdateService.updateExclusaoRegistro(controleComplementarAuto.getIdtCtlCmpAuto(), exclusaoLayout.getNumMatrUltAtualizacao(), situacaoProcessamento);
        }
    }

}
