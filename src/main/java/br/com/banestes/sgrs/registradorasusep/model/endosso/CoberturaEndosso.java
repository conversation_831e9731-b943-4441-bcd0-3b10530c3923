package br.com.banestes.sgrs.registradorasusep.model.endosso;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_EDS_COBERTURA")
public class CoberturaEndosso {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_EDS_COBERTURA")
    private Long idtEdsCobertura;
    @Column(name = "CODIGO")
    private String codigo;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "COD_COBERTURA")
    private Integer codCobertura;
    @Column(name = "COBERTURA_INTERNA_SEGURADORA")
    private String coberturaInternaSeguradora;
    @Column(name = "GRUPO")
    private String grupo;
    @Column(name = "RAMO")
    private String ramo;
    @Column(name = "DATA_TERMINO_COBERTURA")
    private String dataTerminoCobertura;
    @Column(name = "OUTRAS_DESCRICAO")
    private String outrasDescricao;
    @Column(name = "NUMERO_PROCESSO")
    private String numeroProcesso;
    @Column(name = "IDT_EDS_OBJETO")
    private Long idtEdsObjeto;
    @Column(name = "LIMITE_MAXIMO_INDENIZACAO")
    private Double limiteMaximoIndenizacao;
    @Column(name = "PERIODICIDADE_UNIDADE")
    private String periodicidadeUnidade;
    @Column(name = "LIMITE_MAXIMO_INDENIZACAO_REAL")
    private Double limiteMaximoIndenizacaoReal;
    @Column(name = "DATA_INICIO_COBERTURA")
    private String dataInicioCobertura;
    @Column(name = "LIMITE_MAXIMO_INDENIZACAO_SUBLIMITE")
    private String limiteMaximoIndenizacaoSublimite;
    @Column(name = "INDICE_ATUALIZACAO")
    private String indiceAtualizacao;
    @Column(name = "PERIODICIDADE_ATUALIZACAO")
    private Integer periodicidadeAtualizacao;
    @Column(name = "PERIODICIDADE_PREMIO")
    private String periodicidadePremio;
    @Column(name = "DESCRICAO_PERIODICIDADE")
    private String descricacaoPeriodicidade;
    @Column(name = "CARENCIA_DATA_TERMINO")
    private String carenciaDataTermino;
    @Column(name = "COBERTURA_PRINCIPAL")
    private String coberturaPrincipal;
    @Column(name = "COBERTURA_CARACTERISTICA")
    private String coberturaCaracteristica;
    @Column(name = "COBERTURA_TIPO")
    private String coberturaTipos;
    @Column(name = "CUSTO_REAL")
    private Double custoReal;
    @Column(name = "CARENCIA_PERIODO")
    private Integer carenciaPeriodo;
    @Column(name = "CARENCIA_PERIODICIDADE")
    private String carenciaPeriodicidade;
    @Column(name = "TIPO_RISCO")
    private String tipoRisco;
    @Column(name = "CARENCIA_PERIODICIDADE_DIAS")
    private String carenciaPeriodicidadeDias;
    @Column(name = "DATA_INICIO_PREMIO")
    private String dataInicioPremio;
    @Column(name = "CARENCIA_DATA_INICIO")
    private String carenciaDataInicio;
    @Column(name = "DATA_TERMINO_PREMIO")
    private String dataTerminoPremio;
    @Column(name = "VALOR_PREMIO_REAL")
    private Double valorPremioReal;
    @Column(name = "VALOR_PREMIO")
    private Double valorPremio;
    @Column(name = "CUSTO")
    private Double custo;
    @Column(name = "IOF")
    private Double iof;

}
