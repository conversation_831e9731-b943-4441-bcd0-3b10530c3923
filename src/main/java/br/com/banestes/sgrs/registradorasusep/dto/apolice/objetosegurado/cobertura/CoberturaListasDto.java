package br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.cobertura;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.List;

@Getter
@Setter
@ToString
public class CoberturaListasDto {
    private List<FranquiaDto> franquias;
    private List<PosDto> pos;
    private List<BeneficiarioDto> beneficiarios;
    private List<AdicionalCoberturaDto> adicionaisCoberturas;
}
