package br.com.banestes.sgrs.registradorasusep.apolice.mapper;

import br.com.banestes.sgrs.registradorasusep.dto.apolice.*;
import br.com.banestes.sgrs.registradorasusep.model.apolice.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import br.com.banestes.sgrs.registradorasusep.dto.AutomovelDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.ObjetoPatrimonialDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.ObjetoSeguradoDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.cobertura.CoberturaDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.RamoPessoaDto;

import java.util.ArrayList;
import java.util.List;

@Component
public class ApoliceMapper {

    public ApoliceDto toDto(Apolice apolice, List<Parte> partes, List<Intermediario> intermediarios,
                            List<ObjetoSeguradoCompleto> objetosSeguradosCompletos,
                            List<AutomovelApolice> automoveisApolice,
                            List<ParcelaApolice> parcelas,
                            List<ContratoColetivoApolice> contratosColetivoApolice) {

        final ApoliceDto apoliceDto = new ApoliceDto();

        apoliceDto.setIdentificadorRegistro(apolice.getIdentificadorRegistro());
        apoliceDto.setGrupoComercial(apolice.getGrupoComercial());
        apoliceDto.setRamoComercial(apolice.getRamoComercial());
        apoliceDto.setCoberturaBasica(apolice.getCoberturaBasica());
        apoliceDto.setApoliceCodigo(apolice.getApoliceCodigo());
        apoliceDto.setCodigoFilial(apolice.getCodigoFilial());
        apoliceDto.setCodigoSeguradora(apolice.getCodigoSeguradora());
        apoliceDto.setDataEmissao(apolice.getDataEmissao());
        apoliceDto.setDataInicio(apolice.getDataInicioDocumento());
        apoliceDto.setDataTermino(apolice.getDataTerminoDocumento());
        apoliceDto.setCodigoSeguradoraLider(apolice.getCodigoSeguradoraLider());
        apoliceDto.setApoliceCodigoLider(apolice.getApoliceCodigoLider());
        apoliceDto.setLimiteMaximoGarantia(apolice.getLimiteMaximoGarantia());
        apoliceDto.setLimiteMaximoGarantiaReal(apolice.getLimiteMaximoGarantiaReal());
        apoliceDto.setMoedaApolice(apolice.getMoedaApolice());
        apoliceDto.setObjetosSegurado(obterListaObjetosSeguradosDto(objetosSeguradosCompletos));
        apoliceDto.setTipoEmissao(apolice.getTipoEmissao());
        apoliceDto.setTipoDocumentoEmitido(apolice.getTipoDocumentoEmitido());
        apoliceDto.setNumeroSusepApolice(apolice.getNumeroSusepApolice());
        apoliceDto.setCertificadoCodigo(apolice.getCertificadoCodigo());
        apoliceDto.setRenovada(apolice.getRenovada());
        apoliceDto.setApoliceRenovadaCodigo(apolice.getApoliceRenovadaCodigo());
        apoliceDto.setContragarantias(null);
        apoliceDto.setProposta(obterPropostaDto(apolice));
        apoliceDto.setPartes(obterListaPartesDto(partes));
        apoliceDto.setIntermediarios(obterListaIntermediariosDto(intermediarios));
        apoliceDto.setStopLoss(null);
        apoliceDto.setCreditoInterno(null);
        apoliceDto.setCreditoExportacao(null);
        apoliceDto.setExteriores(null);
        apoliceDto.setPremioApolice(obterPremioApoliceDto(apolice));
        apoliceDto.setParcelas(obterParcelasApolice(parcelas));
        apoliceDto.setCosseguro(null);

        if (CollectionUtils.isNotEmpty(contratosColetivoApolice)) {
          apoliceDto.setDadosContratoColetivo(obterContratoColetivoDto(contratosColetivoApolice.get(0)));
        }

        if (CollectionUtils.isNotEmpty(automoveisApolice)) {
            apoliceDto.setAutomovel(obterListaAutomovelDto(automoveisApolice.get(0)));
        }

        return apoliceDto;
    }

    private List<ParcelaApoliceDto> obterParcelasApolice(List<ParcelaApolice> parcelas) {
        final List<ParcelaApoliceDto> parcelasDto = new ArrayList<>();

        parcelas.forEach(p -> {
            parcelasDto.add(new ParcelaApoliceDto(p.getParcelaNumero(), p.getMoedaParcela(), p.getValor(), p.getValorReal(), p.getDataVencimento()));
        });

        return parcelasDto;
    }

    private PremioApoliceDto obterPremioApoliceDto(Apolice apolice) {
        final PremioApoliceDto premioApoliceDto = new PremioApoliceDto();
        premioApoliceDto.setNumeroParcelas(apolice.getNumeroParcelas());
        premioApoliceDto.setValorTotal(apolice.getValorTotal());
        premioApoliceDto.setValorTotalReal(apolice.getValorTotalReal());
        premioApoliceDto.setAdicionalFracionamento(apolice.getAdicionalFracionamento());
        premioApoliceDto.setIof(apolice.getIof());

        return premioApoliceDto;
    }

    private PropostaDto obterPropostaDto(Apolice apolice) {
        final PropostaDto propostaDto = new PropostaDto();
        propostaDto.setCodigoProposta(apolice.getCodigoProposta());
        propostaDto.setDataAssinatura(apolice.getDataAssinatura());
        propostaDto.setDataProtocolo(apolice.getDataProtocolo());
        return propostaDto;
    }

    private List<IntermediarioDto> obterListaIntermediariosDto(List<Intermediario> intermediarios) {
        final List<IntermediarioDto> intermediariosDto = new ArrayList<>(intermediarios.size());

        for (Intermediario intermediario : intermediarios) {
            intermediariosDto.add(new IntermediarioDto(intermediario));
        }
        return intermediariosDto;
    }

    private List<ObjetoSeguradoDto> obterListaObjetosSeguradosDto(List<ObjetoSeguradoCompleto> objetosSeguradosCompletos) {
        final List<ObjetoSeguradoDto> objetosSeguradoDto = new ArrayList<>(objetosSeguradosCompletos.size());

        for (ObjetoSeguradoCompleto objetoSeguradoCompleto : objetosSeguradosCompletos) {
            ObjetoSeguradoDto objetoSeguradoDto = new ObjetoSeguradoDto();
            objetoSeguradoDto.setCodigo(objetoSeguradoCompleto.getObjetoSegurado().getCodigo());
            objetoSeguradoDto.setDataInicio(objetoSeguradoCompleto.getObjetoSegurado().getDataInicio());
            objetoSeguradoDto.setDataTermino(objetoSeguradoCompleto.getObjetoSegurado().getDataTermino());
            objetoSeguradoDto.setDescricaoObjeto(objetoSeguradoCompleto.getObjetoSegurado().getDescricaoObjeto());
            objetoSeguradoDto.setTipo(objetoSeguradoCompleto.getObjetoSegurado().getTipo());
            objetoSeguradoDto.setValor(objetoSeguradoCompleto.getObjetoSegurado().getValor());
            objetoSeguradoDto.setValorReal(objetoSeguradoCompleto.getObjetoSegurado().getValorReal());
            objetoSeguradoDto.setDescricaoTipo(objetoSeguradoCompleto.getObjetoSegurado().getDescricaoTipo());
            objetoSeguradoDto.setLocalRisco(objetoSeguradoCompleto.getObjetoSegurado().getLocalRisco());
            objetoSeguradoDto.setCoberturas(obterListaCoberturasDto(objetoSeguradoCompleto.getCoberturas()));

            if (CollectionUtils.isNotEmpty(objetoSeguradoCompleto.getObjetosPatrimoniais())) {
                objetoSeguradoDto.setObjetosPatrimoniais(objetoListaPatrimonialDtos(objetoSeguradoCompleto.getObjetosPatrimoniais()));
            }

            if (CollectionUtils.isNotEmpty(objetoSeguradoCompleto.getRamosPessoas())) {
                objetoSeguradoDto.setRamosPessoas(objetoListaRamosPessoasDtos(objetoSeguradoCompleto.getRamosPessoas()));
            }

            objetosSeguradoDto.add(objetoSeguradoDto);
        }
        return objetosSeguradoDto;
    }

    private List<ParteDto> obterListaPartesDto(List<Parte> partes) {
        final List<ParteDto> partesDto = new ArrayList<>(partes.size());

        for (Parte parte : partes) {
            partesDto.add(new ParteDto(parte));
        }
        return partesDto;
    }

    private List<CoberturaDto> obterListaCoberturasDto(List<CoberturaCompleta> coberturas) {
        final List<CoberturaDto> coberturasDto = new ArrayList<>(coberturas.size());

        for (CoberturaCompleta coberturaCompleta : coberturas) {
            CoberturaDto coberturaDto = new CoberturaDto();
            coberturaDto.setCoberturaCaracteristica(coberturaCompleta.getCobertura().getCoberturaCaracteristica());
            coberturaDto.setCoberturaPrincipal(coberturaCompleta.getCobertura().getCoberturaPrincipal());
            coberturaDto.setCoberturaTipo(coberturaCompleta.getCobertura().getCoberturaTipos());
            coberturaDto.setCodigo(coberturaCompleta.getCobertura().getCodigo());
            coberturaDto.setDataInicioCobertura(coberturaCompleta.getCobertura().getDataInicioCobertura());
            coberturaDto.setDataTerminoCobertura(coberturaCompleta.getCobertura().getDataTerminoCobertura());
            coberturaDto.setGrupo(coberturaCompleta.getCobertura().getGrupo());
            coberturaDto.setLimiteMaximoIndenizacao(coberturaCompleta.getCobertura().getLimiteMaximoIndenizacao());
            coberturaDto.setLimiteMaximoIndenizacaoReal(coberturaCompleta.getCobertura().getLimiteMaximoIndenizacaoReal());
            coberturaDto.setNumeroProcesso(coberturaCompleta.getCobertura().getNumeroProcesso());
            coberturaDto.setRamo(coberturaCompleta.getCobertura().getRamo());
            coberturaDto.setValorPremio(coberturaCompleta.getCobertura().getValorPremio());
            coberturaDto.setValorPremioReal(coberturaCompleta.getCobertura().getValorPremioReal());
            coberturaDto.setOutrasDescricao(coberturaCompleta.getCobertura().getOutrasDescricao());
            coberturaDto.setCoberturaInternaSeguradora(coberturaCompleta.getCobertura().getCoberturaInternaSeguradora());
            coberturaDto.setIndiceAtualizacao(coberturaCompleta.getCobertura().getIndiceAtualizacao());
            coberturaDto.setPeriodicidadeAtualizacao(coberturaCompleta.getCobertura().getPeriodicidadeAtualizacao());
            coberturaDto.setPeriodicidadeUnidade(coberturaCompleta.getCobertura().getPeriodicidadeUnidade());
            coberturaDto.setPeriodicidadePremio(coberturaCompleta.getCobertura().getPeriodicidadePremio());
            coberturaDto.setDescricaoPeriodicidade(coberturaCompleta.getCobertura().getDescricaoPeriodicidade());
            coberturaDto.setTipoRisco(coberturaCompleta.getCobertura().getTipoRisco());
            coberturaDto.setCarenciaPeriodo(coberturaCompleta.getCobertura().getCarenciaPeriodo());
            coberturaDto.setCarenciaPeriodicidade(coberturaCompleta.getCobertura().getCarenciaPeriodicidade());
            coberturaDto.setCarenciaPeriodicidadeDias(coberturaCompleta.getCobertura().getCarenciaPeriodicidadeDias());
            coberturaDto.setCarenciaDataInicio(coberturaCompleta.getCobertura().getCarenciaDataInicio());
            coberturaDto.setCarenciaDataTermino(coberturaCompleta.getCobertura().getCarenciaDataTermino());
            coberturaDto.setDataInicioPremio(coberturaCompleta.getCobertura().getDataInicioPremio());
            coberturaDto.setDataTerminoPremio(coberturaCompleta.getCobertura().getDataTerminoPremio());
            coberturaDto.setIof(coberturaCompleta.getCobertura().getIof());
            coberturaDto.setCusto(coberturaCompleta.getCobertura().getCusto());
            coberturaDto.setCustoReal(coberturaCompleta.getCobertura().getCustoReal());

            coberturasDto.add(coberturaDto);
            coberturaDto.setLimiteMaximoIndenizacaoSublimite(coberturaCompleta.getCobertura().getLimiteMaximoIndenizacaoSublimite());
        }
        return coberturasDto;
    }

    private ContratoColetivoDto obterContratoColetivoDto(ContratoColetivoApolice dadosContratoColetivo){
      final ContratoColetivoDto dadosContratoColetivoDto = new ContratoColetivoDto();
      dadosContratoColetivoDto.setTipoPlano(dadosContratoColetivo.getTipoPlano());
      return dadosContratoColetivoDto;
    }

    private AutomovelDto obterListaAutomovelDto(AutomovelApolice auto) {

        final AutomovelDto automovelDto = new AutomovelDto();
        automovelDto.setRedeReparacao(auto.getRedeReparacao());
        automovelDto.setTipoPecas(auto.getTipoPecas());
        automovelDto.setClassificacaoPecas(auto.getClassificacaoPecas());
        automovelDto.setNacionalidadePecas(auto.getNacionalidadePecas());
        automovelDto.setTipoVigencia(auto.getTipoVigencia());
        automovelDto.setFormasRecompensa(auto.getFormasRecompensa());
        automovelDto.setBeneficiosAdicionais(auto.getBeneficiosAdicionais());
        automovelDto.setPacotesAssistencia(auto.getPacotesAssitencia());
        automovelDto.setRiscoDecorrido(auto.getRiscoDecorrido());

        return automovelDto;
    }

    private List<ObjetoPatrimonialDto> objetoListaPatrimonialDtos(List<ObjetoPatrimonialApolice> objetoPatrimonialApolices) {
        List<ObjetoPatrimonialDto> values = new ArrayList<>(objetoPatrimonialApolices.size());

        for (ObjetoPatrimonialApolice objetoPatrimonialApolice : objetoPatrimonialApolices) {
            ObjetoPatrimonialDto objetoPatrimonialDto = new ObjetoPatrimonialDto();
            objetoPatrimonialDto.setTipoImovelSegurado(objetoPatrimonialApolice.getTipoImovelSegurado());
            objetoPatrimonialDto.setTipoEstruturacaoCondominio(objetoPatrimonialApolice.getTipoEstruturacaoCondominio());
            objetoPatrimonialDto.setCepObjetoSegurado(objetoPatrimonialApolice.getCepObjetoSegurado());
            objetoPatrimonialDto.setCodigoCNAE(objetoPatrimonialApolice.getCodigoCnae());
            values.add(objetoPatrimonialDto);
        }

        return values;
    }

    private List<RamoPessoaDto> objetoListaRamosPessoasDtos(List<RamoPessoasApolice> objetoRamoPessoas) {

        List<RamoPessoaDto> values = new ArrayList<>(objetoRamoPessoas.size());

        for (RamoPessoasApolice objetoRamoPessoa : objetoRamoPessoas) {
            values.add(new RamoPessoaDto(objetoRamoPessoa));
        }

        return values;
    }

}
