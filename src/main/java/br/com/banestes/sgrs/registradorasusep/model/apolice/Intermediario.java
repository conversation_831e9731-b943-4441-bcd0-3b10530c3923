package br.com.banestes.sgrs.registradorasusep.model.apolice;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_CTR_INTERMEDIARIO")
public class Intermediario {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTR_INTERMEDIARIO")
    private Long idtCtrIntermediario;
    @Column(name = "TIPO")
    private String tipo;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "NOME")
    private String nome;
    @Column(name = "IDT_CTR_APOLICE")
    private Long idtCtrApolice;
    @Column(name = "DOCUMENTO")
    private String documento;
    @Column(name = "CODIGO")
    private String codigo;
    @Column(name = "TIPO_DOCUMENTO")
    private String tipoDocumento;
    @Column(name = "VALOR_COMISSAO")
    private Double valorComissao;
    @Column(name = "EMAIL")
    private String email;
    @Column(name = "ENDERECO")
    private String endereco;
    @Column(name = "VALOR_COMISSAO_REAL")
    private Double valorComissaoReal;
    @Column(name = "COMPLEMENTO")
    private String complemento;
    @Column(name = "NUMERO")
    private String numero;
    @Column(name = "CIDADE")
    private String cidade;
    @Column(name = "BAIRRO")
    private String bairro;
    @Column(name = "PAIS")
    private String pais;
    @Column(name = "UF")
    private String uf;
    @Column(name = "CEP")
    private String cep;
}

