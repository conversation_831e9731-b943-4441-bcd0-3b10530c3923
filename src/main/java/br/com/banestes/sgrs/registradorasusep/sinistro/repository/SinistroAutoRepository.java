package br.com.banestes.sgrs.registradorasusep.sinistro.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroAuto;

import java.util.List;

@Repository
public interface SinistroAutoRepository extends JpaRepository<SinistroAuto, Integer> {
    List<SinistroAuto> getAllByIdtCtlProcAndIdtSntSinistro(Integer idtCtlProc, Long idtSntSinistro);
}

