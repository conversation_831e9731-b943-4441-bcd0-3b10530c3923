package br.com.banestes.sgrs.registradorasusep.model.sinistro;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_SNT_VISTORIA")
public class SinistroVistoria {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_SNT_VISTORIA")
    private Long idtSntVistoria;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_SNT_SINISTRO")
    private Long idtSntSinistro;
    @Column(name = "NUM_SEQ_VISTORIA")
    private Integer numSeqVistoria;
    @Column(name = "DATA_VISTORIA")
    private String dataVistoria;
    @Column(name = "LOCAL_VISTORIA")
    private String localVistoria;
    @Column(name = "UF_VISTORIA")
    private String ufVistoria;
    @Column(name = "CODIGO_POSTAL_VISTORIA")
    private String codigoPostalVistoria;
    @Column(name = "PAIS_VISTORIA")
    private String paisVistoria;
    @Column(name = "TIPO_DOCUMENTO_VISTORIADOR")
    private String tipoDocumentoVistoriador;
    @Column(name = "DOCUMENTO_VISTORIADOR")
    private String documentoVistoriador;
    @Column(name = "NOME_VISTORIADOR")
    private String nomeVistoriador;
    @Column(name = "CODIGO_OBJETO")
    private String codigoObjeto;
    @Column(name = "EVENTO_GERADOR")
    private String eventoGerador;
}
