package br.com.banestes.sgrs.registradorasusep.service;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.ControleSinistro;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.repository.ControleSinistroRepository;

import java.util.Optional;

@Service
public class ControleSinistroService {
    private final ControleSinistroRepository controleSinistroRepository;
    private final SinistroUpdateService sinistroUpdateService;

    public ControleSinistroService(SinistroUpdateService sinistroUpdateService,
                                   ControleSinistroRepository controleSinistroRepository) {

        this.sinistroUpdateService = sinistroUpdateService;
        this.controleSinistroRepository = controleSinistroRepository;
    }

    public void atualizar(Long idtSntSinistro, String situacaoProcessamento, String mensagemErroProcessamento, Character idcOperacao) {
        final Optional<ControleSinistro> controleSinistroOpt = controleSinistroRepository.findById(idtSntSinistro);

        if (controleSinistroOpt.isPresent()) {
            final ControleSinistro controleSinistro = controleSinistroOpt.get();

            sinistroUpdateService.atualizarSituacao(
                controleSinistro.getIdtCtlSinistro(),
                controleSinistro.getIdtCtlProc(),
                situacaoProcessamento,
                mensagemErroProcessamento,
                idcOperacao
            );

            Long idtCtlSinistroAtu = controleSinistro.getIdtCtlSinistroAtu();
            if (("R".equals(situacaoProcessamento) || "E".equals(situacaoProcessamento)) &&
                    idtCtlSinistroAtu != null && idtCtlSinistroAtu > 0) {
                sinistroUpdateService.updateRegistroCorrecaoIngnorar(controleSinistro);
            }
        }
    }


    public void atualizarRegistroExclusao(ExclusaoLayout exclusaoLayout, String situacaoProcessamento) {
        final ControleSinistro controleSinistro = controleSinistroRepository.findById(exclusaoLayout.getIdtLeiaute()).orElse(null);

        if (controleSinistro != null) {
            sinistroUpdateService.updateExclusaoRegistro(controleSinistro.getIdtCtlSinistro(), exclusaoLayout.getNumMatrUltAtualizacao(), situacaoProcessamento);
        }
    }

}
