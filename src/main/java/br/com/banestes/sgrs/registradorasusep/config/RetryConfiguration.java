package br.com.banestes.sgrs.registradorasusep.config;

import br.com.banestes.sgrs.registradorasusep.exception.RotinaException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.step.skip.SkipLimitExceededException;
import org.springframework.batch.core.step.skip.SkipPolicy;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.RetryPolicy;
import org.springframework.retry.backoff.BackOffPolicy;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientException;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Configuration
public class RetryConfiguration {

    @Value("${step.retry.maxAttempts:3}")
    private Integer stepRetryMaxAttempts;
    
    @Value("${step.retry.initialInterval:2000}")
    private Long stepRetryInitialInterval;
    
    @Value("${step.retry.maxInterval:10000}")
    private Long stepRetryMaxInterval;
    
    @Value("${step.retry.multiplier:2.0}")
    private Double stepRetryMultiplier;

    /**
     * Política de retry para operações que podem falhar temporariamente
     */
    @Bean
    public RetryPolicy stepRetryPolicy() {
        Map<Class<? extends Throwable>, Boolean> retryableExceptions = new HashMap<>();
        
        // Exceções que devem ser tentadas novamente
        retryableExceptions.put(DataAccessException.class, true);
        retryableExceptions.put(SQLException.class, true);
        retryableExceptions.put(RestClientException.class, true);
        retryableExceptions.put(ResourceAccessException.class, true);
        retryableExceptions.put(RuntimeException.class, true);
        
        // Exceções que NÃO devem ser tentadas novamente
        retryableExceptions.put(RotinaException.class, false);
        retryableExceptions.put(IllegalArgumentException.class, false);
        retryableExceptions.put(IllegalStateException.class, false);

        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy(stepRetryMaxAttempts, retryableExceptions);
        
        log.info("🔄 Política de retry configurada: {} tentativas máximas", stepRetryMaxAttempts);
        
        return retryPolicy;
    }

    /**
     * Política de backoff exponencial para espaçar as tentativas
     */
    @Bean
    public BackOffPolicy stepBackOffPolicy() {
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(stepRetryInitialInterval);
        backOffPolicy.setMaxInterval(stepRetryMaxInterval);
        backOffPolicy.setMultiplier(stepRetryMultiplier);
        
        log.info("⏱️ Política de backoff configurada: inicial={}ms, máximo={}ms, multiplicador={}", 
                stepRetryInitialInterval, stepRetryMaxInterval, stepRetryMultiplier);
        
        return backOffPolicy;
    }

    /**
     * Template de retry para uso em componentes
     */
    @Bean
    public RetryTemplate retryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();
        retryTemplate.setRetryPolicy(stepRetryPolicy());
        retryTemplate.setBackOffPolicy(stepBackOffPolicy());
        
        // Listeners para log
        retryTemplate.registerListener(new org.springframework.retry.RetryListener() {
            @Override
            public <T, E extends Throwable> boolean open(org.springframework.retry.RetryContext context, 
                    org.springframework.retry.RetryCallback<T, E> callback) {
                if (context.getRetryCount() == 0) {
                    log.debug("🔄 Iniciando operação com retry habilitado");
                }
                return true;
            }

            @Override
            public <T, E extends Throwable> void onError(org.springframework.retry.RetryContext context, 
                    org.springframework.retry.RetryCallback<T, E> callback, Throwable throwable) {
                log.warn("❌ Tentativa {} falhou: {}", context.getRetryCount(), throwable.getMessage());
            }

            @Override
            public <T, E extends Throwable> void close(org.springframework.retry.RetryContext context, 
                    org.springframework.retry.RetryCallback<T, E> callback, Throwable throwable) {
                if (throwable == null) {
                    if (context.getRetryCount() > 0) {
                        log.info("✅ Operação bem-sucedida após {} tentativas", context.getRetryCount() + 1);
                    }
                } else {
                    log.error("🚨 Operação falhou após {} tentativas: {}", 
                            context.getRetryCount() + 1, throwable.getMessage());
                }
            }
        });
        
        return retryTemplate;
    }

    /**
     * Política de skip para itens que falharam múltiplas vezes
     */
    @Bean
    public SkipPolicy customSkipPolicy() {
        return new SkipPolicy() {
            @Override
            public boolean shouldSkip(Throwable t, int skipCount) throws SkipLimitExceededException {
                
                // Nunca pular RotinaException - são erros de negócio
                if (t instanceof RotinaException) {
                    log.error("🚨 RotinaException não pode ser pulada: {}", t.getMessage());
                    return false;
                }
                
                // Pular após 3 tentativas para outros tipos de erro
                if (skipCount < 3) {
                    log.warn("⚠️ Pulando item após {} tentativas devido a: {}", skipCount + 1, t.getMessage());
                    return true;
                }
                
                log.error("🚨 Limite de skip excedido para: {}", t.getMessage());
                return false;
            }
        };
    }

    /**
     * Wrapper para ItemProcessor com retry automático
     */
    public static class RetryableItemProcessor<I, O> implements ItemProcessor<I, O> {
        private final ItemProcessor<I, O> delegate;
        private final RetryTemplate retryTemplate;

        public RetryableItemProcessor(ItemProcessor<I, O> delegate, RetryTemplate retryTemplate) {
            this.delegate = delegate;
            this.retryTemplate = retryTemplate;
        }

        @Override
        public O process(I item) throws Exception {
            return retryTemplate.execute(context -> {
                log.debug("🔄 Processando item (tentativa {}): {}", context.getRetryCount() + 1, item);
                return delegate.process(item);
            });
        }
    }

    /**
     * Wrapper para ItemReader com retry automático
     */
    public static class RetryableItemReader<T> implements ItemReader<T> {
        private final ItemReader<T> delegate;
        private final RetryTemplate retryTemplate;

        public RetryableItemReader(ItemReader<T> delegate, RetryTemplate retryTemplate) {
            this.delegate = delegate;
            this.retryTemplate = retryTemplate;
        }

        @Override
        public T read() throws Exception {
            return retryTemplate.execute(context -> {
                if (context.getRetryCount() > 0) {
                    log.debug("🔄 Tentando ler item (tentativa {})", context.getRetryCount() + 1);
                }
                return delegate.read();
            });
        }
    }

    /**
     * Wrapper para ItemWriter com retry automático
     */
    public static class RetryableItemWriter<T> implements ItemWriter<T> {
        private final ItemWriter<T> delegate;
        private final RetryTemplate retryTemplate;

        public RetryableItemWriter(ItemWriter<T> delegate, RetryTemplate retryTemplate) {
            this.delegate = delegate;
            this.retryTemplate = retryTemplate;
        }

        @Override
        public void write(java.util.List<? extends T> items) throws Exception {
            retryTemplate.execute(context -> {
                if (context.getRetryCount() > 0) {
                    log.debug("🔄 Tentando escrever {} itens (tentativa {})", 
                            items.size(), context.getRetryCount() + 1);
                }
                delegate.write(items);
                return null;
            });
        }
    }
}
