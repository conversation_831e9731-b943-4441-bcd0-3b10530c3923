package br.com.banestes.sgrs.registradorasusep.dto.endosso;

import br.com.banestes.sgrs.registradorasusep.model.endosso.PercentualPrestamistaEndosso;
import br.com.banestes.sgrs.registradorasusep.model.endosso.PrestamistaEndosso;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ToString
public class PrestamistaDto {
    private String modeloCapital;
    private String prestamistaTipo;
    private String tipoDocumento;
    private String documento;
    private String nome;
    private String tipoObrigacao;
    private String descricaoObrigacao;
    private List<PercentualCapitalSeguradoDto> percentuais;

    public PrestamistaDto(PrestamistaEndosso prestamistaEndosso) {
        this.modeloCapital = prestamistaEndosso.getModeloCapital();
        this.prestamistaTipo = prestamistaEndosso.getTipo();
        this.tipoDocumento = prestamistaEndosso.getTipoDocumento();
        this.documento = prestamistaEndosso.getDocumento();
        this.nome = prestamistaEndosso.getNome();
        this.tipoObrigacao = prestamistaEndosso.getTipoObrigacao();
        this.descricaoObrigacao = prestamistaEndosso.getDescricaoObrigacao();

        this.percentuais = new ArrayList<PercentualCapitalSeguradoDto>(prestamistaEndosso.getPercentuais().size());
        for (PercentualPrestamistaEndosso percentualPrestamista : prestamistaEndosso.getPercentuais()) {
            this.percentuais.add(new PercentualCapitalSeguradoDto(percentualPrestamista));
        }
    }

}
