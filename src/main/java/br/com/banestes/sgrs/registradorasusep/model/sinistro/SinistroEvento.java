package br.com.banestes.sgrs.registradorasusep.model.sinistro;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_SNT_EVENTO")
public class SinistroEvento {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_SNT_EVENTO")
    private Long idtSntEvento;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_SNT_SINISTRO")
    private Long idtSntSinistro;
    @Column(name = "SINISTRO_DESCRICAO_AEROPORTO_OPERACAO")
    private String sinistroDescricaoAeroportoOperacao;
    @Column(name = "ESPECIFICACAO_AEROPORTO")
    private String especificacaoAeroporto;
    @Column(name = "SINISTRO_DESCRICAO_ESPECIFICACAO_AEROPORTO")
    private String sinistroDescricaoEspecificacaoAeroporto;
    @Column(name = "SINISTRO_DESCRICAO_EVENTO")
    private String sinistroDescricaoEvento;
    @Column(name = "SINISTRO_DESCRICAO_DANOS")
    private String sinistroDescricaoDanos;
    @Column(name = "CODIGO_OBJETO")
    private String codigoObjeto;
    @Column(name = "GRUPO")
    private String grupo;
    @Column(name = "RAMO")
    private String ramo;
}
