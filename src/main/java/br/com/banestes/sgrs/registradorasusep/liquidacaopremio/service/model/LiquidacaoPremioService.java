package br.com.banestes.sgrs.registradorasusep.liquidacaopremio.service.model;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.helper.ApiResponseHelper;
import br.com.banestes.sgrs.registradorasusep.liquidacaopremio.repository.PremioRepository;
import br.com.banestes.sgrs.registradorasusep.model.premio.Premio;
import br.com.banestes.sgrs.registradorasusep.service.ControlePremioService;

import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class LiquidacaoPremioService {
    private final PremioRepository premioRepository;
    private final ControlePremioService controlePremioService;

    public LiquidacaoPremioService(PremioRepository premioRepository, ControlePremioService controlePremioService) {

        this.premioRepository = premioRepository;
        this.controlePremioService = controlePremioService;
    }

    public List<Premio> listarLiquidacoesPremioTransmissao(Integer idtCtlProc) {
        return premioRepository.listarLiquidacoesPremioTransmissao(idtCtlProc);
    }

    @Transactional
    public boolean atualizarStatusTransmissao(Premio premio, ResponseDto resposta, Character idcOperacao) {

        final List<Integer> httpCodeErrors = Arrays.asList(403, 415, 500, 501, 502, 503, 504, 600);
        String situacaoProcessamento = "R";
        String mensagemErroProcessamento = null;
        if (resposta.getCode() != HttpStatus.OK.value() && resposta.getCode() != HttpStatus.CREATED.value()) {

            // erro interno da maps
            if (httpCodeErrors.contains(resposta.getCode())) {

                situacaoProcessamento = "P";
                mensagemErroProcessamento = ApiResponseHelper.getErrorMessage(resposta);
                controlePremioService.atualizar(premio.getIdtPrmPremio(), situacaoProcessamento, mensagemErroProcessamento, idcOperacao);

                log.info("Erro: {} - tente novamente mais tarde.", resposta.getMessage());
                return false;
            }

            // erro de negócio
            situacaoProcessamento = "E";
            mensagemErroProcessamento = ApiResponseHelper.getErrorMessage(resposta);
        }
        controlePremioService.atualizar(premio.getIdtPrmPremio(), situacaoProcessamento, mensagemErroProcessamento, idcOperacao);
        return true;
    }
}
