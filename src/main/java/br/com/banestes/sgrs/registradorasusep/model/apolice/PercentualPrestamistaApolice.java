package br.com.banestes.sgrs.registradorasusep.model.apolice;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@ToString
@Table(name = "SRO_CTR_PCT_PRESTAMISTA")
public class PercentualPrestamistaApolice
{
    @Id
    @Column(name = "IDT_CTR_PCT_PRESTAMISTA")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idtCtrPctPrestamista;

    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;

    @Column(name = "IDT_CTR_PRESTAMISTA", insertable = false, updatable = false)
    private Long idtCtrPrestamista;

    @Column(name = "DOCUMENTO")
    private String documento;

    @Column(name = "PERCENTUAL_CAPITAL_SEGURADO")
    private Double percentualCapitalSegurado;
}
