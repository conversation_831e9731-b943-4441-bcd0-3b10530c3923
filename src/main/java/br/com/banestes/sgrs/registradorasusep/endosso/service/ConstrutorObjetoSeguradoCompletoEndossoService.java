package br.com.banestes.sgrs.registradorasusep.endosso.service;

import br.com.banestes.sgrs.registradorasusep.endosso.repository.DependenteEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.PercentualPrestamistaEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.PrestamistaEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.RamoPessoasEndossoService;
import br.com.banestes.sgrs.registradorasusep.model.endosso.PrestamistaEndosso;
import br.com.banestes.sgrs.registradorasusep.model.endosso.RamoPessoasEndosso;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.endosso.repository.ObjetoPatrimonialEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.CoberturaEndossoService;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.ObjetoSeguradoEndossoService;
import br.com.banestes.sgrs.registradorasusep.model.endosso.Endosso;
import br.com.banestes.sgrs.registradorasusep.model.endosso.ObjetoSeguradoCompleto;
import br.com.banestes.sgrs.registradorasusep.model.endosso.ObjetoSeguradoEndosso;

import java.util.ArrayList;
import java.util.List;

@Service
public class ConstrutorObjetoSeguradoCompletoEndossoService {
    private final CoberturaEndossoService coberturaService;
    private final ObjetoSeguradoEndossoService objetoSeguradoService;
    private final ObjetoPatrimonialEndossoRepository objetoPatrimonialEndossoRepository;
    private final RamoPessoasEndossoService ramoPessoasEndossoService;
    private final PrestamistaEndossoRepository prestamistaEndossoRepository;
    private final PercentualPrestamistaEndossoRepository percentualPrestamistaEndossoRepository;
    private final DependenteEndossoRepository dependenteEndossoRepository;

    public ConstrutorObjetoSeguradoCompletoEndossoService(CoberturaEndossoService coberturaService,
                                                          ObjetoSeguradoEndossoService objetoSeguradoService,
                                                          ObjetoPatrimonialEndossoRepository objetoPatrimonialEndossoRepository,
                                                          RamoPessoasEndossoService ramoPessoasEndossoService,
                                                          PrestamistaEndossoRepository prestamistaEndossoRepository,
                                                          PercentualPrestamistaEndossoRepository percentualPrestamistaEndossoRepository,
                                                          DependenteEndossoRepository dependenteEndossoRepository) {

        this.coberturaService = coberturaService;
        this.objetoSeguradoService = objetoSeguradoService;
        this.objetoPatrimonialEndossoRepository = objetoPatrimonialEndossoRepository;
        this.ramoPessoasEndossoService = ramoPessoasEndossoService;
        this.prestamistaEndossoRepository = prestamistaEndossoRepository;
        this.percentualPrestamistaEndossoRepository = percentualPrestamistaEndossoRepository;
        this.dependenteEndossoRepository = dependenteEndossoRepository;
    }

    public List<ObjetoSeguradoCompleto> obterObjetosSeguradosCompletosEndosso(Endosso endosso) {

        final List<ObjetoSeguradoEndosso> objetosSegurados = objetoSeguradoService.findAllByIdtCtlProcAndIdtEdsEndosso(
                endosso.getIdtCtlProc(),
                endosso.getIdtEdsEndosso());

        final List<ObjetoSeguradoCompleto> objetosSeguradosCompletos = new ArrayList<>(objetosSegurados.size());

        for (ObjetoSeguradoEndosso objetoSegurado : objetosSegurados) {

            final ObjetoSeguradoCompleto objetoSeguradoCompleto = new ObjetoSeguradoCompleto();

            objetoSeguradoCompleto.setObjetoSegurado(objetoSegurado);
            objetoSeguradoCompleto.setCoberturas(coberturaService.findAllByIdtCtlProcAndIdtEdsEndossoAndIdtCtrObjeto(objetoSegurado.getIdtCtlProc(), objetoSegurado.getIdtEdsObjeto()));
            objetoSeguradoCompleto.setObjetosPatrimoniais(objetoPatrimonialEndossoRepository.findAllByIdtEdsObjetoAndIdtCtlProc(objetoSegurado.getIdtEdsObjeto(), objetoSegurado.getIdtCtlProc()));

            objetoSeguradoCompleto.setRamosPessoas(ramoPessoasEndossoService.findAllByIdtEdsObjetoAndIdtCtlProc(objetoSegurado.getIdtEdsObjeto(), objetoSegurado.getIdtCtlProc()));
            for(RamoPessoasEndosso ramoPessoasEndosso : objetoSeguradoCompleto.getRamosPessoas()){
                ramoPessoasEndosso.setDependentes(dependenteEndossoRepository.findAllByidtEdsRmoPessoaAndIdtCtlProc(ramoPessoasEndosso.getIdtEdsRmoPessoa(), ramoPessoasEndosso.getIdtCtlProc()));
                ramoPessoasEndosso.setPrestamistas(prestamistaEndossoRepository.findAllByidtEdsRmoPessoaAndIdtCtlProc(ramoPessoasEndosso.getIdtEdsRmoPessoa(), ramoPessoasEndosso.getIdtCtlProc()));
                for(PrestamistaEndosso prestamistaEndosso : ramoPessoasEndosso.getPrestamistas()){
                    prestamistaEndosso.setPercentuais(percentualPrestamistaEndossoRepository.findAllByidtEdsPrestamistaAndIdtCtlProc(prestamistaEndosso.getIdtEdsPrestamista(), prestamistaEndosso.getIdtCtlProc()));
                }
            }

            objetosSeguradosCompletos.add(objetoSeguradoCompleto);
        }
        return objetosSeguradosCompletos;
    }

}
