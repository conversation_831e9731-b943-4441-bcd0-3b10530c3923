package br.com.banestes.sgrs.registradorasusep.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.ControleApolice;

import javax.sql.DataSource;
import java.sql.Timestamp;

@Slf4j
@Service
public class ApoliceUpdateService {

    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;
    private final JdbcTemplate jdbcTemplateObject;

    public ApoliceUpdateService(@Qualifier("sqlServerDataSource") DataSource dataSource, JdbcTemplate jdbcTemplateObject) {
        this.jdbcTemplateObject = jdbcTemplateObject;
        this.jdbcTemplateObject.setDataSource(dataSource);
    }

    public void atualizarSituacao(Long idtCtlApolice, Integer idtCtlProc, String situacaoProcessamento, String mensagemErroProcessamento, Character idcOperacao) {
        if ("E".equals(situacaoProcessamento)) {
            updateErro(idtCtlApolice, idtCtlProc, mensagemErroProcessamento);
        } else if ("P".equals(situacaoProcessamento)) {
            updateErroPlataforma(idtCtlApolice, idtCtlProc, mensagemErroProcessamento);
        } else {
            updateSucesso(idtCtlApolice, idtCtlProc, idcOperacao);
        }
    }

    public void updateRegistroCorrecaoIngnorar(ControleApolice controleApolice) {
        String sql = "UPDATE SRO_CTL_APOLICE SET IDC_SIT_PROC = 'I', DAT_HOR_ULT_ATUALIZACAO = ? WHERE IDT_CTL_APOLICE = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, new Timestamp(System.currentTimeMillis()), controleApolice.getIdtCtlApoliceAtu());
        }
    }

    private void updateSucesso(Long idtCtlApolice, Integer idtCtlProc, Character idcOperacao) {
//        log.info("Nao houve erro na transmissao estatus sendo atualizado para: 'R'");
        String sql = "update SRO_CTL_APOLICE set IDC_SIT_PROC = 'R', DES_MSG_PROC = '', DAT_HOR_ULT_ATUALIZACAO = ? where IDT_CTL_APOLICE = ? and IDT_CTL_PROC = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, new Timestamp(System.currentTimeMillis()), idtCtlApolice, idtCtlProc);
            if (idcOperacao.equals('I')) {
                String sqlUpdateDatHorRegistro = "UPDATE SRO_CTL_APOLICE SET DAT_HOR_REGISTRO = ? where IDT_CTL_APOLICE = ? and IDT_CTL_PROC = ?";
                jdbcTemplateObject.update(sqlUpdateDatHorRegistro, new Timestamp(System.currentTimeMillis()), idtCtlApolice, idtCtlProc);
            }
        }
    }

    private void updateErro(Long idtCtlApolice, Integer idtCtlProc, String mensagemErroProcessamento) {
//        log.info("Houve erro na transmissao estatus sendo atualizado para: 'E' mensagem: {}", mensagemErroProcessamento);
        String sql = "update SRO_CTL_APOLICE set IDC_SIT_PROC = 'E', DES_MSG_PROC = ? , DAT_HOR_ULT_ATUALIZACAO = ? where IDT_CTL_APOLICE = ? and IDT_CTL_PROC = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, mensagemErroProcessamento, new Timestamp(System.currentTimeMillis()), idtCtlApolice, idtCtlProc);
        }
    }

    private void updateErroPlataforma(Long idtCtlApolice, Integer idtCtlProc, String mensagemErroProcessamento) {
//        log.info("Houve erro de plataforma na transmissao estatus sendo atualizado para: 'P' mensagem: {}", mensagemErroProcessamento);
        String sql = "update SRO_CTL_APOLICE set IDC_SIT_PROC = 'P', DES_MSG_PROC = ? , DAT_HOR_ULT_ATUALIZACAO = ? where IDT_CTL_APOLICE = ? and IDT_CTL_PROC = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, mensagemErroProcessamento, new Timestamp(System.currentTimeMillis()), idtCtlApolice, idtCtlProc);
        }
    }

    public void updateExclusaoRegistro(Long idtCtlApolice, Integer numeroMatr, String situacao) {
        String sql = "update SRO_CTL_APOLICE set IDC_SIT_PROC = ?, DAT_HOR_ULT_ATUALIZACAO = ?, NUM_MATR_ULT_ATUALIZACAO = ? where IDT_CTL_APOLICE = ?";
        if (!modoSimulacao) {
            jdbcTemplateObject.update(sql, situacao, new Timestamp(System.currentTimeMillis()), numeroMatr, idtCtlApolice);
        }
    }
}
