package br.com.banestes.sgrs.registradorasusep.model.endosso;

import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Data
@NoArgsConstructor
@Entity
@Table(name = "SRO_EDS_AUTOMOVEL")
public class AutomovelEndosso {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_EDS_AUTOMOVEL")
    private Long idtEdsAutomovel;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "RISCO_DECORRIDO")
    private String riscoDecorrido;
    @Column(name = "IDT_EDS_ENDOSSO")
    private Long idtEdsEndosso;
    @Column(name = "BENEFICIOS_ADICIONAIS")
    private String beneficiosAdicionais;
    @Column(name = "TIPO_VIGENCIA")
    private String tipoVigencia;
    @Column(name = "REDE_REPARACAO")
    private String redeReparacao;
    @Column(name = "NACIONALIDADE_PECAS")
    private String nacionalidadePecas;
    @Column(name = "TIPO_PECAS")
    private String tipoPecas;
    @Column(name = "FORMAS_RECOMPENSA")
    private String formasRecompensa;
    @Column(name = "CLASSIFICACAO_PECAS")
    private String classificacaoPecas;
    @Column(name = "PACOTES_ASSISTENCIA")
    private String pacotesAssitencia;


}
