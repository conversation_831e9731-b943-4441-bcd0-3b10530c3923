package br.com.banestes.sgrs.registradorasusep.config;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Order(2147483647)
public class EnvConfig implements EnvironmentPostProcessor {
    private static final String QUERY = "select PROPERTY, VALUE from SRO_AUX_ROTINA";
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        Map<String, Object> properties = new ConcurrentHashMap();
        DataSourceBuilder<?> dataSourceBuilder = DataSourceBuilder.create();
        dataSourceBuilder.username(new String(Base64.getDecoder().decode(environment.getProperty("sqlserver.datasource.username")), StandardCharsets.ISO_8859_1));
        dataSourceBuilder.password(new String(Base64.getDecoder().decode(environment.getProperty("sqlserver.datasource.password")), StandardCharsets.ISO_8859_1));
        dataSourceBuilder.driverClassName(environment.getProperty("sqlserver.datasource.driverClassName"));
        dataSourceBuilder.url(environment.getProperty("sqlserver.datasource.jdbcUrl"));

        JdbcTemplate jdbcTemplateObject = new JdbcTemplate(dataSourceBuilder.build());
        SqlRowSet sqlRowSet = jdbcTemplateObject.queryForRowSet(QUERY);
        while (sqlRowSet.next()){
            String propertyname = sqlRowSet.getString(1);
            String propertyValue = environment.getProperty(propertyname,"");
            if (propertyValue.equalsIgnoreCase("")) {
                properties.put(propertyname, sqlRowSet.getString(2));
            }
        }
        environment.getPropertySources().addLast(new MapPropertySource("databaseProperties", properties));
    }
}