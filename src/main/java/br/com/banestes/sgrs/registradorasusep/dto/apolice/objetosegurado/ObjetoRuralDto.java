package br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ObjetoRuralDto {
    private String participaFESR;
    private Double valorPremioSubvencionado;
    private String ufOrigemSubvencao;
    private String areaSeguradaTotal;
    private String unidadeMedidaAreaSegurada;
    private String codigoCultura;
    private String codigoRebanho;
    private String codigoFloresta;
    private String dataVistoria;
    private String localVistoria;
    private String ufVistoria;
    private String cepVistoria;
    private String paisVistoria;
    private String tipoDocumentoVistoriador;
    private String documentoVistoriador;
    private String nomeVistoriador;
    private String tipoModeloParametrico;
    private String cobreBemNaoDadoGarantiaOperacaoCreditoRural;
    private String destinacaoAnimalCobertoParaPecuario;
    private String classificacaoAnimalCoberto;
}
