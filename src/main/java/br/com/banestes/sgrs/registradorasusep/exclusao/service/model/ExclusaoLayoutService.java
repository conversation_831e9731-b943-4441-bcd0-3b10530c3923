package br.com.banestes.sgrs.registradorasusep.exclusao.service.model;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.exclusao.repository.ExclusaoLayoutRepository;
import br.com.banestes.sgrs.registradorasusep.helper.ApiResponseHelper;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.service.ControleApoliceService;
import br.com.banestes.sgrs.registradorasusep.service.ControleComplementarAutoService;
import br.com.banestes.sgrs.registradorasusep.service.ControleEndossoService;
import br.com.banestes.sgrs.registradorasusep.service.ControleMovimentoSinistroService;
import br.com.banestes.sgrs.registradorasusep.service.ControlePremioService;
import br.com.banestes.sgrs.registradorasusep.service.ControleSinistroService;
import br.com.banestes.sgrs.registradorasusep.service.ExclusaoUpdateService;

import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class ExclusaoLayoutService {

    private final ExclusaoLayoutRepository exclusaoLayoutRepository;
    private final ControleApoliceService controleApoliceService;
    private final ControleComplementarAutoService controleComplementarAutoService;
    private final ControleEndossoService controleEndossoService;
    private final ControlePremioService controlePremioService;
    private final ControleSinistroService controleSinistroService;
    private final ControleMovimentoSinistroService controleMovimentoSinistroService;
    private final ExclusaoUpdateService exclusaoUpdateService;

    public ExclusaoLayoutService(ExclusaoLayoutRepository exclusaoLayoutRepository,
                                 ControleApoliceService controleApoliceService,
                                 ControleComplementarAutoService controleComplementarAutoService,
                                 ControleEndossoService controleEndossoService,
                                 ControlePremioService controlePremioService,
                                 ControleSinistroService controleSinistroService,
                                 ControleMovimentoSinistroService controleMovimentoSinistroService,
                                 ExclusaoUpdateService exclusaoUpdateService) {

        this.exclusaoLayoutRepository = exclusaoLayoutRepository;
        this.controleApoliceService = controleApoliceService;
        this.controleComplementarAutoService = controleComplementarAutoService;
        this.controleEndossoService = controleEndossoService;
        this.controlePremioService = controlePremioService;
        this.controleSinistroService = controleSinistroService;
        this.controleMovimentoSinistroService = controleMovimentoSinistroService;
        this.exclusaoUpdateService = exclusaoUpdateService;
    }

    public List<ExclusaoLayout> listarRegistrosExcluir(Integer idtExcCadastro) {
        return exclusaoLayoutRepository.listarRegistrosExcluir(idtExcCadastro);
    }

    @Transactional
    public boolean atualizarStatusTransmissao(ExclusaoLayout exclusaoLayout, ResponseDto resposta) {
        final List<Integer> httpCodeErrors = Arrays.asList(403, 415, 500, 501, 502, 503, 504, 600);
        String situacaoProcessamento = "C";
        String mensagemErroProcessamento = null;

        if (resposta.getCode() != HttpStatus.OK.value() && resposta.getCode() != HttpStatus.NO_CONTENT.value()) {

            // erro interno da maps
            if (httpCodeErrors.contains(resposta.getCode())) {

                situacaoProcessamento = "P";
                mensagemErroProcessamento = ApiResponseHelper.getErrorMessage(resposta);
                atualizarSituacaoRegistro(exclusaoLayout, situacaoProcessamento, mensagemErroProcessamento);

                log.info("Erro: {} - tente novamente mais tarde.", resposta.getMessage());
                return false;
            }

            // erro de negócio
            situacaoProcessamento = "E";

            mensagemErroProcessamento = ApiResponseHelper.getErrorMessage(resposta);
        }
        atualizarSituacaoRegistro(exclusaoLayout, situacaoProcessamento, mensagemErroProcessamento);
        exclusaoLayout.setIdcSitProc(situacaoProcessamento);
        exclusaoLayout.setDesMsgProc(mensagemErroProcessamento);

        atualizarTabelaControleRegistro(exclusaoLayout);

        return true;
    }


    private void atualizarSituacaoRegistro(ExclusaoLayout exclusaoLayout, String situacaoProcessamento, String mensagemErroProcessamento) {
        exclusaoUpdateService.updateExclusaoRegistro(exclusaoLayout.getIdtExcLeiaute(), mensagemErroProcessamento, situacaoProcessamento);
    }

    private void atualizarTabelaControleRegistro(ExclusaoLayout exclusaoLayout) {

        String situacaoExclusao = defineSituacaoProc(exclusaoLayout.getIdcReenviarLeiaute());

        if (exclusaoLayout.getIdcSitProc().equals("E") || exclusaoLayout.getIdcSitProc().equals("P")) {
            situacaoExclusao = "R";
        }

        switch (exclusaoLayout.getDesLeiaute()) {

            case "APOLICE":
                controleApoliceService.atualizarRegistroExclusao(exclusaoLayout, situacaoExclusao);
                break;

            case "COMPLEMENTARAUTOCTR":
            case "COMPLEMENTARAUTOEDS":
                controleComplementarAutoService.atualizarRegistroExclusao(exclusaoLayout, situacaoExclusao);
                break;

            case "ENDOSSO":
                controleEndossoService.atualizarRegistroExclusao(exclusaoLayout, situacaoExclusao);
                break;

            case "PREMIO":
                controlePremioService.atualizarRegistroExclusao(exclusaoLayout, situacaoExclusao);
                break;

            case "SINISTRO":
                controleSinistroService.atualizarRegistroExclusao(exclusaoLayout, situacaoExclusao);
                break;

            case "MOVSINISTRO":
                controleMovimentoSinistroService.atualizarRegistroExclusao(exclusaoLayout, situacaoExclusao);
                break;
            default:
                return;

        }

    }

    private String defineSituacaoProc(String idcReenviarLeiaute) {
        String retorno = "";

        if ("S".equals(idcReenviarLeiaute)) {
            retorno = "A";
        }

        if ("N".equals(idcReenviarLeiaute)) {
            retorno = "X";
        }

        if ("T".equals(idcReenviarLeiaute)) {
            retorno = "B";
        }

        return retorno;
    }

}
