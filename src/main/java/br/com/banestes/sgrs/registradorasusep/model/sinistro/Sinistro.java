package br.com.banestes.sgrs.registradorasusep.model.sinistro;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_SNT_SINISTRO")
public class Sinistro {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_SNT_SINISTRO")
    private Long idtSntSinistro;
    @Column(name = "COD_EMISSOR")
    private Integer codEmissor;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "COD_TIP_ACOMPANHAMENTO")
    private Integer codTipAcompanhamento;
    @Column(name = "COD_EMPRESA")
    private Integer codEmpresa;
    @Column(name = "IDT_PCS_SINISTRO")
    private Integer idtPcsSinistro;
    @Column(name = "COD_SEGURADORA")
    private Integer codSeguradora;
    @Column(name = "NUM_CONTRATO")
    private BigInteger numContrato;
    @Column(name = "ANO_PCS")
    private Integer anoPcs;
    @Column(name = "IDENTIFICADOR_REGISTRO")
    private String identificadorRegistro;
    @Column(name = "NUM_SEQ_PCS")
    private Integer numSeqPcs;
    @Column(name = "COD_RAMO")
    private Integer codRamo;
    @Column(name = "DAT_ACOMPANHAMENTO")
    private String datAcompanhamento;
    @Column(name = "COD_MODALIDADE")
    private Integer codModalidade;
    @Column(name = "IDT_SNT_SINISTRO_ATU")
    private Long idtSntSinistroAtu;
    @Column(name = "CODIGO_SINISTRO")
    private String codigoSinistro;
    @Column(name = "CODIGO_SEGURADORA")
    private String codigoSeguradora;
    @Column(name = "DATA_ENTREGA")
    private String dataEntrega;
    @Column(name = "STATUS")
    private String status;
    @Column(name = "DATA_ALTERACAO_STATUS")
    private String dataAlteracaoStatus;
    @Column(name = "DATA_OCORRENCIA")
    private String dataOcorrencia;
    @Column(name = "DATA_AVISO")
    private String dataAviso;
    @Column(name = "DATA_REGISTRO_SEGURADORA")
    private String dataRegistroSeguradora;
    @Column(name = "DATA_RECLAMACAO_TERCEIRO")
    private String dataReclamacaoTerceiro;
    @Column(name = "JUSTIFICATIVA")
    private String justificativa;
    @Column(name = "DESCRICAO_JUSTIFICATIVA")
    private String descricaoJustificativa;
}
