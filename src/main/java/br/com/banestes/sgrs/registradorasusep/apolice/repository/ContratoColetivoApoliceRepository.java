package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.ContratoColetivoApolice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ContratoColetivoApoliceRepository extends JpaRepository<ContratoColetivoApolice, Integer> {

    List<ContratoColetivoApolice> findAllByidtCtrApoliceAndIdtCtlProc(Long idtCtrApolice, Integer idtCtlProc);

}
