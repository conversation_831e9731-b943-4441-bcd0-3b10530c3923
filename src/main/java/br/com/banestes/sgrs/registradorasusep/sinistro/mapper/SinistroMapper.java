package br.com.banestes.sgrs.registradorasusep.sinistro.mapper;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import br.com.banestes.sgrs.registradorasusep.dto.sinistro.DadoAutoDto;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.DadoEventoDto;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.DadoVistoriaDto;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.DadosBeneficiarioFinalDto;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.DocumentosAfetadosDto;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.JustificativaNegativaDto;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.PessoaAcidentadaDto;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.SinistroDto;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.Sinistro;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroAuto;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroBeneficiario;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroEvento;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroPessoa;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroVistoria;

import java.util.ArrayList;
import java.util.List;

@Component
public class SinistroMapper {

    public SinistroDto toDto(
            Sinistro sinistro,
            List<SinistroPessoa> listaSinistroPessoa,
            List<SinistroEvento> listaSinistroEvento,
            List<SinistroVistoria> listaSinistroVistoria,
            List<DocumentosAfetadosDto> documentosAfetadosDtos,
            List<SinistroAuto> autos,
            List<SinistroBeneficiario> sinistroBeneficiarios) {

        final SinistroDto sinistroDto = new SinistroDto();

        sinistroDto.setIdentificadorRegistro(sinistro.getIdentificadorRegistro());
        sinistroDto.setCodigoSinistro(sinistro.getCodigoSinistro());
        sinistroDto.setCodigoSeguradora(sinistro.getCodigoSeguradora());
        sinistroDto.setDataEntrega(sinistro.getDataEntrega());
        sinistroDto.setStatus(sinistro.getStatus());
        sinistroDto.setDataAlteracaoStatus(sinistro.getDataAlteracaoStatus());
        sinistroDto.setDataOcorrencia(sinistro.getDataOcorrencia());
        sinistroDto.setDataAviso(sinistro.getDataAviso());
        sinistroDto.setDataRegistroSeguradora(sinistro.getDataRegistroSeguradora());
        sinistroDto.setDataReclamacaoTerceiro(sinistro.getDataReclamacaoTerceiro());
        sinistroDto.setPessoasAcidentadas(obterListaPessoasAcidentadas(listaSinistroPessoa));
        sinistroDto.setDadosEvento(obterListaDadosEvento(listaSinistroEvento));
        sinistroDto.setDadosVistorias(obterListaDadosVistorias(listaSinistroVistoria));

        if (CollectionUtils.isNotEmpty(autos)) {
            sinistroDto.setDadosAuto(obterListaDadosAuto(autos));
        }

        if (CollectionUtils.isNotEmpty(sinistroBeneficiarios)) {
            sinistroDto.setBeneficiariosFinais(obterDadosBeneficiarioFinal(sinistroBeneficiarios));
        }

        if (sinistro.getJustificativa() != null) {
            sinistroDto.setJustificativaNegativa(obterJustificativaNegativa(sinistro));
        }

        sinistroDto.setDocumentosAfetados(documentosAfetadosDtos);

        return sinistroDto;
    }


    private List<DadosBeneficiarioFinalDto> obterDadosBeneficiarioFinal(List<SinistroBeneficiario> sinistroBeneficiarios) {

        final List<DadosBeneficiarioFinalDto> values = new ArrayList<>(sinistroBeneficiarios.size());

        sinistroBeneficiarios.forEach(p -> {
            values.add(new DadosBeneficiarioFinalDto(p));
        });

        return values;
    }

    private JustificativaNegativaDto obterJustificativaNegativa(Sinistro sinistro) {
        return new JustificativaNegativaDto(sinistro.getJustificativa(), sinistro.getDescricaoJustificativa());
    }

    private List<PessoaAcidentadaDto> obterListaPessoasAcidentadas(List<SinistroPessoa> listaSinistroPessoa) {

        final List<PessoaAcidentadaDto> pessoasAcidentadasDto = new ArrayList<>(listaSinistroPessoa.size());

        for (SinistroPessoa sinistroPessoa : listaSinistroPessoa) {
            pessoasAcidentadasDto.add(new PessoaAcidentadaDto(sinistroPessoa));
        }

        return pessoasAcidentadasDto;
    }

    private List<DadoEventoDto> obterListaDadosEvento(List<SinistroEvento> listaSinistroEvento) {

        final List<DadoEventoDto> dadosEventosDto = new ArrayList<>(listaSinistroEvento.size());

        for (SinistroEvento sinistroEvento : listaSinistroEvento) {
            dadosEventosDto.add(new DadoEventoDto(sinistroEvento));
        }

        return dadosEventosDto;
    }

    private List<DadoVistoriaDto> obterListaDadosVistorias(List<SinistroVistoria> listaSinistroVistoria) {

        final List<DadoVistoriaDto> dadosVistoriasDto = new ArrayList<>(listaSinistroVistoria.size());

        for (SinistroVistoria sinistroVistoria : listaSinistroVistoria) {
            dadosVistoriasDto.add(new DadoVistoriaDto(sinistroVistoria));
        }

        return dadosVistoriasDto;
    }

    private List<DadoAutoDto> obterListaDadosAuto(List<SinistroAuto> autos) {

        final List<DadoAutoDto> values = new ArrayList<>(autos.size());

        autos.forEach(p -> {
            values.add(new DadoAutoDto(p));
        });

        return values;
    }

}
