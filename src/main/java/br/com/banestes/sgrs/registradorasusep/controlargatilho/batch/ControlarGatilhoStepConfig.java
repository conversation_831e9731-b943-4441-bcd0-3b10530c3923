package br.com.banestes.sgrs.registradorasusep.controlargatilho.batch;

import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ControlarGatilhoStepConfig {

    private final StepBuilderFactory stepBuilderFactory;

    public ControlarGatilhoStepConfig(StepBuilderFactory stepBuilderFactory) {
        this.stepBuilderFactory = stepBuilderFactory;
    }

    @Bean
    public Step controlarGatilhoStep(
            ItemReader<Integer> controlarGatilhoItemReader,
            @Qualifier("controlarGatilhoProcessor") ItemProcessor<Integer, Integer> controlarGatilhoItemProcessor,
            ItemWriter<Integer> controlarGatilhoItemWriter) {

        return stepBuilderFactory
                .get("controlarGatilhoStep")
                .<Integer, Integer>chunk(1)
                .reader(controlarGatilhoItemReader)
                .processor(controlarGatilhoItemProcessor)
                .writer(controlarGatilhoItemWriter)
                .build();
    }
}
