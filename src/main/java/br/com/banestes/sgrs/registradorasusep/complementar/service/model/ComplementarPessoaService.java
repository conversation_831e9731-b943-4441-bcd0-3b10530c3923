package br.com.banestes.sgrs.registradorasusep.complementar.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.complementar.repository.ComplementarPessoaRepository;
import br.com.banestes.sgrs.registradorasusep.model.complementar.ComplementarPessoa;

import java.util.List;

@Service
public class ComplementarPessoaService {

    private final ComplementarPessoaRepository complementarPessoaRepository;

    public ComplementarPessoaService(ComplementarPessoaRepository complementarPessoaRepository) {
        this.complementarPessoaRepository = complementarPessoaRepository;
    }

    public List<ComplementarPessoa> findAllByIdtCtlProcAndIdtCmpAuto(Integer idtCtlProc, Long idtCmpAuto) {
        return complementarPessoaRepository.findAllByIdtCtlProcAndIdtCmpAuto(idtCtlProc, idtCmpAuto);
    }

}
