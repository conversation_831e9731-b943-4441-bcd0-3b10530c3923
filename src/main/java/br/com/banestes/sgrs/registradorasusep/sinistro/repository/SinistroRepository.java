package br.com.banestes.sgrs.registradorasusep.sinistro.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.Sinistro;

import java.util.List;

@Repository
public interface SinistroRepository extends JpaRepository<Sinistro, Integer> {

    @Query("SELECT s"
            + " FROM Sinistro s"
            + " INNER JOIN ControleSinistro c"
            + " ON s.idtSntSinistro = c.idtCtlSinistro"
            + " WHERE (c.idtCtlProc = :idtCtlProc)"
            + " AND (c.idcSitProc = 'S' OR c.idcSitProc = 'P')")
    List<Sinistro> listarSinistrosTransmissao(@Param("idtCtlProc") Integer idtCtlProc);

}
