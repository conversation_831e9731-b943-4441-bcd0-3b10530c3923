package br.com.banestes.sgrs.registradorasusep.service;

import br.com.banestes.common.auth.oauth2.OAuth2ClientCredentials;
import br.com.banestes.common.auth.oauth2.Token;
import br.com.banestes.sgrs.registradorasusep.config.SsoConfig;
import br.com.banestes.sgrs.registradorasusep.exception.GenerateTokenException;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;

@Service
@EnableConfigurationProperties(value = SsoConfig.class)
public class KeycloakService {

    private final OAuth2ClientCredentials auth;
    private final MessageService messageService;

    public KeycloakService(OAuth2ClientCredentials auth, MessageService messageService) {
        this.auth = auth;
        this.messageService = messageService;
    }

    public String getAccessToken(String scope) {

        try {

            final Token token = auth.getToken();

            return token.getAccessToken();
        } catch (Exception e) {
            throw new GenerateTokenException(messageService.message("error.token.sso"), e);
        }
    }

}
