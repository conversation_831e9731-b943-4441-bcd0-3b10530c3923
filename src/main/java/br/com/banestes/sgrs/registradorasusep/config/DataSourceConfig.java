package br.com.banestes.sgrs.registradorasusep.config;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import javax.sql.DataSource;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Configuration
@RequiredArgsConstructor
public class DataSourceConfig {

    private final Environment environment;

    @Primary
    @Bean
    //@ConfigurationProperties(prefix="sqlserver.datasource")
    public DataSource sqlServerDataSource() {

        final DataSourceBuilder<?> dataSourceBuilder = DataSourceBuilder.create();

        dataSourceBuilder.username(new String(Base64.getDecoder().decode(environment.getProperty("sqlserver.datasource.username")), StandardCharsets.ISO_8859_1));
        dataSourceBuilder.password(new String(Base64.getDecoder().decode(environment.getProperty("sqlserver.datasource.password")), StandardCharsets.ISO_8859_1));
        dataSourceBuilder.driverClassName(environment.getProperty("sqlserver.datasource.driverClassName"));
        dataSourceBuilder.url(environment.getProperty("sqlserver.datasource.jdbcUrl"));

        return dataSourceBuilder.build();
    }


    @Bean
    //@ConfigurationProperties(prefix="bdread.sqlserver.datasource")
    public DataSource sqlServerReadDataSource() {

        final DataSourceBuilder<?> dataSourceBuilder = DataSourceBuilder.create();

        dataSourceBuilder.username(new String(Base64.getDecoder().decode(environment.getProperty("bdread.sqlserver.datasource.username")), StandardCharsets.ISO_8859_1));
        dataSourceBuilder.password(new String(Base64.getDecoder().decode(environment.getProperty("bdread.sqlserver.datasource.password")), StandardCharsets.ISO_8859_1));
        dataSourceBuilder.driverClassName(environment.getProperty("bdread.sqlserver.datasource.driverClassName"));
        dataSourceBuilder.url(environment.getProperty("bdread.sqlserver.datasource.jdbcUrl"));

        return dataSourceBuilder.build();
    }
}
