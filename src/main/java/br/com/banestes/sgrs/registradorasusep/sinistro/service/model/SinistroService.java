package br.com.banestes.sgrs.registradorasusep.sinistro.service.model;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.helper.ApiResponseHelper;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.Sinistro;
import br.com.banestes.sgrs.registradorasusep.service.ControleSinistroService;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroRepository;

import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class SinistroService {
    private final SinistroRepository sinistroRepository;
    private final ControleSinistroService controleSinistroService;

    public SinistroService(SinistroRepository sinistroRepository, ControleSinistroService controleSinistroService) {
        this.sinistroRepository = sinistroRepository;
        this.controleSinistroService = controleSinistroService;
    }

    public List<Sinistro> listarSinistrosTransmissao(Integer idtCtlProc) {
        return sinistroRepository.listarSinistrosTransmissao(idtCtlProc);
    }

    @Transactional
    public boolean atualizarStatusTransmissao(Sinistro sinistro, ResponseDto resposta, Character idcOperacao) {
        final List<Integer> httpCodeErrors = Arrays.asList(403, 415, 500, 501, 502, 503, 504, 600);
        String situacaoProcessamento = "R";
        String mensagemErroProcessamento = null;
        if (resposta.getCode() != HttpStatus.OK.value() && resposta.getCode() != HttpStatus.CREATED.value()) {

            if (httpCodeErrors.contains(resposta.getCode())) {

                situacaoProcessamento = "P";
                mensagemErroProcessamento = ApiResponseHelper.getErrorMessage(resposta);
                controleSinistroService.atualizar(sinistro.getIdtSntSinistro(), situacaoProcessamento, mensagemErroProcessamento, idcOperacao);

                log.info("Erro: {} - tente novamente mais tarde.", resposta.getMessage());
                return false;
            }

            // erro de negócio
            situacaoProcessamento = "E";
            mensagemErroProcessamento = ApiResponseHelper.getErrorMessage(resposta);
        }
        controleSinistroService.atualizar(sinistro.getIdtSntSinistro(), situacaoProcessamento, mensagemErroProcessamento, idcOperacao);
        return true;
    }

}
