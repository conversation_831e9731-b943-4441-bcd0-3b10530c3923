package br.com.banestes.sgrs.registradorasusep.model.endosso;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_EDS_ENDOSSO")
public class Endosso {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_EDS_ENDOSSO")
    private Long idtEdsEndosso;
    @Column(name = "NUM_ENDOSSO")
    private BigInteger numEndosso;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "COD_EMPRESA")
    private Integer codEmpresa;
    @Column(name = "CODIGO_SEGURADORA")
    private String codigoSeguradora;
    @Column(name = "COD_EMISSOR")
    private Integer codEmissor;
    @Column(name = "IDENTIFICADOR_REGISTRO")
    private String identificadorRegistro;
    @Column(name = "COD_RAMO")
    private Integer codRamo;
    @Column(name = "NUM_CONTRATO")
    private BigInteger numContrato;
    @Column(name = "COD_MODALIDADE")
    private Integer codModalidade;
    @Column(name = "COD_SEGURADORA")
    private Integer codSeguradora;
    @Column(name = "IDT_EDS_ENDOSSO_ATU")
    private Long idtEdsEndossoAtu;
    @Column(name = "CERTIFICADO_CODIGO")
    private String certificadoCodigo;
    @Column(name = "APOLICE_CODIGO")
    private String apoliceCodigo;
    @Column(name = "COBERTURA_BASICA")
    private String coberturaBasica;
    @Column(name = "NUMERO_SUSEP_APOLICE")
    private String numeroSusepApolice;
    @Column(name = "TIPO_EMISSAO")
    private String tipoEmissao;
    @Column(name = "CODIGO_SEGURADORA_LIDER")
    private String codigoSeguradoraLider;
    @Column(name = "APOLICE_CODIGO_LIDER")
    private String apoliceCodigoLider;
    @Column(name = "DATA_INICIO_DOCUMENTO")
    private String dataInicioDocumento;
    @Column(name = "DATA_EMISSAO")
    private String dataEmissao;
    @Column(name = "CODIGO_FILIAL")
    private String codigoFilial;
    @Column(name = "DATA_TERMINO_DOCUMENTO")
    private String dataTerminoDocumento;
    @Column(name = "LIMITE_MAXIMO_GARANTIA")
    private Double limiteMaximoGarantia;
    @Column(name = "MOEDA_APOLICE")
    private String moedaApolice;
    @Column(name = "LIMITE_MAXIMO_GARANTIA_REAL")
    private Double limiteMaximoGarantiaReal;
    @Column(name = "VALOR_TOTAL")
    private Double valorTotal;
    @Column(name = "CODIGO_PROPOSTA")
    private String codigoProposta;
    @Column(name = "ENDOSSO_DESCRICAO")
    private String endossoDescricao;
    @Column(name = "DATA_ASSINATURA")
    private String dataAssinatura;
    @Column(name = "DATA_PROTOCOLO")
    private String dataProtocolo;
    @Column(name = "VALOR_TOTAL_REAL")
    private Double valorTotalReal;
    @Column(name = "ADICIONAL_FRACIONAMENTO")
    private Double adicionalFracionamento;
    @Column(name = "NUMERO_PARCELAS")
    private Integer numeroParcelas;
    @Column(name = "IOF")
    private Double iof;
    @Column(name = "TIPO_DOCUMENTO_ENDOSSADO")
    private String tipoDocumentoEndossado;
    @Column(name = "ENDOSSO_CODIGO")
    private String endossoCodigo;
    @Column(name = "ENDOSSO_TIPO")
    private String endossoTipo;
    @Column(name = "DATA_INICIO_ENDOSSO")
    private String dataInicioEndosso;
    @Column(name = "ENDOSSO_AVERBAVEL")
    private String endossoAverbavel;
    @Column(name = "DATA_TERMINO_ENDOSSO")
    private String dataTerminoEndosso;

}
