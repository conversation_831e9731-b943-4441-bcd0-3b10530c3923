package br.com.banestes.sgrs.registradorasusep.dto.endosso;

import br.com.banestes.sgrs.registradorasusep.dto.apolice.EnderecoDto;
import br.com.banestes.sgrs.registradorasusep.model.endosso.DependenteEndosso;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class DependenteDto {
    private String parentesco;
    private String email;
    private String tipoDocumento;
    private String documento;
    private String nome;
    private String dataNascimento;
    private EnderecoDto endereco;

    public DependenteDto(DependenteEndosso dependenteEndosso) {
        this.parentesco = dependenteEndosso.getParentesco();
        this.email = dependenteEndosso.getEmail();
        this.tipoDocumento = dependenteEndosso.getTipoDocumento();
        this.documento = dependenteEndosso.getDocumento();
        this.nome = dependenteEndosso.getNome();
        this.dataNascimento = dependenteEndosso.getDataNascimento();
        this.endereco = new EnderecoDto(dependenteEndosso.getEndereco(), dependenteEndosso.getNumero(),
                dependenteEndosso.getComplemento(), dependenteEndosso.getBairro(), dependenteEndosso.getCidade(),
                dependenteEndosso.getUf(), dependenteEndosso.getPais(), dependenteEndosso.getCep());
    }
}
