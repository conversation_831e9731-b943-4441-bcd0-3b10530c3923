package br.com.banestes.sgrs.registradorasusep.model.endosso;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@Entity
@Table(name = "SRO_EDS_OBJETO")
public class ObjetoSeguradoEndosso {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_EDS_OBJETO")
    private Long idtEdsObjeto;
    @Column(name = "IDT_EDS_ENDOSSO")
    private Long idtEdsEndosso;
    @Column(name = "LOCAL_RISCO")
    private String localRisco;
    @Column(name = "NUM_ITEM")
    private Integer numItem;
    @Column(name = "DATA_TERMINO")
    private String dataTermino;
    @Column(name = "NUM_ITM_ACESSORIO")
    private Integer numItmAcessorio;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "TIPO")
    private String tipo;
    @Column(name = "CODIGO")
    private String codigo;
    @Column(name = "DESCRICAO_OBJETO")
    private String descricaoObjeto;
    @Column(name = "DESCRICAO_TIPO")
    private String descricaoTipo;
    @Column(name = "VALOR_REAL")
    private Double valorReal;
    @Column(name = "VALOR")
    private Double valor;
    @Column(name = "DATA_INICIO")
    private String dataInicio;
}
