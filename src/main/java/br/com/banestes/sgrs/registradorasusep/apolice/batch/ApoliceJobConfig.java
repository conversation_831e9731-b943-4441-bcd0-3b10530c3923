package br.com.banestes.sgrs.registradorasusep.apolice.batch;

import br.com.banestes.sgrs.registradorasusep.helper.EnhancedBatchWarmup;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ApoliceJobConfig {

    private final JobBuilderFactory jobBuilderFactory;
    private final EnhancedBatchWarmup enhancedBatchWarmup;

    public ApoliceJobConfig(JobBuilderFactory jobBuilderFactory, EnhancedBatchWarmup enhancedBatchWarmup) {
        this.jobBuilderFactory = jobBuilderFactory;
        this.enhancedBatchWarmup = enhancedBatchWarmup;
    }

    @Bean
    public Job apoliceJob(Step apoliceStep) {

        return jobBuilderFactory
                .get("apoliceJob")
                .start(enhancedBatchWarmup.enhancedWarmupBatchStep())
                .next(apoliceStep)
                .incrementer(new RunIdIncrementer())
                .build();
    }
}
