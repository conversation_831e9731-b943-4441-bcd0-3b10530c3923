package br.com.banestes.sgrs.registradorasusep.apolice.batch;

import br.com.banestes.sgrs.registradorasusep.helper.BatchWarmup;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ApoliceJobConfig {

    private final JobBuilderFactory jobBuilderFactory;
    private final BatchWarmup batchWarmup;

    public ApoliceJobConfig(JobBuilderFactory jobBuilderFactory, BatchWarmup batchWarmup) {
        this.jobBuilderFactory = jobBuilderFactory;
        this.batchWarmup = batchWarmup;
    }

    @Bean
    public Job apoliceJob(Step apoliceStep) {

        return jobBuilderFactory
                .get("apoliceJob")
                .start(batchWarmup.warmupBatchStep())
                .next(apoliceStep)
                .incrementer(new RunIdIncrementer())
                .build();
    }
}
