package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.ApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.helper.ApiResponseHelper;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Apolice;
import br.com.banestes.sgrs.registradorasusep.service.ControleApoliceService;

import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class ApoliceService {
    private final ApoliceRepository apoliceRepository;
    private final ControleApoliceService controleApoliceService;

    public ApoliceService(ApoliceRepository apoliceRepository, ControleApoliceService controleApoliceService) {
        this.apoliceRepository = apoliceRepository;
        this.controleApoliceService = controleApoliceService;
    }

    public List<Apolice> listarApolicesTransmissao(Integer idtCtlProc) {
        return apoliceRepository.listarApolicesTransmissao(idtCtlProc);
    }

    @Transactional
    public boolean atualizarStatusTransmissao(Apolice apolice, ResponseDto resposta, Character idcOperacao) {
        final List<Integer> httpCodeErrors = Arrays.asList(403, 415, 500, 501, 502, 503, 504, 600);
        String situacaoProcessamento = "R";
        String mensagemErroProcessamento = null;
        if (resposta.getCode() != HttpStatus.OK.value() && resposta.getCode() != HttpStatus.CREATED.value()) {

            // erro interno da maps
            if (httpCodeErrors.contains(resposta.getCode())) {
                situacaoProcessamento = "P";
                mensagemErroProcessamento = ApiResponseHelper.getErrorMessage(resposta);
                controleApoliceService.atualizar(apolice.getIdtCtrApolice(), situacaoProcessamento, mensagemErroProcessamento, idcOperacao);

                log.info("Erro: {} - tente novamente mais tarde.", resposta.getMessage());
                return false;
            }

            // erro de negócio
            situacaoProcessamento = "E";
            mensagemErroProcessamento = ApiResponseHelper.getErrorMessage(resposta);
        }
        controleApoliceService.atualizar(apolice.getIdtCtrApolice(), situacaoProcessamento, mensagemErroProcessamento, idcOperacao);
        return true;
    }
}
