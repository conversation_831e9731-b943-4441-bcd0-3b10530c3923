package br.com.banestes.sgrs.registradorasusep.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
@Slf4j
public class JobRetryStarter {

    @Value("${layout:}")
    private String layout;
    @Value("${grupoRamo:}")
    private String grupoRamo;
    @Value("${complemento:}")
    private String complemento;
    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;
    @Value("${job.retry.maxAttempts:3}")
    private Integer maxRetryAttempts;
    @Value("${job.retry.delaySeconds:30}")
    private Integer retryDelaySeconds;
    @Value("${job.retry.enabled:true}")
    private Boolean retryEnabled;
    
    private final JobLauncher jobLauncher;
    private final ApplicationContext context;

    public JobRetryStarter(JobLauncher jobLauncher, ApplicationContext context) {
        this.jobLauncher = jobLauncher;
        this.context = context;
    }

    @Bean
    @Primary
    public ExitStatus jobSelectorWithRetry() {
        if (!retryEnabled) {
            log.info("Sistema de retry desabilitado. Executando job normalmente.");
            return executeJobOnce();
        }

        log.info("Sistema de retry habilitado. Máximo de {} tentativas com intervalo de {} segundos", 
                maxRetryAttempts, retryDelaySeconds);

        ExitStatus lastExitStatus = ExitStatus.FAILED;
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetryAttempts; attempt++) {
            try {
                log.info("=== TENTATIVA {} de {} ===", attempt, maxRetryAttempts);
                
                if (attempt > 1) {
                    log.info("Aguardando {} segundos antes da próxima tentativa...", retryDelaySeconds);
                    Thread.sleep(retryDelaySeconds * 1000L);
                }

                lastExitStatus = executeJob(attempt);
                
                if (lastExitStatus.getExitCode().equals(ExitStatus.COMPLETED.getExitCode())) {
                    log.info(" Job executado com SUCESSO na tentativa {}", attempt);
                    return lastExitStatus;
                }
                
                log.warn(" Tentativa {} falhou com status: {}", attempt, lastExitStatus.getExitCode());
                
            } catch (Exception e) {
                lastException = e;
                log.error(" Erro na tentativa {}: {}", attempt, e.getMessage());
                
                if (attempt == maxRetryAttempts) {
                    log.error(" TODAS as {} tentativas falharam. Último erro:", maxRetryAttempts, e);
                }
            }
        }

        log.error(" Job falhou após {} tentativas. Status final: {}",
                maxRetryAttempts, lastExitStatus.getExitCode());
        
        return lastExitStatus;
    }

    private ExitStatus executeJob(int attempt) throws JobExecutionException {
        try {
            if (modoSimulacao) log.info("Modo simulação ativado");
            log.info("Iniciado layout {}, grupoRamo {} e complemento {} (Tentativa {})", 
                    layout, grupoRamo, complemento, attempt);

            final List<String> rotinasValidas = new ArrayList<>(Arrays.asList(
                    "apolice",
                    "complementar", 
                    "endosso",
                    "liquidacaoPremio",
                    "sinistro",
                    "movimentoSinistro",
                    "exclusao",
                    "liberarControle",
                    "controlarGatilho"));

            final String rotina = rotinasValidas.stream()
                    .filter(p -> p.equalsIgnoreCase(layout))
                    .findFirst()
                    .orElse(null);

            if (rotina == null) {
                log.error("Rotina inválida: {}", layout);
                return ExitStatus.FAILED;
            }

            final Job job = this.context.getBean(rotina.concat("Job"), Job.class);

            // Criar JobParameters únicos para cada tentativa
            JobParameters jobParameters = new JobParametersBuilder()
                    .addLong("timestamp", System.currentTimeMillis())
                    .addLong("attempt", (long) attempt)
                    .toJobParameters();

            final JobExecution run = this.jobLauncher.run(job, jobParameters);

            // Log detalhado do resultado
            logJobExecutionDetails(run, attempt);

            return run.getExitStatus();

        } catch (JobExecutionException e) {
            log.error("Erro na execução do Job (tentativa {}): {}", attempt, e.getMessage());
            throw e;
        }
    }

    private ExitStatus executeJobOnce() {
        try {
            return executeJob(1);
        } catch (JobExecutionException e) {
            log.error("Ocorreu um erro na execução do Job => ", e);
            return ExitStatus.FAILED;
        }
    }

    private void logJobExecutionDetails(JobExecution jobExecution, int attempt) {
        String status = jobExecution.getExitStatus().getExitCode();
        
        log.info("Resultado da tentativa {}: Status={}, Início={}, Fim={}",
                attempt, status, jobExecution.getStartTime(), jobExecution.getEndTime());

        if ("FAILED".equals(status) && !jobExecution.getAllFailureExceptions().isEmpty()) {
            String rootCauseMessage = ExceptionUtils.getRootCauseMessage(
                    jobExecution.getAllFailureExceptions().get(0));
            rootCauseMessage = rootCauseMessage.substring(
                    rootCauseMessage.indexOf(':') + 1);
            
            log.error("Erro detalhado (tentativa {}): {}", attempt, rootCauseMessage);
        }
    }
}
