package br.com.banestes.sgrs.registradorasusep.sinistro.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.Sinistro;
import br.com.banestes.sgrs.registradorasusep.service.EnvioSusepService;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroService;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class TransmissaoSinistroService {

    @Value("${numeroErrosPlataforma}")
    private Integer numeroErrosPlataforma;
    private final SinistroService sinistroService;
    private final ConstrutorSinistroService construtorSinistroService;
    private final EnvioSusepService envioSusepService;

    public TransmissaoSinistroService(SinistroService sinistroService, ConstrutorSinistroService construtorSinistroService, EnvioSusepService envioSusepService) {
        this.sinistroService = sinistroService;
        this.construtorSinistroService = construtorSinistroService;
        this.envioSusepService = envioSusepService;
    }

    public void transmitirSinistros(Integer idtCtlProc, Character idcOperacao) {

        log.info("Controle de Processamento: {}.", idtCtlProc);
        if(idcOperacao.toString().equalsIgnoreCase("i")){
            log.info("Operação: INCLUSÃO");
            log.info("Leiaute Selecionado: Sinistro");
        } else if (idcOperacao.toString().equalsIgnoreCase("a")) {
            log.info("Operação: ALTERACAO");
            log.info("Leiaute Selecionado: Sinistro");
        }


        final List<Sinistro> sinistros = sinistroService.listarSinistrosTransmissao(idtCtlProc);

        Integer errorCount = 0;

        log.info("Número de sinistros a serem processados: {}", sinistros.size());
        log.info("Processando Sinistros...");

        for (Sinistro sinistro : sinistros) {
//            log.info("Sinistro codigo: {}", sinistro.getIdtSntSinistro());
            if (!processaSinistro(sinistro, idcOperacao)) {
                errorCount++;
                if (Objects.equals(errorCount, numeroErrosPlataforma)) {
                    log.info("Processamento interrompido pelo Sinistro: {}", sinistro.getIdtSntSinistro());
                    return;
                }
            }
        }
        log.info("Os sinistros foram processados com sucesso !");
    }

    private boolean processaSinistro(Sinistro sinistro, Character idcOperacao) {
        return sinistroService.atualizarStatusTransmissao(sinistro,
            envioSusepService.transmitir(
                construtorSinistroService.construir(sinistro),
                idcOperacao.equals('I') ? "sinistro" : "sinistro/" + sinistro.getIdentificadorRegistro(),
                idcOperacao.equals('I') ? HttpMethod.POST : HttpMethod.PUT
            ),
            idcOperacao
        );
    }

}
