package br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ObjetoResponsabilidadeDto {
    private String contratoComplementar;
    private Double valorContratoComplementar;
    private Double valorMaximoCoberturaAmbientalDesconhecido;
    private String dataInicioCoberturaAmbientalDesconhecido;
    private String dataTerminoCoberturaAmbientalDesconhecido;
    private Integer prazoCoberturaPassivoAmbiental;
    private String unidadeCoberturaPassivoAmbiental;
    private String diasUteisCoberturaPassivoAmbiental;
    private String danosPoluicaoPorTransporte;
    private String danosOriginadosLocaisTerceiro;
    private String classeProfissional;
    private String aplicacaoRetroatividade;
}
