package br.com.banestes.sgrs.registradorasusep.sinistro.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroEvento;

import java.util.List;

@Repository
public interface SinistroEventoRepository extends JpaRepository<SinistroEvento, Integer> {

    List<SinistroEvento> getAllByIdtCtlProcAndIdtSntSinistro(Integer idtCtlProc, Long idtSntSinistro);

}
