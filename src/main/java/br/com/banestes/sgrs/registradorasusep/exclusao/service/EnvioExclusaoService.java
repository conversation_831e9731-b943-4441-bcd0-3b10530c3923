package br.com.banestes.sgrs.registradorasusep.exclusao.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.error.ResponseUtils;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.service.KeycloakService;

@Slf4j
@Service
public class EnvioExclusaoService {

    private final RestTemplate restTemplate;
    private final String apiBaseUrl;
    private final KeycloakService keycloakService;
    private final ResponseUtils responseUtils;

    public EnvioExclusaoService(RestTemplate restTemplate,
                                @Value("${api.baseurl}") String apiBaseUrl,
                                KeycloakService keycloakService,
                                ResponseUtils responseUtils) {

        this.apiBaseUrl = apiBaseUrl;
        this.restTemplate = restTemplate;
        this.keycloakService = keycloakService;
        this.responseUtils = responseUtils;
    }

    public ResponseDto transmitir(ExclusaoLayout exclusaoLayout, String endPoint) {
        try {
            final HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + keycloakService.getAccessToken(""));
            final String url = String.format("%s/%s/%s", apiBaseUrl, endPoint, exclusaoLayout.getIdentificadorRegistro());

            final HttpEntity<?> request = new HttpEntity<>(httpHeaders);
            final ResponseEntity<String> retorno = restTemplate.exchange(url, HttpMethod.DELETE, request, String.class);
            return responseUtils.gerarResponseDtoExclusao(retorno.getStatusCode(), retorno.getBody(), endPoint);

        } catch (RestClientException e) {
            log.error("Erro na trasnmissão - mensagem: ", e);
            return responseUtils.gerarResponseDtoExclusao(null, e.getMessage(), endPoint);
        }
    }

}
