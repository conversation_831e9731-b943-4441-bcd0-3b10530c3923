package br.com.banestes.sgrs.registradorasusep.sinistro.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroPessoa;

import java.util.List;

@Repository
public interface SinistroPessoaRepository extends JpaRepository<SinistroPessoa, Integer> {

    List<SinistroPessoa> getAllByIdtCtlProcAndIdtSntSinistro(Integer idtCtlProc, Long idtSntSinistro);

}
