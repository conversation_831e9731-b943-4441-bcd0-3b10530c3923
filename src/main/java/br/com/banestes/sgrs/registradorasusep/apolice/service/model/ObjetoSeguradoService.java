package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.ObjetoSeguradoRepository;
import br.com.banestes.sgrs.registradorasusep.model.apolice.ObjetoSegurado;

import java.util.List;

@Service
public class ObjetoSeguradoService {
    private final ObjetoSeguradoRepository objetoSeguradoRepository;

    public ObjetoSeguradoService(ObjetoSeguradoRepository objetoSeguradoRepository) {
        this.objetoSeguradoRepository = objetoSeguradoRepository;
    }

    public List<ObjetoSegurado> findAllByIdtCtlProcAndIdtCtrApolice(Integer idtCtlProc, Long idtCtrApolice) {
        return objetoSeguradoRepository.findAllByIdtCtlProcAndIdtCtrApolice(idtCtlProc, idtCtrApolice);
    }

}
