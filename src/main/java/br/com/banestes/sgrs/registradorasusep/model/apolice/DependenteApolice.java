package br.com.banestes.sgrs.registradorasusep.model.apolice;

import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.Embedded;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import br.com.banestes.sgrs.registradorasusep.model.Endereco;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@ToString
@Table(name = "SRO_CTR_DEPENDENTE")
public class DependenteApolice
{
    @Id
    @Column(name = "IDT_CTR_DEPENDENTE")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idtCtrDependente;

    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;

    @Column(name = "IDT_CTR_RMO_PESSOA", insertable = false, updatable = false)
    private Long idtCtrRmoPessoa;

    @Column(name = "PARENTESCO")
    private String parentesco;

    @Column(name = "EMAIL")
    private String email;

    @Column(name = "TIPO_DOCUMENTO")
    private String tipoDocumento;

    @Column(name = "DOCUMENTO")
    private String documento;

    @Column(name = "NOME")
    private String nome;

    @Column(name = "DATA_NASCIMENTO")
    private String dataNascimento;

    @Column(name = "ENDERECO")
    private String endereco;

    @Column(name = "NUMERO")
    private String numero;

    @Column(name = "COMPLEMENTO")
    private String complemento;

    @Column(name = "BAIRRO")
    private String bairro;

    @Column(name = "CIDADE")
    private String cidade;

    @Column(name = "UF")
    private String uf;

    @Column(name = "PAIS")
    private String pais;

    @Column(name = "CEP")
    private String cep;
}
