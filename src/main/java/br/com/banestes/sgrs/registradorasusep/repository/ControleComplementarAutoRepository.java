package br.com.banestes.sgrs.registradorasusep.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.ControleComplementarAuto;

import java.util.List;

@Repository
public interface ControleComplementarAutoRepository extends JpaRepository<ControleComplementarAuto, Long> {

    @Query("select c from ControleComplementarAuto c "
            + "where (:idtCtlProc is null or c.idtCtlProc = :idtCtlProc) "
            + "and (c.idcSitProc = 'S' OR c.idcSitProc = 'P') ")
    List<ControleComplementarAuto> listarControleConplementarPendentes(@Param("idtCtlProc") Integer numeroProcessamento);

    List<ControleComplementarAuto> findAllByIdtCtlProc(Integer numeroProcessamento);

}
