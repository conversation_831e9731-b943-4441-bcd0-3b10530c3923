package br.com.banestes.sgrs.registradorasusep.complementar.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import br.com.banestes.sgrs.registradorasusep.complementar.service.TransmissaoComplementarService;
import br.com.banestes.sgrs.registradorasusep.exception.RotinaException;
import br.com.banestes.sgrs.registradorasusep.model.ControleComplementarAuto;
import br.com.banestes.sgrs.registradorasusep.model.ControleRotina;
import br.com.banestes.sgrs.registradorasusep.repository.ControleComplementarAutoRepository;
import br.com.banestes.sgrs.registradorasusep.service.ControleRotinaService;
import br.com.banestes.sgrs.registradorasusep.service.MessageService;
import br.com.banestes.sgrs.registradorasusep.service.RotinaService;

import java.util.List;

@Slf4j
@Component("complementarProcessor")
@RequiredArgsConstructor
public class ComplementarProcessor implements ItemProcessor<Integer, Integer> {

    @Value("${layout:}")
    private String layout;
    @Value("${modoSimulacao:false}")
    private Boolean modoSimulacao;
    private final ControleComplementarAutoRepository controleComplementarAutoRepository;
    private final RotinaService rotinaService;
    private final TransmissaoComplementarService transmissaoComplementarService;
    private final ControleRotinaService controleRotinaService;
    private final MessageService messageService;

    @Override
    public Integer process(Integer idtCtlRtn) throws Exception {

        if (idtCtlRtn == -1) {
            return 1;
        }

        log.info("Controle de Rotina: {}", idtCtlRtn);

        if (idtCtlRtn != 0) {
            final ControleRotina controle = controleRotinaService.findByIdtCtlRtn(idtCtlRtn);
            transmissaoComplementarService.transmitirComplementares(controle.getIdtCtlProc(), controle.getIdcOperacao());

            final List<ControleComplementarAuto> controleComplementarAutos = controleComplementarAutoRepository
                    .listarControleConplementarPendentes(controle.getIdtCtlProc());

            if (CollectionUtils.isEmpty(controleComplementarAutos)) {
                rotinaService.atualizar(idtCtlRtn);
            } else {
                if (!modoSimulacao) {
                    throw new RotinaException(messageService.message("error.layout.pendencias"));
                }
            }

            return 0;
        }
        throw new RotinaException("Número do controle de rotina inválido");
    }
}
