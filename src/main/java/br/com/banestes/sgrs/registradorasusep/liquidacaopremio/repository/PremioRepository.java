package br.com.banestes.sgrs.registradorasusep.liquidacaopremio.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.premio.Premio;

import java.util.List;

@Repository
public interface PremioRepository extends JpaRepository<Premio, Integer> {

    @Query("SELECT p"
            + " FROM Premio p"
            + " INNER JOIN ControlePremio c"
            + " ON p.idtPrmPremio = c.idtCtlPremio"
            + " WHERE (c.idtCtlProc = :idtCtlProc)"
            + " AND (c.idcSitProc = 'S' OR c.idcSitProc = 'P')")
    List<Premio> listarLiquidacoesPremioTransmissao(@Param("idtCtlProc") Integer idtCtlProc);

}
