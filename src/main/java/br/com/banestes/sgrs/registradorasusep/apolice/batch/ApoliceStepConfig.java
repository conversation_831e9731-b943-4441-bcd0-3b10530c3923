package br.com.banestes.sgrs.registradorasusep.apolice.batch;

import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ApoliceStepConfig {

    private final StepBuilderFactory stepBuilderFactory;

    public ApoliceStepConfig(StepBuilderFactory stepBuilderFactory) {
        this.stepBuilderFactory = stepBuilderFactory;
    }

    @Bean
    public Step apoliceStep(
            ItemReader<Integer> apoliceItemReader,
            @Qualifier("apoliceProcessor") ItemProcessor<Integer, Integer> apoliceItemProcessor,
            ItemWriter<Integer> apoliceItemWriter) {

        return stepBuilderFactory
                .get("apoliceStep")
                .<Integer, Integer>chunk(1)
                .reader(apoliceItemReader)
                .processor(apoliceItemProcessor)
                .writer(apoliceItemWriter)
                .build();
    }
}
