package br.com.banestes.sgrs.registradorasusep.apolice.batch;

import br.com.banestes.sgrs.registradorasusep.config.RetryConfiguration;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.support.RetryTemplate;

@Configuration
public class ApoliceStepConfig {

    private final StepBuilderFactory stepBuilderFactory;
    private final RetryTemplate retryTemplate;

    @Value("${step.retry.enabled:true}")
    private Boolean stepRetryEnabled;

    public ApoliceStepConfig(StepBuilderFactory stepBuilderFactory,
                           RetryTemplate retryTemplate) {
        this.stepBuilderFactory = stepBuilderFactory;
        this.retryTemplate = retryTemplate;
    }

    @Bean
    public Step apoliceStep(
            ItemReader<Integer> apoliceItemReader,
            @Qualifier("apoliceProcessor") ItemProcessor<Integer, Integer> apoliceItemProcessor,
            ItemWriter<Integer> apoliceItemWriter) {

        if (stepRetryEnabled) {
            return stepBuilderFactory
                    .get("apoliceStep")
                    .<Integer, Integer>chunk(1)
                    .reader(new RetryConfiguration.RetryableItemReader<>(apoliceItemReader, retryTemplate))
                    .processor(new RetryConfiguration.RetryableItemProcessor<>(apoliceItemProcessor, retryTemplate))
                    .writer(new RetryConfiguration.RetryableItemWriter<>(apoliceItemWriter, retryTemplate))
                    .build();
        } else {
            return stepBuilderFactory
                    .get("apoliceStep")
                    .<Integer, Integer>chunk(1)
                    .reader(apoliceItemReader)
                    .processor(apoliceItemProcessor)
                    .writer(apoliceItemWriter)
                    .build();
        }
    }
}
