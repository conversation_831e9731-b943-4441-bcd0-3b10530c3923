package br.com.banestes.sgrs.registradorasusep.apolice.batch;

import br.com.banestes.sgrs.registradorasusep.config.RetryConfiguration;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.skip.SkipPolicy;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.RetryPolicy;
import org.springframework.retry.backoff.BackOffPolicy;
import org.springframework.retry.support.RetryTemplate;

@Configuration
public class ApoliceStepConfig {

    private final StepBuilderFactory stepBuilderFactory;
    private final RetryPolicy stepRetryPolicy;
    private final BackOffPolicy stepBackOffPolicy;
    private final SkipPolicy customSkipPolicy;
    private final RetryTemplate retryTemplate;

    @Value("${step.retry.enabled:true}")
    private Boolean stepRetryEnabled;

    public ApoliceStepConfig(StepBuilderFactory stepBuilderFactory,
                           RetryPolicy stepRetryPolicy,
                           BackOffPolicy stepBackOffPolicy,
                           SkipPolicy customSkipPolicy,
                           RetryTemplate retryTemplate) {
        this.stepBuilderFactory = stepBuilderFactory;
        this.stepRetryPolicy = stepRetryPolicy;
        this.stepBackOffPolicy = stepBackOffPolicy;
        this.customSkipPolicy = customSkipPolicy;
        this.retryTemplate = retryTemplate;
    }

    @Bean
    public Step apoliceStep(
            ItemReader<Integer> apoliceItemReader,
            @Qualifier("apoliceProcessor") ItemProcessor<Integer, Integer> apoliceItemProcessor,
            ItemWriter<Integer> apoliceItemWriter) {

        var stepBuilder = stepBuilderFactory
                .get("apoliceStep")
                .<Integer, Integer>chunk(1)
                .reader(stepRetryEnabled ?
                    new RetryConfiguration.RetryableItemReader<>(apoliceItemReader, retryTemplate) :
                    apoliceItemReader)
                .processor(stepRetryEnabled ?
                    new RetryConfiguration.RetryableItemProcessor<>(apoliceItemProcessor, retryTemplate) :
                    apoliceItemProcessor)
                .writer(stepRetryEnabled ?
                    new RetryConfiguration.RetryableItemWriter<>(apoliceItemWriter, retryTemplate) :
                    apoliceItemWriter);

        if (stepRetryEnabled) {
            stepBuilder
                .retryPolicy(stepRetryPolicy)
                .backOffPolicy(stepBackOffPolicy)
                .skipPolicy(customSkipPolicy);
        }

        return stepBuilder.build();
    }
}
