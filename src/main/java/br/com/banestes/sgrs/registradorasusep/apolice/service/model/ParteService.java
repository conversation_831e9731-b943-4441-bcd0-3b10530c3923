package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.ParteRepository;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Parte;

import java.util.List;

@Service
public class ParteService {
    private final ParteRepository parteRepository;

    public ParteService(ParteRepository parteRepository) {
        this.parteRepository = parteRepository;
    }

    public List<Parte> findAllByIdtCtlProcAndIdtCtrApolice(Integer idtCtlProc, Long idtCtrApolice) {
        return parteRepository.findAllByIdtCtlProcAndIdtCtrApolice(idtCtlProc, idtCtrApolice);
    }

}
