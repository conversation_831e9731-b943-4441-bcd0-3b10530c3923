package br.com.banestes.sgrs.registradorasusep.model.apolice;

import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "SRO_CTR_COBERTURA_FRANQUIA")
public class Franquia {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTR_COBERTURA_FRANQUIA")
    private Integer idtCtrCoberturaFranquia;
    @Column(name = "IDT_CTR_APOLICE")
    private Integer idtCtrApolice;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_CTR_OBJETO_SEG")
    private Integer idtCtrObjetoSeg;
    @Column(name = "custoReal")
    private Double custoReal;
    @Column(name = "grupo")
    private String grupo;
    @Column(name = "ramo")
    private String ramo;
    @Column(name = "coberturaInternaSeguradora")
    private String coberturaInternaSeguradora;
    @Column(name = "outrasDescricao")
    private String outrasDescricao;
    @Column(name = "numeroProcesso")
    private String numeroProcesso;
    @Column(name = "limiteMaximoIndenizacaoReal")
    private Double limiteMaximoIndenizacaoReal;
    @Column(name = "limiteMaximoIndenizacao")
    private Double limiteMaximoIndenizacao;
    @Column(name = "dataInicio")
    private String dataInicio;
    @Column(name = "tipoRisco")
    private String tipoRisco;
    @Column(name = "dataTermino")
    private String dataTermino;
    @Column(name = "iof")
    private Double iof;
    @Column(name = "indiceAtualizacao")
    private String indiceAtualizacao;
    @Column(name = "periodicidadeUnidade")
    private String periodicidadeUnidade;
    @Column(name = "periodicidadeAtualizacao")
    private Integer periodicidadeAtualizacao;
    @Column(name = "coberturaTipos")
    private String coberturaTipos;
    @Column(name = "coberturaPrincipal")
    private String coberturaPrincipal;
    @Column(name = "coberturaCaracteristica")
    private String coberturaCaracteristica;

    @Column(name = "carenciaPeriodicidade")
    private String carenciaPeriodicidade;
    @Column(name = "carenciaPeriodo")
    private Integer carenciaPeriodo;
    @Column(name = "carenciaDataInicio")
    private String carenciaDataInicio;
    @Column(name = "carenciaPeriodicidadeDias")
    private String carenciaPeriodicidadeDias;
    @Column(name = "dataInicioPremio")
    private String dataInicioPremio;
    @Column(name = "carenciaDataTermino")
    private String carenciaDataTermino;
    @Column(name = "valorPremio")
    private Double valorPremio;
    @Column(name = "dataTerminoPremio")
    private String dataTerminoPremio;

    @Column(name = "valorPremioReal")
    private Double valorPremioReal;

    @Column(name = "custo")
    private Double custo;

    @Column(name = "codigo")
    private String codigo;
}
