package br.com.banestes.sgrs.registradorasusep.dto.movimentosinistro;

import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistroAdicional;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MovimentoSinistroAdicionalDto {
    private String tipoAdicional;
    private Double valorMovimentoAdicional;
    private Double valorMovimentoAdicionalReal;

    public MovimentoSinistroAdicionalDto(MovimentoSinistroAdicional movimentoSinistroAdicional) {
        this.tipoAdicional = movimentoSinistroAdicional.getTipoAdicional();
        this.valorMovimentoAdicional = movimentoSinistroAdicional.getValorMovimentoAdicional();
        this.valorMovimentoAdicionalReal = movimentoSinistroAdicional.getValorMovimentoAdicionalReal();
    }
}
