package br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

import br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.cobertura.CoberturaDto;

@Getter
@Setter
public class ObjetoSeguradoDto {
    private String tipo;
    private String codigo;
    private String descricaoTipo;
    private Double valor;
    private String descricaoObjeto;
    private String dataInicio;
    private Double valorReal;
    private String localRisco;
    private List<IdentificacaoAdicionalDto> identificacoesAdicionais;
    private List<CoberturaDto> coberturas;
    private List<ObjetoAeronauticoDto> objetosAeronauticos;
    private List<ObjetoPatrimonialDto> objetosPatrimoniais;
    private List<ObjetoMaritimoDto> objetosMaritimos;
    private List<ObjetoResponsabilidadeDto> objetosResponsabilidades;
    private List<ObjetoRuralDto> objetosRurais;
    private List<RamoPessoaDto> ramosPessoas;
    private String dataTermino;
}
