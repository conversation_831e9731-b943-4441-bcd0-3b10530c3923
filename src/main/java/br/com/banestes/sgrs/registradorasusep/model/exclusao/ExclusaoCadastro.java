package br.com.banestes.sgrs.registradorasusep.model.exclusao;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;
import java.sql.Timestamp;

@Getter
@Setter
@ToString
@Entity
@Table(name = "SRO_EXC_CADASTRO")
public class ExclusaoCadastro {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_EXC_CADASTRO")
    private Integer idtExcCadastro;
    @Column(name = "NUM_CONTRATO")
    private BigInteger numContrato;
    @Column(name = "COD_RAMO")
    private Integer codRamo;
    @Column(name = "COD_EMPRESA")
    private Integer codEmpresa;
    @Column(name = "DAT_HOR_ULT_ATUALIZACAO")
    private Timestamp datHorUltAtualizacao;
    @Column(name = "COD_SEGURADORA")
    private Integer codSeguradora;
    @Column(name = "NUM_MATR_ULT_ATUALIZACAO")
    private String numMatrUltAtualizacao;
    @Column(name = "COD_EMISSOR")
    private Integer codEmissor;
    @Column(name = "IDC_SIT_PROC")
    private String idcSitProc;
    @Column(name = "COD_MODALIDADE")
    private Integer codModalidade;
}
