package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.Parte;

import java.util.List;

@Repository
public interface ParteRepository extends JpaRepository<Parte, Integer> {

    List<Parte> findAllByIdtCtlProcAndIdtCtrApolice(Integer idtCtlProc, Long idtCtrApolice);

}
	