package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.Beneficiario;

import java.util.List;

public interface BeneficiarioRepository extends JpaRepository<Beneficiario, Integer> {

    List<Beneficiario> findAllByIdtCtlProcAndIdtCtrObjeto(Integer idtCtlProc, Long idtCtrObjeto);

}
