package br.com.banestes.sgrs.registradorasusep.apolice.service;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.RamoPessoasApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.model.apolice.RamoPessoasApolice;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RamoPessoasApoliceService {
    private final RamoPessoasApoliceRepository ramoPessoaApoliceRepository;

    public RamoPessoasApoliceService(RamoPessoasApoliceRepository ramoPessoaEndossoRepository) {
        this.ramoPessoaApoliceRepository = ramoPessoaEndossoRepository;
    }

    public List<RamoPessoasApolice> findAllByIdtCtrObjetoAndIdtCtlProc(Long idtCtrObjeto, Integer idtCtlProc) {
        return ramoPessoaApoliceRepository.findAllByIdtCtrObjetoAndIdtCtlProc(idtCtrObjeto, idtCtlProc);
    }

}