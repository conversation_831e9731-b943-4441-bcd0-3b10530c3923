package br.com.banestes.sgrs.registradorasusep.sinistro.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroDocumento;

import java.util.List;

@Repository
public interface SinistroDocumentoRepository extends JpaRepository<SinistroDocumento, Integer> {

    List<SinistroDocumento> getAllByIdtCtlProcAndIdtSntSinistro(Integer idtCtlProc, Long idtSntSinistro);

}
