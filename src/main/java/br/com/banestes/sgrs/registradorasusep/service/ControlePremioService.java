package br.com.banestes.sgrs.registradorasusep.service;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.ControlePremio;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.repository.ControlePremioRepository;

import java.util.Optional;

@Service
public class ControlePremioService {
    private final ControlePremioRepository controlePremioRepository;
    private final PremioUpdateService premioUpdateService;

    public ControlePremioService(PremioUpdateService premioUpdateService,
                                 ControlePremioRepository controlePremioRepository) {

        this.premioUpdateService = premioUpdateService;
        this.controlePremioRepository = controlePremioRepository;
    }

    public void atualizar(Long idtPrmPremio, String situacaoProcessamento, String mensagemErroProcessamento, Character idcOperacao) {
        final Optional<ControlePremio> controlePremioOpt = controlePremioRepository.findById((idtPrmPremio));

        if (controlePremioOpt.isPresent()) {
            final ControlePremio controlePremio = controlePremioOpt.get();

            premioUpdateService.atualizarSituacao(
                controlePremio.getIdtCtlPremio(),
                controlePremio.getIdtCtlProc(),
                situacaoProcessamento,
                mensagemErroProcessamento,
                idcOperacao
            );

            if (("R".equals(situacaoProcessamento) || "E".equals(situacaoProcessamento)) &&
                    (controlePremio.getIdtCtlPremioAtu() != null && controlePremio.getIdtCtlPremioAtu() > 0)) {
                premioUpdateService.updateRegistroCorrecaoIngnorar(controlePremio);
            }

        }
    }

    public void atualizarRegistroExclusao(ExclusaoLayout exclusaoLayout, String situacaoProcessamento) {
        final ControlePremio controlePremio = controlePremioRepository.findById(exclusaoLayout.getIdtLeiaute()).orElse(null);

        if (controlePremio != null) {
            premioUpdateService.updateExclusaoRegistro(controlePremio.getIdtCtlPremio(), exclusaoLayout.getNumMatrUltAtualizacao(), situacaoProcessamento);
        }
    }

}
