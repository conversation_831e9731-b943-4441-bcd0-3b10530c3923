package br.com.banestes.sgrs.registradorasusep.helper;

import br.com.banestes.sgrs.registradorasusep.dto.ErroDto;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;

public final class ApiResponseHelper {

    private ApiResponseHelper() {
        throw new IllegalStateException("Utility class");
    }

    private static final Integer MESSAGE_LIMIT = 1023;

    public static String getErrorMessage(ResponseDto responseDto) {

        Integer tamanho = 1;

        if (responseDto.getErrors() != null) {
            tamanho += responseDto.getErrors().size();
        }
        final StringBuilder mensagemErro = new StringBuilder(tamanho);

        if (responseDto.getMessage() != null) {
            mensagemErro.append(responseDto.getMessage().concat(": "));
        }

        if (tamanho > 1) {
            for (ErroDto erro : responseDto.getErrors()) {

                if (erro.getField() != null) {
                    mensagemErro.append(erro.getField());
                }

                if (erro.getMessage() != null) {
                    mensagemErro.append(" - ".concat(erro.getMessage()).concat(" , "));
                }
            }
        }
        if (mensagemErro.length() > MESSAGE_LIMIT) {
            return mensagemErro.toString().substring(0, Math.min(mensagemErro.length(), MESSAGE_LIMIT));
        }
        return mensagemErro.toString();
    }
}
