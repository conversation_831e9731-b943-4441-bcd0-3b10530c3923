package br.com.banestes.sgrs.registradorasusep.endosso.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.endosso.Endosso;

import java.util.List;

@Repository
public interface EndossoRepository extends JpaRepository<Endosso, Integer> {

    @Query("SELECT e"
            + " FROM Endosso e"
            + " INNER JOIN ControleEndosso c"
            + " ON e.idtEdsEndosso = c.idtCtlEndosso"
            + " WHERE (c.idtCtlProc = :idtCtlProc)"
            + " AND (c.idcSitProc = 'S' OR c.idcSitProc = 'P')")
    List<Endosso> listarEndossosTransmissao(@Param("idtCtlProc") Integer idtCtlProc);

}
