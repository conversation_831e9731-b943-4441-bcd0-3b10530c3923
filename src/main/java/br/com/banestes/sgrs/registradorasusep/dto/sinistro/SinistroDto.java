package br.com.banestes.sgrs.registradorasusep.dto.sinistro;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

@Getter
@Setter
public class SinistroDto {
    private String identificadorRegistro;
    private String codigoSinistro;
    private String codigoSeguradora;
    private String dataEntrega;
    private String status;
    private String dataAlteracaoStatus;
    private String dataOcorrencia;
    private String dataAviso;
    private String dataRegistroSeguradora;
    private String dataReclamacaoTerceiro;
    private JustificativaNegativaDto justificativaNegativa;
    private List<PessoaAcidentadaDto> pessoasAcidentadas;
    private List<DadoEventoDto> dadosEvento;
    private List<DadoVistoriaDto> dadosVistorias;
    private List<DadoAutoDto> dadosAuto;
    private List<DocumentosAfetadosDto> documentosAfetados;
    private List<DadosBeneficiarioFinalDto> beneficiariosFinais;
}
