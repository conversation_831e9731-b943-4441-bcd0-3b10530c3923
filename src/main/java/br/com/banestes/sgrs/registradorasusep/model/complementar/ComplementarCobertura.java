package br.com.banestes.sgrs.registradorasusep.model.complementar;

import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.math.BigInteger;

@Data
@NoArgsConstructor
@Entity
@Table(name = "SRO_CMP_COBERTURA_AUTO")
public class ComplementarCobertura {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CMP_COBERTURA")
    private Long idtCmpCobertura;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "IDT_CMP_AUTO")
    private Long idtCmpAuto;
    @Column(name = "COD_COBERTURA")
    private Integer codCobertura;
    @Column(name = "GRUPO")
    private String grupo;
    @Column(name = "RAMO")
    private String ramo;
    @Column(name = "CODIGO")
    private String codigo;
    @Column(name = "OUTRAS_DESCRICAO")
    private String outrasDescricao;
    @Column(name = "COBERTURA_INTERNA_SEGURADORA")
    private String coberturaInternaSeguradora;
    @Column(name = "NUMERO_PROCESSO")
    private String numeroProcesso;
    @Column(name = "LIMITE_MAXIMO_INDENIZACAO")
    private BigDecimal limiteMaximoIndenizacao;
    @Column(name = "PERIODICIDADE_ATUALIZACAO")
    private Integer periodicidadeAtualizacao;
    @Column(name = "LIMITE_MAXIMO_INDENIZACAO_REAL")
    private BigDecimal limiteMaximoIndenizacaoReal;
    @Column(name = "COBERTURA_CARACTERISTICA")
    private String coberturaCaracteristica;
    @Column(name = "DATA_TERMINO")
    private String dataTermino;
    @Column(name = "COBERTURA_PRINCIPAL")
    private String coberturaPrincipal;
    @Column(name = "INDICE_ATUALIZACAO")
    private String indiceAtualizacao;
    @Column(name = "COBERTURA_TIPO")
    private String coberturaTipo;
    @Column(name = "DATA_INICIO")
    private String dataInicio;
    @Column(name = "CARENCIA_PERIODO")
    private Integer carenciaPeriodo;
    @Column(name = "TIPO_INDENIZACAO")
    private String tipoIndenizacao;
    @Column(name = "CARENCIA_PERIODICIDADE")
    private String carenciaPeriocidade;
    @Column(name = "PERIODICIDADE_UNIDADE")
    private String periodicidadeUnidade;
    @Column(name = "CARENCIA_PERIODICIDADE_DIAS")
    private String carenciaPeriocidadeDias;
    @Column(name = "CARENCIA_DATA_INICIO")
    private String carenciaDataInicio;
    @Column(name = "CARENCIA_DATA_TERMINO")
    private String carenciaDataTermino;
    @Column(name = "VALOR_PREMIO")
    private BigDecimal valorPremio;
    @Column(name = "VALOR_PREMIO_REAL")
    private BigDecimal valorPremioReal;
    @Column(name = "IOF")
    private BigDecimal iof;
    @Column(name = "CUSTO")
    private BigDecimal custo;
    @Column(name = "CUSTO_REAL")
    private BigDecimal custoReal;
    @Column(name = "PERCENTUAL_INDENIZACAO_PARCIAL")
    private BigDecimal percentualIndenizacaoParcial;
    @Column(name = "PERCENTUAL_LMI")
    private BigDecimal percentualLmi;
    @Column(name = "DIAS_COBERTURA")
    private String diasCobertura;
    @Column(name = "COBERTURA_VINCULADA")
    private String coberturaVinculada;
    @Column(name = "PERIODICIDADE_PREMIO")
    private String periodicidadePremio;

}
