package br.com.banestes.sgrs.registradorasusep.dto.endosso;

import br.com.banestes.sgrs.registradorasusep.dto.apolice.EnderecoDto;
import br.com.banestes.sgrs.registradorasusep.model.endosso.IntermediarioEndosso;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class IntermediarioDto {
    private String tipo;
    private String documento;
    private String tipoDocumento;
    private String codigo;
    private String nome;
    private String email;
    private Double valorComissao;
    private Double valorComissaoReal;
    private EnderecoDto endereco;

    public IntermediarioDto(IntermediarioEndosso intermediario) {
        this.documento = intermediario.getDocumento();
        this.endereco = new EnderecoDto(intermediario.getEndereco(), intermediario.getNumero(),
                intermediario.getComplemento(), intermediario.getBairro(), intermediario.getCidade(),
                intermediario.getUf(), "BRASIL", intermediario.getCep());
        this.nome = intermediario.getNome();
        this.tipo = intermediario.getTipo();
        this.valorComissao = intermediario.getValorComissao();
        this.valorComissaoReal = intermediario.getValorComissaoReal();
        this.codigo = intermediario.getCodigo();
        this.email = intermediario.getEmail();
        this.tipoDocumento = intermediario.getTipoDocumento();
    }
}
