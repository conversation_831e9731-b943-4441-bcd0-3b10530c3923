package br.com.banestes.sgrs.registradorasusep.complementar.service.model;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.complementar.repository.ComplementarRepository;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.helper.ApiResponseHelper;
import br.com.banestes.sgrs.registradorasusep.model.complementar.Complementar;
import br.com.banestes.sgrs.registradorasusep.service.ControleComplementarAutoService;

import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
public class ComplementarService {

    @Value("${complemento:}")
    private String complemento;
    private final ComplementarRepository complementarRepository;
    private final ControleComplementarAutoService controleComplementarAutoService;

    public ComplementarService(ComplementarRepository complementarRepository,
                               ControleComplementarAutoService controleComplementarAutoService) {

        this.complementarRepository = complementarRepository;
        this.controleComplementarAutoService = controleComplementarAutoService;
    }

    public List<Complementar> listarComplementarTransmissao(Integer idtCtlProc) {
        return complementarRepository.listarComplementarTransmissao(idtCtlProc, complemento);
    }

    @Transactional
    public boolean atualizarStatusTransmissao(Complementar complementar, ResponseDto resposta, Character idcOperacao) {
        final List<Integer> httpCodeErrors = Arrays.asList(403, 415, 500, 501, 502, 503, 504, 600);
        String situacaoProcessamento = "R";
        String mensagemErroProcessamento = null;

        if (resposta.getCode() != HttpStatus.OK.value() && resposta.getCode() != HttpStatus.CREATED.value()) {
            // erro interno da maps
            if (httpCodeErrors.contains(resposta.getCode())) {
                situacaoProcessamento = "P";
                mensagemErroProcessamento = ApiResponseHelper.getErrorMessage(resposta);
                controleComplementarAutoService.atualizar(complementar.getIdtCmpAuto(), situacaoProcessamento, mensagemErroProcessamento, idcOperacao);
                log.info("Erro: {} - tente novamente mais tarde.", resposta.getMessage());
                return false;
            }

            // erro de negócio
            situacaoProcessamento = "E";
            mensagemErroProcessamento = ApiResponseHelper.getErrorMessage(resposta);
        }
        controleComplementarAutoService.atualizar(complementar.getIdtCmpAuto(), situacaoProcessamento, mensagemErroProcessamento, idcOperacao);
        return true;
    }
}
