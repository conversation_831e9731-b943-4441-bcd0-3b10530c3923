package br.com.banestes.sgrs.registradorasusep.complementar.batch;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ComplementarJobConfig {

    private final JobBuilderFactory jobBuilderFactory;

    public ComplementarJobConfig(JobBuilderFactory jobBuilderFactory) {
        this.jobBuilderFactory = jobBuilderFactory;
    }

    @Bean
    public Job complementarJob(Step complementarStep) {

        return jobBuilderFactory
                .get("complementarJob")
                .start(complementarStep)
                .incrementer(new RunIdIncrementer())
                .build();
    }
}
