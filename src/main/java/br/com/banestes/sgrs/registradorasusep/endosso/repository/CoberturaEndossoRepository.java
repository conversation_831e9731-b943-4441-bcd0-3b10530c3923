package br.com.banestes.sgrs.registradorasusep.endosso.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.endosso.CoberturaEndosso;

import java.util.List;

@Repository
public interface CoberturaEndossoRepository extends JpaRepository<CoberturaEndosso, Integer> {

    List<CoberturaEndosso> findAllByIdtCtlProcAndIdtEdsObjeto(Integer idtCtlProc, Long idtEdsObjeto);

}
