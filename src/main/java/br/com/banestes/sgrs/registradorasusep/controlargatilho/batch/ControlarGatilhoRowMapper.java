package br.com.banestes.sgrs.registradorasusep.controlargatilho.batch;

import org.springframework.jdbc.core.RowMapper;

import br.com.banestes.sgrs.registradorasusep.helper.BatchHelper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class ControlarGatilhoRowMapper implements RowMapper<Integer> {
    public Integer mapRow(ResultSet rs, int rowNum) throws SQLException {
        return BatchHelper.extrairRetornoStoredProcedureControlarGatilho(rs);
    }
}
