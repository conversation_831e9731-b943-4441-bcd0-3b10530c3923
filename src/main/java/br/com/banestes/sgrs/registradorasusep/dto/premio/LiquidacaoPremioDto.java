package br.com.banestes.sgrs.registradorasusep.dto.premio;

import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;

@Getter
@Setter
public class LiquidacaoPremioDto {
    private String identificadorRegistro;
    private String codigoSeguradora;
    private String apoliceCodigo;
    private String certificadoCodigo;
    private String endossoCodigo;
    private String identificadorMovimento;
    private String moeda;
    private BigDecimal valorMovimento;
    private BigDecimal valorMovimentoReal;
    private String dataMovimento;
    private Integer numeroParcelaMovimento;
    private String dataVencimento;
    private String tipoMovimento;
    private String estipulantesPagador;
    private String periodoPagamento;
    private String origem;
    private String documentoPagador;
    private String tipoDocumentoPagador;
    private String nomePagador;
    private String codigoInstituicao;
    private String meioPagamento;
    private ValorEstipulanteSeguradoDto valorEstipulanteSegurado;
}
