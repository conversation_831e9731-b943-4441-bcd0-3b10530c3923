package br.com.banestes.sgrs.registradorasusep.dto.apolice.objetosegurado.cobertura;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class AdicionalCoberturaDto {
    private String baseIndenizacao;
    private String descricaoBaseIndenizacao;
    private String dataLimiteRetroatividade;
    private Integer prazoRetroatividade;
    private String unidadeTempoRetroatividade;
    private String indicadorDiasRetroatividade;
    private String dataInicioPrazoComplementar;
    private String dataTerminoPrazoComplementar;
    private Integer prazoComplementar;
    private String unidadePrazoComplementar;
    private String indicadorDiasPrazoComplementar;
    private String dataInicioPrazoSuplementar;
    private String dataTerminoPrazoSuplementar;
    private Integer prazoSuplementar;
    private String unidadePrazoSuplementar;
    private String indicadorDiasPrazoSuplementar;
    private String indicacaoAdvogado;
    private String descricaoFormaIndicacaoAdvogado;
    private String coberturaSegundoRisco;
}
