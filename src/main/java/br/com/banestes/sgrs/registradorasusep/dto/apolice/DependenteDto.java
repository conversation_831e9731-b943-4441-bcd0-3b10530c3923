package br.com.banestes.sgrs.registradorasusep.dto.apolice;

import br.com.banestes.sgrs.registradorasusep.model.apolice.DependenteApolice;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class DependenteDto {
    private String parentesco;
    private String email;
    private String tipoDocumento;
    private String documento;
    private String nome;
    private String dataNascimento;
    private EnderecoDto endereco;

    public DependenteDto(DependenteApolice dependenteApolice) {
        this.parentesco = dependenteApolice.getParentesco();
        this.email = dependenteApolice.getEmail();
        this.tipoDocumento = dependenteApolice.getTipoDocumento();
        this.documento = dependenteApolice.getDocumento();
        this.nome = dependenteApolice.getNome();
        this.dataNascimento = dependenteApolice.getDataNascimento();
        this.endereco = new EnderecoDto(dependenteApolice.getEndereco(), dependenteApolice.getNumero(),
                dependenteApolice.getComplemento(), dependenteApolice.getBairro(), dependenteApolice.getCidade(),
                dependenteApolice.getUf(), dependenteApolice.getPais(), dependenteApolice.getCep());
    }
}
