package br.com.banestes.sgrs.registradorasusep.apolice.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.apolice.service.model.ApoliceService;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Apolice;
import br.com.banestes.sgrs.registradorasusep.service.EnvioSusepService;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class TransmissaoApoliceService {

    @Value("${numeroErrosPlataforma}")
    private Integer numeroErrosPlataforma;

    private final ApoliceService apoliceService;
    private final ConstrutorApoliceService construtorApoliceService;
    private final EnvioSusepService envioSusepService;

    public TransmissaoApoliceService(ApoliceService apoliceService, ConstrutorApoliceService construtorApoliceService, EnvioSusepService envioSusepService) {
        this.apoliceService = apoliceService;
        this.construtorApoliceService = construtorApoliceService;
        this.envioSusepService = envioSusepService;
    }

    public void transmitirApolices(Integer idtCtlProc, Character idcOperacao) {
        log.info("Controle de Processamento: {}.", idtCtlProc);
        if(idcOperacao.toString().equalsIgnoreCase("i")){
            log.info("Operação: INCLUSÃO");
            log.info("Leiaute Selecionado: Apólices");
        } else if (idcOperacao.toString().equalsIgnoreCase("a")) {
            log.info("Operação: ALTERACAO");
            log.info("Leiaute Selecionado: Apólices");
        }

        Integer errorCount = 0;

        final List<Apolice> apolices = apoliceService.listarApolicesTransmissao(idtCtlProc);

        log.info("Numero de apolices a serem processadas: {}", apolices.size());
        log.info("Processando Apolices...");

        for (Apolice apolice : apolices) {
//            log.info("Apolice codigo: {} - numero SUSEP {}", apolice.getApoliceCodigo(), apolice.getNumeroSusepApolice());
            if (!processaApolice(apolice, idcOperacao)) {
                errorCount++;
                if (Objects.equals(errorCount, numeroErrosPlataforma)) {
                    log.info("Processamento interrompido na apolice {}", apolice.getNumeroSusepApolice());
                    return;
                }
            }
        }
        log.info("As apolices foram processadas com sucesso !");
    }

    private boolean processaApolice(Apolice apolice, Character idcOperacao) {
        return apoliceService.atualizarStatusTransmissao(
            apolice,
            envioSusepService.transmitir(
                construtorApoliceService.construir(apolice),
                idcOperacao.equals('I') ? "apolice" : "apolice/" + apolice.getIdentificadorRegistro(),
                idcOperacao.equals('I') ? HttpMethod.POST : HttpMethod.PUT
            ),
            idcOperacao
        );
    }
}
