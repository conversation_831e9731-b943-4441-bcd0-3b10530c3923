package br.com.banestes.sgrs.registradorasusep.sinistro.batch;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemWriter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class SinistroWriter {
    @Bean
    public ItemWriter<Integer> sinistroItemWriter() {
        return retornos -> log.info("ItemWriter: {}", retornos.get(0));
    }
}
