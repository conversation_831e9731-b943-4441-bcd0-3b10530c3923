package br.com.banestes.sgrs.registradorasusep.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;
import java.sql.Timestamp;

@Data
@NoArgsConstructor
@Entity
@Table(name = "SRO_CTL_CMP_AUTO")
public class ControleComplementarAuto {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTL_CMP_AUTO")
    private Long idtCtlCmpAuto;
    @Column(name = "COD_MODALIDADE")
    private Integer codModalidade;
    @Column(name = "NUM_CONTRATO")
    private BigInteger numContrato;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "COD_EMISSOR")
    private Integer codEmissor;
    @Column(name = "NUM_ENDOSSO")
    private Integer numEndosso;
    @Column(name = "COD_SEGURADORA")
    private Integer codSeguradora;
    @Column(name = "COD_EMPRESA")
    private Integer codEmpresa;
    @Column(name = "COD_RAMO")
    private Integer codRamo;
    @Column(name = "NUM_ITEM")
    private Integer numItem;
    @Column(name = "IDT_CTL_CMP_AUTO_ATU")
    private Long idtCtlCmpAutoAtu;
    @Column(name = "IDC_SIT_PROC")
    private String idcSitProc;
    @Column(name = "DES_MSG_PROC")
    private String desMsgProc;
    @Column(name = "NUM_MATR_ULT_ATUALIZACAO")
    private Integer numMatrUltAtualizacao;
    @Column(name = "DAT_HOR_ULT_ATUALIZACAO")
    private Timestamp datHorUltAtualizacao;
    @Column(name = "DAT_HOR_REGISTRO")
    private Timestamp datHorRegistro;
}
