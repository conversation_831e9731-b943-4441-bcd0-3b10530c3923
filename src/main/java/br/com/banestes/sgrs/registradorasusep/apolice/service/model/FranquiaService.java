package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.FranquiaRepository;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Cobertura;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Franquia;

import java.util.List;

@Service
public class FranquiaService {
    private final FranquiaRepository franquiaRepository;

    public FranquiaService(FranquiaRepository franquiaRepository) {
        this.franquiaRepository = franquiaRepository;
    }

    public List<Franquia> findAllByIdtCtlProcAndIdtCtrObjetoSeg(Cobertura cobertura) {
        return franquiaRepository.findAllByIdtCtlProcAndIdtCtrObjetoSeg(cobertura.getIdtCtlProc(),
                cobertura.getIdtCtrObjeto());
    }

}
