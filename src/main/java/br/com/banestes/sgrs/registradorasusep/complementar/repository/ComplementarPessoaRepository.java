package br.com.banestes.sgrs.registradorasusep.complementar.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.complementar.ComplementarPessoa;

import java.util.List;

@Repository
public interface ComplementarPessoaRepository extends JpaRepository<ComplementarPessoa, Integer> {

    List<ComplementarPessoa> findAllByIdtCtlProcAndIdtCmpAuto(Integer idtCtlProc, Long idtCmpAuto);

}
