package br.com.banestes.sgrs.registradorasusep.liberarcontrole.batch;

import org.springframework.jdbc.core.RowMapper;
import br.com.banestes.sgrs.registradorasusep.helper.BatchHelper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class LiberarControleRowMapper implements RowMapper<Integer> {
    public Integer mapRow(ResultSet rs, int rowNum) throws SQLException {
        return BatchHelper.extrairRetornoStoredProcedureLiberar(rs);
    }
}
