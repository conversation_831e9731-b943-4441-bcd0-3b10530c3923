package br.com.banestes.sgrs.registradorasusep.endosso.service.model;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.EndossoRepository;
import br.com.banestes.sgrs.registradorasusep.helper.ApiResponseHelper;
import br.com.banestes.sgrs.registradorasusep.model.endosso.Endosso;
import br.com.banestes.sgrs.registradorasusep.service.ControleEndossoService;

import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class EndossoService {
    private final EndossoRepository endossoRepository;
    private final ControleEndossoService controleEndossoService;

    public EndossoService(EndossoRepository endossoRepository,
                          ControleEndossoService controleEndossoService) {

        this.endossoRepository = endossoRepository;
        this.controleEndossoService = controleEndossoService;
    }

    public List<Endosso> listarEndossosTransmissao(Integer idtCtlProc) {
        return endossoRepository.listarEndossosTransmissao(idtCtlProc);
    }

    @Transactional
    public boolean atualizarStatusTransmissao(Endosso endosso, ResponseDto resposta, Character idcOperacao) {

        final List<Integer> httpCodeErrors = Arrays.asList(403, 415, 500, 501, 502, 503, 504, 600);
        String situacaoProcessamento = "R";
        String mensagemErroProcessamento = null;

        if (resposta.getCode() != HttpStatus.OK.value() && resposta.getCode() != HttpStatus.CREATED.value()) {
            // erro interno da maps
            if (httpCodeErrors.contains(resposta.getCode())) {
                situacaoProcessamento = "P";
                mensagemErroProcessamento = ApiResponseHelper.getErrorMessage(resposta);
                controleEndossoService.atualizar(endosso.getIdtEdsEndosso(), situacaoProcessamento, mensagemErroProcessamento, idcOperacao);

                log.info("Erro: {} - tente novamente mais tarde.", resposta.getMessage());
                return false;
            }

            // erro de negócio
            situacaoProcessamento = "E";
            mensagemErroProcessamento = ApiResponseHelper.getErrorMessage(resposta);
        }
        controleEndossoService.atualizar(endosso.getIdtEdsEndosso(), situacaoProcessamento, mensagemErroProcessamento, idcOperacao);
        return true;
    }
}
