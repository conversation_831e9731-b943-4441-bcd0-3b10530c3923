package br.com.banestes.sgrs.registradorasusep.service;

import br.com.banestes.sgrs.registradorasusep.exception.RotinaException;
import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.ControleApolice;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.repository.ControleApoliceRepository;

import java.util.List;
import java.util.Optional;

@Service
public class ControleApoliceService {
    private final ControleApoliceRepository controleApoliceRepository;
    private final ApoliceUpdateService apoliceUpdateService;

    public ControleApoliceService(ApoliceUpdateService apoliceUpdateService,
                                  ControleApoliceRepository controleApoliceRepository) {

        this.apoliceUpdateService = apoliceUpdateService;
        this.controleApoliceRepository = controleApoliceRepository;
    }

    public List<ControleApolice> listarControleApolicePendentes(Integer numeroProcessamento) {
        return controleApoliceRepository.listarControleApolicePendentes(numeroProcessamento);
    }

    public void atualizar(Long idtCtrApolice, String situacaoProcessamento, String mensagemErroProcessamento, Character idcOperacao) {
        final Optional<ControleApolice> controleApoliceOpt = controleApoliceRepository.findById(idtCtrApolice);

        if (controleApoliceOpt.isPresent()) {
            final ControleApolice controleApolice = controleApoliceOpt.get();
            apoliceUpdateService.atualizarSituacao(controleApolice.getIdtCtlApolice(), controleApolice.getIdtCtlProc(),
                    situacaoProcessamento, mensagemErroProcessamento, idcOperacao);

            Long idtCtlApoliceAtu = controleApolice.getIdtCtlApoliceAtu();
            if (("R".equals(situacaoProcessamento) || "E".equals(situacaoProcessamento)) &&
                    idtCtlApoliceAtu != null && idtCtlApoliceAtu > 0) {
                apoliceUpdateService.updateRegistroCorrecaoIngnorar(controleApolice);
            }

            // Qualquer erro de plataforma (P) onde tipo contrato do controle da apolice = AC (coletivo), abortar rotina
            if ("P".equals(situacaoProcessamento) && "AC".equals(controleApolice.getTipContrato())) {
              throw new RotinaException("Erro em apólice coletiva detectado. Abortando rotina");
            }
        }
    }



    public void atualizarRegistroExclusao(ExclusaoLayout exclusaoLayout, String situacaoProcessamento) {
        final ControleApolice controleApolice = controleApoliceRepository.findById(exclusaoLayout.getIdtLeiaute()).orElse(null);
        if (controleApolice != null) {
            apoliceUpdateService.updateExclusaoRegistro(controleApolice.getIdtCtlApolice(), exclusaoLayout.getNumMatrUltAtualizacao(), situacaoProcessamento);
        }
    }


}
