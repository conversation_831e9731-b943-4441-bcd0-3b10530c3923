package br.com.banestes.sgrs.registradorasusep.movimentosinistro.batch;

import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MovimentoSinistroStepConfig {
    private final StepBuilderFactory stepBuilderFactory;

    public MovimentoSinistroStepConfig(StepBuilderFactory stepBuilderFactory) {
        this.stepBuilderFactory = stepBuilderFactory;
    }

    @Bean
    public Step movimentoSinistroStep(
            ItemReader<Integer> movimentoSinistroItemReader,
            @Qualifier("movimentoSinistroProcessor") ItemProcessor<Integer, Integer> movimentoSinistroItemProcessor,
            ItemWriter<Integer> movimentoSinistroItemWriter) {

        return stepBuilderFactory
                .get("movimentoSinistroStep")
                .<Integer, Integer>chunk(1)
                .reader(movimentoSinistroItemReader)
                .processor(movimentoSinistroItemProcessor)
                .writer(movimentoSinistroItemWriter)
                .build();
    }
}
