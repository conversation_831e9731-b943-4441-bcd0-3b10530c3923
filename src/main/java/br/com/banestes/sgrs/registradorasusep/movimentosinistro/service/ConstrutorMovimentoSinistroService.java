package br.com.banestes.sgrs.registradorasusep.movimentosinistro.service;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.dto.movimentosinistro.MovimentoSinistroDto;
import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.mapper.MovimentoSinistroMapper;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.service.model.MovimentoSinistroAdicionalService;

@Service
public class ConstrutorMovimentoSinistroService {
    private final MovimentoSinistroMapper movimentoSinistroMapper;
    private final MovimentoSinistroAdicionalService movimentoSinistroAdicionalService;

    public ConstrutorMovimentoSinistroService(MovimentoSinistroMapper movimentoSinistroMapper,
                                              MovimentoSinistroAdicionalService movimentoSinistroAdicionalService) {

        this.movimentoSinistroMapper = movimentoSinistroMapper;
        this.movimentoSinistroAdicionalService = movimentoSinistroAdicionalService;
    }

    public MovimentoSinistroDto construir(MovimentoSinistro movimentoSinistro) {
        return movimentoSinistroMapper.toDto(movimentoSinistro,
                movimentoSinistroAdicionalService.getAllByIdtCtlProcAndIdtSntMovSinistro(movimentoSinistro.getIdtCtlProc(),
                        movimentoSinistro.getIdtSntMovSinistro()));
    }

}
