package br.com.banestes.sgrs.registradorasusep.dto.sinistro;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroVistoria;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DadoVistoriaDto {
    private String dataVistoria;
    private String localVistoria;
    private String ufVistoria;
    private String codigoPostalVistoria;
    private String paisVistoria;
    private String tipoDocumentoVistoriador;
    private String nomeVistoriador;
    private String documentoVistoriador;
    private String codigoObjeto;
    private String eventoGerador;

    public DadoVistoriaDto(SinistroVistoria sinistroVistoria) {
        this.dataVistoria = sinistroVistoria.getDataVistoria();
        this.localVistoria = sinistroVistoria.getLocalVistoria();
        this.ufVistoria = sinistroVistoria.getUfVistoria();
        this.codigoPostalVistoria = sinistroVistoria.getCodigoPostalVistoria();
        this.paisVistoria = sinistroVistoria.getPaisVistoria();
        this.tipoDocumentoVistoriador = sinistroVistoria.getTipoDocumentoVistoriador();
        this.nomeVistoriador = sinistroVistoria.getNomeVistoriador();
        this.codigoObjeto = sinistroVistoria.getCodigoObjeto();
        this.documentoVistoriador = sinistroVistoria.getDocumentoVistoriador();
        this.eventoGerador = sinistroVistoria.getEventoGerador();
    }
}
