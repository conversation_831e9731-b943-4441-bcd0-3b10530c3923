package br.com.banestes.sgrs.registradorasusep.model.apolice;

import java.util.List;
import javax.persistence.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@ToString
@Table(name = "SRO_CTR_PRESTAMISTA")
public class PrestamistaApolice
{
    @Id
    @Column(name = "IDT_CTR_PRESTAMISTA")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idtCtrPrestamista;

    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;

    @Column(name = "IDT_CTR_RMO_PESSOA", insertable = false, updatable = false)
    private Long idtCtrRmoPessoa;

    @Column(name = "MODELO_CAPITAL")
    private String modeloCapital;

    @Column(name = "PRESTAMISTA_TIPO")
    private String tipo;

    @Column(name = "TIPO_DOCUMENTO")
    private String tipoDocumento;

    @Column(name = "DOCUMENTO")
    private String documento;

    @Column(name = "NOME")
    private String nome;

    @Column(name = "TIPO_OBRIGACAO")
    private String tipoObrigacao;

    @Column(name = "DESCRICAO_OBRIGACAO")
    private String descricaoObrigacao;

    @Transient
    private List<PercentualPrestamistaApolice> percentuais;
}
