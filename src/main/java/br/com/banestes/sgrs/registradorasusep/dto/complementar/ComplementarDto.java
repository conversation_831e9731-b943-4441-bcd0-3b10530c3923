package br.com.banestes.sgrs.registradorasusep.dto.complementar;

import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class ComplementarDto {

    private String identificadorRegistro;
    private String apoliceCodigo;
    private String endossoCodigo;
    private String objetoSeguradoCodigo;
    private String tipo;
    private String descricaoTipo;
    private String descricaoObjeto;
    private String identificacaoExataVeiculo;
    private String modalidadeCasco;
    private BigDecimal percentualTabelaReferencia;
    private String tabelaValorMedio;
    private String codigoModelo;
    private Integer anoModelo;
    private String categoriaTarifaria;
    private String cepRisco;
    private String codigoUtilizacao;
    private String cepLocalidadeDestino;
    private String cepLocalidadePernoite;
    private BigDecimal percentualDescontoBonus;
    private Integer classeBonus;
    private String codigoSeguradora;
    private String certificadoCodigo;
    private List<ComplementarCoberturaDto> coberturas;
    private List<ComplementarPessoaDto> pessoasAssociadas;

}
