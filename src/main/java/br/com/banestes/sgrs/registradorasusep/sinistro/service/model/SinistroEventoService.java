package br.com.banestes.sgrs.registradorasusep.sinistro.service.model;

import org.springframework.stereotype.Service;

import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroEvento;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroEventoRepository;

import java.util.List;

@Service
public class SinistroEventoService {
    private final SinistroEventoRepository sinistroEventoRepository;

    public SinistroEventoService(SinistroEventoRepository sinistroEventoRepository) {
        this.sinistroEventoRepository = sinistroEventoRepository;
    }

    public List<SinistroEvento> getAllByIdtCtlProcAndIdtSntSinistro(Integer idtCtlProc, Long idtSntSinistro) {
        return sinistroEventoRepository.getAllByIdtCtlProcAndIdtSntSinistro(idtCtlProc, idtSntSinistro);
    }

}
