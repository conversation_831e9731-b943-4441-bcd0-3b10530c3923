package br.com.banestes.sgrs.registradorasusep.model.apolice;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;

@Getter
@Setter
@ToString
@Entity
@Table(name = "SRO_CTR_APOLICE")
@AllArgsConstructor
@NoArgsConstructor
public class Apolice {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "IDT_CTR_APOLICE")
    private Long idtCtrApolice;
    @Column(name = "COD_MODALIDADE")
    private Integer codModalidade;
    @Column(name = "IDT_CTL_PROC")
    private Integer idtCtlProc;
    @Column(name = "COD_EMPRESA")
    private Integer codEmpresa;
    @Column(name = "TIPO_DOCUMENTO_EMITIDO")
    private String tipoDocumentoEmitido;
    @Column(name = "COD_SEGURADORA")
    private Integer codSeguradora;
    @Column(name = "IDENTIFICADOR_REGISTRO")
    private String identificadorRegistro;
    @Column(name = "COD_EMISSOR")
    private Integer codEmissor;
    @Column(name = "COD_RAMO")
    private Integer codRamo;
    @Column(name = "DATA_PROTOCOLO")
    private String dataProtocolo;
    @Column(name = "NUM_CONTRATO")
    private BigInteger numContrato;
    @Column(name = "CODIGO_PROPOSTA")
    private String codigoProposta;
    @Column(name = "IDT_CTR_APOLICE_ATU")
    private Long idtCtrApoliceAtu;
    @Column(name = "APOLICE_CODIGO")
    private String apoliceCodigo;
    @Column(name = "CODIGO_SEGURADORA")
    private String codigoSeguradora;
    @Column(name = "TIPO_EMISSAO")
    private String tipoEmissao;
    @Column(name = "NUMERO_SUSEP_APOLICE")
    private String numeroSusepApolice;
    @Column(name = "CERTIFICADO_CODIGO")
    private String certificadoCodigo;
    @Column(name = "RENOVADA")
    private String renovada;
    @Column(name = "CODIGO_SEGURADORA_LIDER")
    private String codigoSeguradoraLider;
    @Column(name = "VALOR_TOTAL")
    private Double valorTotal;
    @Column(name = "APOLICE_RENOVADA_CODIGO")
    private String apoliceRenovadaCodigo;
    @Column(name = "DATA_EMISSAO")
    private String dataEmissao;
    @Column(name = "IOF")
    private Double iof;
    @Column(name = "DATA_INICIO")
    private String dataInicioDocumento;
    @Column(name = "DATA_TERMINO")
    private String dataTerminoDocumento;
    @Column(name = "APOLICE_CODIGO_LIDER")
    private String apoliceCodigoLider;
    @Column(name = "NUM_CTR_SUSEP")
    private BigInteger numCtrSusep;
    @Column(name = "CODIGO_FILIAL")
    private String codigoFilial;
    @Column(name = "COBERTURA_BASICA")
    private String coberturaBasica;
    @Column(name = "LIMITE_MAXIMO_GARANTIA")
    private Double limiteMaximoGarantia;
    @Column(name = "LIMITE_MAXIMO_GARANTIA_REAL")
    private Double limiteMaximoGarantiaReal;
    @Column(name = "DATA_ASSINATURA")
    private String dataAssinatura;
    @Column(name = "RAMO_COMERCIAL")
    private String ramoComercial;
    @Column(name = "VALOR_TOTAL_REAL")
    private Double valorTotalReal;
    @Column(name = "ADICIONAL_FRACIONAMENTO")
    private Double adicionalFracionamento;
    @Column(name = "NUMERO_PARCELAS")
    private Integer numeroParcelas;
    @Column(name = "GRUPO_COMERCIAL")
    private String grupoComercial;
    @Column(name = "MOEDA_APOLICE")
    private String moedaApolice;
}
