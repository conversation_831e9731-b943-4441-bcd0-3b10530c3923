package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.Intermediario;

import java.util.List;

@Repository
public interface IntermediarioRepository extends JpaRepository<Intermediario, Integer> {

    List<Intermediario> findAllByIdtCtlProcAndIdtCtrApolice(Integer idtCtlProc, Long idtCtrApolice);

}
