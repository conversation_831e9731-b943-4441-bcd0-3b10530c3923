package br.com.banestes.sgrs.registradorasusep.apolice.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import br.com.banestes.sgrs.registradorasusep.model.apolice.DependenteApolice;

import java.util.List;

@Repository
public interface DependenteApoliceRepository extends JpaRepository<DependenteApolice, Integer> {

    List<DependenteApolice> findAllByidtCtrRmoPessoaAndIdtCtlProc(Long idtCtrRmoPessoa, Integer idtCtlProc);

}
