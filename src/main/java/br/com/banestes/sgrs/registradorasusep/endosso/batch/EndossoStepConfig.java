package br.com.banestes.sgrs.registradorasusep.endosso.batch;

import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class EndossoStepConfig {

    private final StepBuilderFactory stepBuilderFactory;

    public EndossoStepConfig(StepBuilderFactory stepBuilderFactory) {
        this.stepBuilderFactory = stepBuilderFactory;
    }

    @Bean
    public Step endossoStep(
            ItemReader<Integer> endossoItemReader,
            @Qualifier("endossoProcessor") ItemProcessor<Integer, Integer> endossoItemProcessor,
            ItemWriter<Integer> endossoItemWriter) {

        return stepBuilderFactory
                .get("endossoStep")
                .<Integer, Integer>chunk(1)
                .reader(endossoItemReader)
                .processor(endossoItemProcessor)
                .writer(endossoItemWriter)
                .build();
    }
}
