package br.com.banestes.sgrs;

import lombok.SneakyThrows;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import br.com.banestes.sgrs.registradorasusep.config.JobRetryStarter;
import br.com.banestes.sgrs.registradorasusep.helper.ExitErrorCode;

@EnableBatchProcessing
@SpringBootApplication
public class Application {

    @SneakyThrows
    public static void main(String[] args) {
        final ConfigurableApplicationContext run = SpringApplication.run(Application.class, args);

        //Ajuste para forcar o exit code 1 e evitar do opcon colocar o job como Sucesso
        final ExitStatus exitStatus = run.getBean(JobRetryStarter.class).jobSelectorWithRetry();

        if (exitStatus.getExitCode().equals(ExitStatus.FAILED.getExitCode())) {
            System.exit(SpringApplication.exit(run, new ExitErrorCode()));
        }

        System.exit(SpringApplication.exit(run));
    }

}
