{"properties": [{"name": "rotina", "type": "java.lang.String", "description": "A description for 'rotina'"}, {"name": "ramo", "type": "java.lang.String", "description": "A description for 'ramo'"}, {"name": "movimento", "type": "java.lang.String", "description": "A description for 'movimento'"}, {"name": "h2.datasource.jdbcUrl", "type": "java.lang.String", "description": "A description for 'h2.datasource.jdbcUrl'"}, {"name": "h2.datasource.driverClassName", "type": "java.lang.String", "description": "A description for 'h2.datasource.driverClassName'"}, {"name": "h2.datasource.password", "type": "java.lang.String", "description": "A description for 'h2.datasource.password'"}, {"name": "h2.datasource.username", "type": "java.lang.String", "description": "A description for 'h2.datasource.username'"}, {"name": "sqlserver.datasource.jdbcUrl", "type": "java.lang.String", "description": "A description for 'sqlserver.datasource.jdbcUrl'"}, {"name": "sqlserver.datasource.driverClassName", "type": "java.lang.String", "description": "A description for 'sqlserver.datasource.driverClassName'"}, {"name": "sqlserver.datasource.username", "type": "java.lang.String", "description": "A description for 'sqlserver.datasource.username'"}, {"name": "sqlserver.jpa.show-sql", "type": "java.lang.String", "description": "A description for 'sqlserver.jpa.show-sql'"}, {"name": "sqlserver.datasource.password", "type": "java.lang.String", "description": "A description for 'sqlserver.datasource.password'"}, {"name": "sqlserver.jpa.database-platform", "type": "java.lang.String", "description": "A description for 'sqlserver.jpa.database-platform'"}, {"name": "api.baseurl", "type": "java.lang.String", "description": "A description for 'api.baseurl'"}]}