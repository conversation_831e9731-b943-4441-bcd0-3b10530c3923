numeroErrosPlataforma: '10'

sqlserver:
  jpa:
    show-sql: 'true'
    database-platform: org.hibernate.dialect.SQLServerDialect
  datasource:
    username: Y29uc3VsdGE=
    password: YmFuZXN0ZXM=
    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
    jdbcUrl: *******************************************************************************************

bdread:
  sqlserver:
    datasource:
      username: Y29uc3VsdGE=
      password: YmFuZXN0ZXM=
      driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
      jdbcUrl: *******************************************************************************************
    jpa:
      show-sql: 'true'
      database-platform: org.hibernate.dialect.SQLServerDialect

spring:
  main:
    banner-mode: 'off'
  batch:
    job:
      enabled: 'false'

banestes:
  sso:
    url: https://sso-dev.apps.banestes.b.br/auth
    realm: sfb-dev
    username: SV9FR1NfQVBQ
    password: bGhlZ3MwMDE=
    client-id: c2VndXJvcy1yZWdpc3RyYWRvcmFzdXNlcC1iYXRjaA==
    client-secret: YmJkYjAzYjQtZjhhZS00NTBlLTgzZWMtNmI0NzhjMWNjMjM4

api:
  baseurl: https://seguros-registradorasusep-api-sgrs-dev.cloud.sfb

log:
  logger:
    datasource:
      url: ***************************************************************************************;
      username: consulta
      password: banestes

controlargatilho:
  filepath: D:/SISTEMAS/Jobs_integracao/MAPS/

# Configurações de Retry para Jobs
job:
  retry:
    enabled: true          # Habilita/desabilita o sistema de retry
    maxAttempts: 3         # Número máximo de tentativas
    delaySeconds: 30       # Intervalo entre tentativas em segundos

# Configurações de Retry para Steps
step:
  retry:
    enabled: true          # Habilita/desabilita retry nos steps
    maxAttempts: 3         # Número máximo de tentativas por step
    initialInterval: 2000  # Intervalo inicial em ms
    maxInterval: 10000     # Intervalo máximo em ms
    multiplier: 2.0        # Multiplicador para backoff exponencial

# Configurações de Warmup
warmup:
  enabled: true           # Habilita/desabilita warmup
  maxAttempts: 5          # Número máximo de tentativas de warmup
  delaySeconds: 3         # Intervalo entre tentativas de warmup
  timeoutSeconds: 10      # Timeout para cada tentativa

# Configurações de Logging para Retry
logging:
  level:
    br.com.banestes.sgrs.registradorasusep.config.JobRetryStarter: INFO
    br.com.banestes.sgrs.registradorasusep.helper.EnhancedBatchWarmup: INFO
    br.com.banestes.sgrs.registradorasusep.config.RetryConfiguration: DEBUG