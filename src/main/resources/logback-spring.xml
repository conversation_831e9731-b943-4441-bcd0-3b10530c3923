<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

    <property resource="application.yml"/>
    <springProperty name="DB_URL" source="log.logger.datasource.url"/>
    <springProperty name="DB_USERNAME" source="log.logger.datasource.username"/>
    <springProperty name="DB_PASSWORD" source="log.logger.datasource.password"/>

    <appender name="DATABASE" class="br.com.banestes.sgrs.logger.DBAppender">
        <application>seguros-registradorasusep-batch</application>

        <connectionSource class="ch.qos.logback.core.db.DriverManagerConnectionSource">
            <driverClass>com.microsoft.sqlserver.jdbc.SQLServerDriver</driverClass>
            <url>${DB_URL}</url>
            <user>${DB_USERNAME}</user>
            <password>${DB_PASSWORD}</password>
        </connectionSource>

        <filter class="br.com.banestes.sgrs.logger.LoggerFilter">
            <logger>br.com.banestes.sgrs</logger>
            <level>WARN</level>
        </filter>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="DATABASE" />
    </root>
</configuration>