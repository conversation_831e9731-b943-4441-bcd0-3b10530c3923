numeroErrosPlataforma: '10'

sqlserver:
  jpa:
    show-sql: 'true'
    database-platform: org.hibernate.dialect.SQLServerDialect
  datasource:
    username: Y29uc3VsdGE=
    password: YmFuZXN0ZXM=
    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
    jdbcUrl: *******************************************************************************************

bdread:
  sqlserver:
    datasource:
      username: Y29uc3VsdGE=
      password: YmFuZXN0ZXM=
      driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
      jdbcUrl: *******************************************************************************************
    jpa:
      show-sql: 'true'
      database-platform: org.hibernate.dialect.SQLServerDialect

spring:
  main:
    banner-mode: 'off'
  batch:
    job:
      enabled: 'false'

banestes:
  sso:
    url: https://sso-dev.apps.banestes.b.br/auth
    realm: sfb-dev
    username: SV9FR1NfQVBQ
    password: bGhlZ3MwMDE=
    client-id: c2VndXJvcy1yZWdpc3RyYWRvcmFzdXNlcC1iYXRjaA==
    client-secret: YmJkYjAzYjQtZjhhZS00NTBlLTgzZWMtNmI0NzhjMWNjMjM4

api:
  baseurl: http://localhost:9090

log:
  logger:
    datasource:
      url: ***************************************************************************************;
      username: consulta
      password: banestes

controlargatilho:
  filepath: D:/SISTEMAS/Jobs_integracao/MAPS/