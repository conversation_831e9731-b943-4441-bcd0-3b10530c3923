package br.com.banestes.sgrs.registradorasusep.error;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.error.ResponseUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ResponseUtilsTest {

    @Mock
    private ObjectMapper objectMapper;
    @InjectMocks
    private ResponseUtils responseUtils;

    @Test
    @SneakyThrows
    void gerarResponseDtoErro() {
        final ResponseDto responseDto = ConstantsApolice.madeResponseDtoErro();
        final String json = "{ error: " + ConstantsApolice.madeResponseDtoJson(responseDto);
        final String content = " {\"message\":\"Erro no cadastro!\",\"code\":500,\"status\":\"OK\",\"errors\":[{\"message\":\"Erro teste\",\"field\":\"teste\",\"parameter\":\"teste\"}]}";
        when(objectMapper.readValue(content, ResponseDto.class)).thenReturn(responseDto);

        ResponseDto sut = responseUtils.gerarResponseDto(HttpStatus.BAD_REQUEST, json);
        Assertions.assertThat(sut).isEqualTo(responseDto);
    }

    @Test
    void gerarResponseDtoSucesso() {
        final ResponseDto responseDto = ConstantsApolice.madeResponseDtoCreated();
        ResponseDto sut = responseUtils.gerarResponseDto(HttpStatus.OK, null);
        Assertions.assertThat(sut.getCode()).isEqualTo(responseDto.getCode());
        Assertions.assertThat(sut.getMessage()).isEqualTo(responseDto.getMessage());
    }

    @Test
    void gerarResponseDtoInesperado() {
        final ResponseDto responseDto = ConstantsApolice.madeResponseDtoInesperada("teste");
        ResponseDto sut = responseUtils.gerarResponseDto(HttpStatus.INTERNAL_SERVER_ERROR, "teste");
        Assertions.assertThat(sut.getCode()).isEqualTo(responseDto.getCode());
        Assertions.assertThat(sut.getMessage()).isEqualTo(responseDto.getMessage());
    }

    @Test
    @SneakyThrows
    void gerarResponseDtoExclusaoErro() {
        final ResponseDto responseDto = ConstantsApolice.madeResponseDtoErro();
        final String json = "{ error: " + ConstantsApolice.madeResponseDtoJson(responseDto);
        final String content = " {\"message\":\"Erro no cadastro!\",\"code\":500,\"status\":\"OK\",\"errors\":[{\"message\":\"Erro teste\",\"field\":\"teste\",\"parameter\":\"teste\"}]}";
        when(objectMapper.readValue(content, ResponseDto.class)).thenReturn(responseDto);

        ResponseDto sut = responseUtils.gerarResponseDtoExclusao(HttpStatus.BAD_REQUEST, json, "apolice");
        Assertions.assertThat(sut).isEqualTo(responseDto);
    }

    @Test
    void gerarResponseDtoExclusaoInesperado() {
        final ResponseDto responseDto = ConstantsApolice.madeResponseDtoNoContent("apolice");
        ResponseDto sut = responseUtils.gerarResponseDtoExclusao(HttpStatus.OK, null, "apolice");
        Assertions.assertThat(sut.getCode()).isEqualTo(responseDto.getCode());
        Assertions.assertThat(sut.getMessage()).isEqualTo(responseDto.getMessage());
    }

    @Test
    void gerarResponseDtoExclusaoSucesso() {
        final ResponseDto responseDto = ConstantsApolice.madeResponseDtoInesperada("teste");
        ResponseDto sut = responseUtils.gerarResponseDtoExclusao(HttpStatus.INTERNAL_SERVER_ERROR, "teste", "apolice");
        Assertions.assertThat(sut.getCode()).isEqualTo(responseDto.getCode());
        Assertions.assertThat(sut.getMessage()).isEqualTo(responseDto.getMessage());
    }

}
