package br.com.banestes.sgrs.registradorasusep.constants;

import java.util.ArrayList;
import java.util.Collections;

import br.com.banestes.sgrs.registradorasusep.dto.endosso.ContratoColetivoDto;
import br.com.banestes.sgrs.registradorasusep.dto.endosso.EndossoDto;
import br.com.banestes.sgrs.registradorasusep.model.ControleEndosso;
import br.com.banestes.sgrs.registradorasusep.model.endosso.*;

public class ConstantsEndosso {

    public static Endosso madeEndosso() {
        Endosso endosso = new Endosso();
        endosso.setIdtEdsEndosso(1L);
        endosso.setIdentificadorRegistro("111");
        endosso.setIdtCtlProc(1);
        endosso.setCodEmpresa(1);
        return endosso;
    }

    public static EndossoDto madeEndossoDto() {
        EndossoDto endossoDto = new EndossoDto();
        endossoDto.setIdentificadorRegistro("111");
        endossoDto.setCodigoSeguradora("111");
        endossoDto.setApoliceCodigo("111");

        return endossoDto;
    }

    public static ContratoColetivoEndosso madeContratoColetivoEndosso(){
        ContratoColetivoEndosso contratoColetivoEndosso = new ContratoColetivoEndosso();
        contratoColetivoEndosso.setIdtEdsEndosso(1L);
        contratoColetivoEndosso.setIdtCtlProc(1);
        contratoColetivoEndosso.setIdtEdsColetivo(1L);
        contratoColetivoEndosso.setTipoPlano("");
        return contratoColetivoEndosso;
    }

    public static ParteEndosso madeParteEndosso() {
        ParteEndosso parteEndosso = new ParteEndosso();
        parteEndosso.setIdtEdsEndosso(1L);
        parteEndosso.setIdtCtlProc(1);
        parteEndosso.setIdtEdsParte(1L);
        return parteEndosso;
    }

    public static IntermediarioEndosso madeIntermediarioEndosso() {
        IntermediarioEndosso intermediarioEndosso = new IntermediarioEndosso();
        intermediarioEndosso.setIdtCtlProc(1);
        intermediarioEndosso.setIdtEdsIntermediario(1L);
        intermediarioEndosso.setTipoDocumento("AAA");
        return intermediarioEndosso;
    }

    public static ObjetoSeguradoCompleto madeObjetoSeguradoCompleto() {
        ObjetoSeguradoCompleto objetoSeguradoCompleto = new ObjetoSeguradoCompleto();
        objetoSeguradoCompleto.setObjetoSegurado(madeObjetoSeguradoEndosso());
        objetoSeguradoCompleto.setCoberturas(new ArrayList<>(1));
        objetoSeguradoCompleto.getCoberturas().add(madeCoberturaEndosso());
        objetoSeguradoCompleto.setRamosPessoas(new ArrayList<>(1));
        objetoSeguradoCompleto.getRamosPessoas().add(madeRamoPessoaEndosso());

        return objetoSeguradoCompleto;
    }

    public static ObjetoSeguradoEndosso madeObjetoSeguradoEndosso() {
        ObjetoSeguradoEndosso objetoSeguradoEndosso = new ObjetoSeguradoEndosso();
        objetoSeguradoEndosso.setIdtEdsEndosso(1L);
        objetoSeguradoEndosso.setIdtCtlProc(1);
        objetoSeguradoEndosso.setCodigo("AAA");
        return objetoSeguradoEndosso;
    }

    public static CoberturaEndosso madeCoberturaEndosso() {
        CoberturaEndosso coberturaEndosso = new CoberturaEndosso();
        coberturaEndosso.setIdtEdsCobertura(1L);
        coberturaEndosso.setIdtCtlProc(1);
        coberturaEndosso.setIdtEdsObjeto(1L);
        return coberturaEndosso;
    }

    public static AutomovelEndosso madeAutomovelEndosso() {
        AutomovelEndosso automovelEndosso = new AutomovelEndosso();
        automovelEndosso.setIdtEdsEndosso(1L);
        automovelEndosso.setIdtCtlProc(1);
        automovelEndosso.setIdtEdsAutomovel(1L);
        return automovelEndosso;
    }

    public static RamoPessoasEndosso madeRamoPessoaEndosso() {
        RamoPessoasEndosso ramoPessoasEndosso  = new RamoPessoasEndosso();
        ramoPessoasEndosso.setIdtCtlProc(1);
        ramoPessoasEndosso.setIdtEdsObjeto(1L);
        ramoPessoasEndosso.setIdtEdsRmoPessoa(1L);

        PrestamistaEndosso prestamistaEndosso = new PrestamistaEndosso();
        prestamistaEndosso.setIdtCtlProc(1);
        prestamistaEndosso.setIdtEdsRmoPessoa(1L);
        prestamistaEndosso.setIdtEdsPrestamista(1L);

        PercentualPrestamistaEndosso percentualPrestamistaEndosso = new PercentualPrestamistaEndosso();
        percentualPrestamistaEndosso.setIdtCtlProc(1);
        percentualPrestamistaEndosso.setIdtEdsPrestamista(1L);
        percentualPrestamistaEndosso.setIdtEdsPctPrestamista(1L);
        prestamistaEndosso.setPercentuais(Collections.singletonList(percentualPrestamistaEndosso));

        ramoPessoasEndosso.setPrestamistas(Collections.singletonList(prestamistaEndosso));

        DependenteEndosso dependenteEndosso = new DependenteEndosso();
        dependenteEndosso.setIdtCtlProc(1);
        dependenteEndosso.setIdtEdsRmoPessoa(1L);
        dependenteEndosso.setIdtEdsDependente(1L);
        ramoPessoasEndosso.setDependentes(Collections.singletonList(dependenteEndosso));

        return ramoPessoasEndosso;
    }

    public static ParcelaEndosso madeParcelaEndosso() {
        ParcelaEndosso parcelaEndosso = new ParcelaEndosso();
        parcelaEndosso.setIdtCtlProc(1);
        parcelaEndosso.setIdtEdsEndosso(1L);
        parcelaEndosso.setIdtEdsParcela(1L);
        return parcelaEndosso;
    }

    public static ControleEndosso madeControleEndosso() {
        ControleEndosso controleEndosso = new ControleEndosso();
        controleEndosso.setIdtCtlEndosso(1L);
        controleEndosso.setIdtCtlEndossoAtu(1L);
        controleEndosso.setIdcSitProc("R");
        return controleEndosso;
    }

    public static ObjetoPatrimonialEndosso madeObjetoPatrimonialEndosso() {
        return new ObjetoPatrimonialEndosso(1L, 1, 1L, "Tipo", "1", "1", "1");
    }
}
