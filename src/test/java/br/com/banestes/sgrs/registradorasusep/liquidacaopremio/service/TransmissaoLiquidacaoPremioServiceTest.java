package br.com.banestes.sgrs.registradorasusep.liquidacaopremio.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsPremio;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.dto.premio.LiquidacaoPremioDto;
import br.com.banestes.sgrs.registradorasusep.liquidacaopremio.service.ConstrutorLiquidacaoPremioService;
import br.com.banestes.sgrs.registradorasusep.liquidacaopremio.service.TransmissaoLiquidacaoPremioService;
import br.com.banestes.sgrs.registradorasusep.liquidacaopremio.service.model.LiquidacaoPremioService;
import br.com.banestes.sgrs.registradorasusep.model.premio.Premio;
import br.com.banestes.sgrs.registradorasusep.service.EnvioSusepService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpMethod;
import org.springframework.test.util.ReflectionTestUtils;
import java.util.Collections;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TransmissaoLiquidacaoPremioServiceTest {

    @Mock
    private LiquidacaoPremioService liquidacaoPremioService;
    @Mock
    private ConstrutorLiquidacaoPremioService construtorLiquidacaoPremioService;
    @Mock
    private EnvioSusepService envioSusepService;
    @InjectMocks
    private TransmissaoLiquidacaoPremioService transmissaoLiquidacaoPremioService;

    @BeforeEach
    void before() {
        ReflectionTestUtils.setField(transmissaoLiquidacaoPremioService, "numeroErrosPlataforma", 10);
    }

    @Test
    void transmitirLiquidacaoPremio_inclusao() {

        final Premio premio = ConstantsPremio.madePremio();
        final LiquidacaoPremioDto liquidacaoPremioDto = ConstantsPremio.madeLiquidacaoPremioDto();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        when(liquidacaoPremioService.listarLiquidacoesPremioTransmissao(1)).thenReturn(Collections.singletonList(premio));
        when(liquidacaoPremioService.atualizarStatusTransmissao(premio, responseDto, 'I')).thenReturn(true);
        when(envioSusepService.transmitir(liquidacaoPremioDto, "liquidacao-premio", HttpMethod.POST)).thenReturn(responseDto);
        when(construtorLiquidacaoPremioService.construir(premio)).thenReturn(liquidacaoPremioDto);

        assertDoesNotThrow(() -> transmissaoLiquidacaoPremioService.transmitirLiquidacaoPremio(1, 'I'));
    }

    @Test
    void transmitirLiquidacaoPremio_alteracao() {

        final Premio premio = ConstantsPremio.madePremio();
        final LiquidacaoPremioDto liquidacaoPremioDto = ConstantsPremio.madeLiquidacaoPremioDto();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        when(liquidacaoPremioService.listarLiquidacoesPremioTransmissao(1)).thenReturn(Collections.singletonList(premio));
        when(liquidacaoPremioService.atualizarStatusTransmissao(premio, responseDto, 'A')).thenReturn(true);
        when(envioSusepService.transmitir(liquidacaoPremioDto, "liquidacao-premio/" + premio.getIdentificadorRegistro(), HttpMethod.PUT)).thenReturn(responseDto);
        when(construtorLiquidacaoPremioService.construir(premio)).thenReturn(liquidacaoPremioDto);

        assertDoesNotThrow(() -> transmissaoLiquidacaoPremioService.transmitirLiquidacaoPremio(1, 'A'));
    }

}
