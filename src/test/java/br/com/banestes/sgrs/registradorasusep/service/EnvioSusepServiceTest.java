package br.com.banestes.sgrs.registradorasusep.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.ApoliceDto;
import br.com.banestes.sgrs.registradorasusep.error.ResponseUtils;
import br.com.banestes.sgrs.registradorasusep.service.EnvioSusepService;
import br.com.banestes.sgrs.registradorasusep.service.KeycloakService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class EnvioSusepServiceTest {

    @Mock
    private RestTemplate restTemplate;
    @Mock
    private KeycloakService keycloakService;
    @Mock
    private ResponseUtils responseUtils;
    @InjectMocks
    private EnvioSusepService envioSusepService;

    @BeforeEach
    void before() {
        ReflectionTestUtils.setField(envioSusepService, "modoSimulacao", false);
        ReflectionTestUtils.setField(envioSusepService, "apiBaseUrl", "http://localhost:9090");
    }

    @Test
    void transmitir_operacao_inclusao() {
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();
        final ApoliceDto apoliceDto = ConstantsApolice.madeApoliceDto();
        final String token = "AAA";
        final HttpEntity<?> request = ConstantsApolice.madeHttpEntity(apoliceDto, token);
        final ResponseEntity<String> retorno = new ResponseEntity<>("Teste", HttpStatus.OK);

        when(keycloakService.getAccessToken("")).thenReturn("AAA");
        when(restTemplate.exchange(String.format("%s/%s", "http://localhost:9090", "apolice"), HttpMethod.POST, request, String.class)).thenReturn(retorno);
        when(responseUtils.gerarResponseDto(retorno.getStatusCode(), retorno.getBody())).thenReturn(responseDto);

        ResponseDto sut = envioSusepService.transmitir(apoliceDto, "apolice", HttpMethod.POST);
        Assertions.assertThat(sut).isEqualTo(responseDto);
    }

    @Test
    void transmitir_operacao_alteracao() {
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();
        final ApoliceDto apoliceDto = ConstantsApolice.madeApoliceDto();
        final String token = "AAA";
        final HttpEntity<?> request = ConstantsApolice.madeHttpEntity(apoliceDto, token);
        final ResponseEntity<String> retorno = new ResponseEntity<>("Teste", HttpStatus.OK);

        when(keycloakService.getAccessToken("")).thenReturn("AAA");
        when(restTemplate.exchange(String.format("%s/%s", "http://localhost:9090", "apolice/" + apoliceDto.getIdentificadorRegistro()), HttpMethod.PUT, request, String.class)).thenReturn(retorno);
        when(responseUtils.gerarResponseDto(retorno.getStatusCode(), retorno.getBody())).thenReturn(responseDto);

        ResponseDto sut = envioSusepService.transmitir(apoliceDto, "apolice/" + apoliceDto.getIdentificadorRegistro(), HttpMethod.PUT);
        Assertions.assertThat(sut).isEqualTo(responseDto);
    }

}
