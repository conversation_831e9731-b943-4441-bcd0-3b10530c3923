package br.com.banestes.sgrs.registradorasusep.movimentosinistro.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsMovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistroAdicional;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.repository.MovimentoSinistroAdicionalRepository;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.service.model.MovimentoSinistroAdicionalService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MovimentoSinistroAdicionalServiceTest {

    @Mock
    private MovimentoSinistroAdicionalRepository movimentoSinistroAdicionalRepository;
    @InjectMocks
    private MovimentoSinistroAdicionalService movimentoSinistroAdicionalService;

    @Test
    void getAllByIdtCtlProcAndIdtSntMovSinistro() {
        final MovimentoSinistroAdicional movimentoSinistroAdicional = ConstantsMovimentoSinistro.madeMovimentoSinistroAdicional();
        when(movimentoSinistroAdicionalRepository.getAllByIdtCtlProcAndIdtSntMovSinistro(1, 1L)).thenReturn(Collections.singletonList(movimentoSinistroAdicional));

        List<MovimentoSinistroAdicional> sut = movimentoSinistroAdicionalService.getAllByIdtCtlProcAndIdtSntMovSinistro(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(movimentoSinistroAdicional);
    }
}
