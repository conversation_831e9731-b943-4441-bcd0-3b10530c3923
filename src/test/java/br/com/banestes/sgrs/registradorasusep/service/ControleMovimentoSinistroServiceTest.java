package br.com.banestes.sgrs.registradorasusep.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsExclusao;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsMovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.model.ControleMovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.repository.ControleMovimentoSinistroRepository;
import br.com.banestes.sgrs.registradorasusep.service.ControleMovimentoSinistroService;
import br.com.banestes.sgrs.registradorasusep.service.MovimentoSinistroUpdateService;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ControleMovimentoSinistroServiceTest {

    @Mock
    private ControleMovimentoSinistroRepository controleMovimentoSinistroRepository;
    @Mock
    private MovimentoSinistroUpdateService movimentoSinistroUpdateService;
    @InjectMocks
    private ControleMovimentoSinistroService controleMovimentoSinistroService;

    @Test
    void atualizar() {
        final ControleMovimentoSinistro controleMovimentoSinistro = ConstantsMovimentoSinistro.madeControleMovimentoSinistro();
        when(controleMovimentoSinistroRepository.findById(1L)).thenReturn(Optional.of(controleMovimentoSinistro));

        assertDoesNotThrow(() -> controleMovimentoSinistroService.atualizar(1L, "R", "Erro Teste", 'I'));
    }

    @Test
    void atualizarRegistroExclusao() {
        final ExclusaoLayout exclusaoLayout = ConstantsExclusao.madeExclusaoLayout();
        final ControleMovimentoSinistro controleMovimentoSinistro = ConstantsMovimentoSinistro.madeControleMovimentoSinistro();
        when(controleMovimentoSinistroRepository.findById(exclusaoLayout.getIdtLeiaute())).thenReturn(Optional.of(controleMovimentoSinistro));

        assertDoesNotThrow(() -> controleMovimentoSinistroService.atualizarRegistroExclusao(exclusaoLayout, "R"));
    }

}
