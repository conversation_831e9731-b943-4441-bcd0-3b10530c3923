package br.com.banestes.sgrs.registradorasusep.movimentosinistro.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsMovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.repository.MovimentoSinistroRepository;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.service.model.MovimentoSinistroService;
import br.com.banestes.sgrs.registradorasusep.service.ControleMovimentoSinistroService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MovimentoSinistroServiceTest {

    @Mock
    private MovimentoSinistroRepository movimentoSinistroRepository;
    @Mock
    private ControleMovimentoSinistroService controleMovimentoSinistroService;
    @InjectMocks
    private MovimentoSinistroService movimentoSinistroService;

    @Test
    void listarMovimentosSinistroTransmissao() {
        final MovimentoSinistro movimentoSinistro = ConstantsMovimentoSinistro.madeMovimentoSinistro();
        when(movimentoSinistroRepository.listarMovimentosSinistroTransmissao(1)).thenReturn(Collections.singletonList(movimentoSinistro));

        List<MovimentoSinistro> sut = movimentoSinistroService.listarMovimentosSinistroTransmissao(1);
        Assertions.assertThat(sut.get(0)).isEqualTo(movimentoSinistro);
    }

    @Test
    void atualizarStatusTransmissaoSucesso() {
        final MovimentoSinistro movimentoSinistro = ConstantsMovimentoSinistro.madeMovimentoSinistro();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        boolean sut = movimentoSinistroService.atualizarStatusTransmissao(movimentoSinistro, responseDto, 'I');
        Assertions.assertThat(sut).isEqualTo(true);
    }

    @Test
    void atualizarStatusTransmissaoErro() {
        final MovimentoSinistro movimentoSinistro = ConstantsMovimentoSinistro.madeMovimentoSinistro();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDtoErro();

        boolean sut = movimentoSinistroService.atualizarStatusTransmissao(movimentoSinistro, responseDto, 'I');
        Assertions.assertThat(sut).isEqualTo(false);
    }

}
