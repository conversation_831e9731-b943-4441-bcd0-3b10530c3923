package br.com.banestes.sgrs.registradorasusep.complementar.service.model;

import br.com.banestes.sgrs.registradorasusep.complementar.repository.CoberturaAutoRepository;
import br.com.banestes.sgrs.registradorasusep.complementar.service.model.CoberturaAutoService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsComplementar;
import br.com.banestes.sgrs.registradorasusep.model.complementar.ComplementarCobertura;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CoberturaAutoServiceTest {

    @Mock
    private CoberturaAutoRepository coberturaAutoRepository;
    @InjectMocks
    private CoberturaAutoService coberturaAutoService;

    @Test
    void findAllByIdtCtlProcAndIdtCmpAuto() {
        final ComplementarCobertura complementarCobertura = ConstantsComplementar.madeComplementarCobertura();
        when(coberturaAutoRepository.findAllByIdtCtlProcAndIdtCmpAuto(1, 1L)).thenReturn(Collections.singletonList(complementarCobertura));

        List<ComplementarCobertura> sut = coberturaAutoService.findAllByIdtCtlProcAndIdtCmpAuto(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(complementarCobertura);
    }

}
