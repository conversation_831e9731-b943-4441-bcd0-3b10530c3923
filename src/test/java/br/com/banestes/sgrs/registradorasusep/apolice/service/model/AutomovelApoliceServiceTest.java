package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.AutomovelApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.AutomovelApoliceService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.model.apolice.AutomovelApolice;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AutomovelApoliceServiceTest {

    @Mock
    private AutomovelApoliceRepository automovelApoliceRepository;
    @InjectMocks
    private AutomovelApoliceService automovelApoliceService;

    @Test
    void findAllByIdtCtlProcAndIdtCtrApolice() {
        final AutomovelApolice automovelApolice = ConstantsApolice.madeAutomovelApolice();
        when(automovelApoliceRepository.findAllByIdtCtlProcAndIdtCtrApolice(1, 1L)).thenReturn(Collections.singletonList(automovelApolice));

        List<AutomovelApolice> sut = automovelApoliceService.findAllByIdtCtlProcAndIdtCtrApolice(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(automovelApolice);
    }

}
