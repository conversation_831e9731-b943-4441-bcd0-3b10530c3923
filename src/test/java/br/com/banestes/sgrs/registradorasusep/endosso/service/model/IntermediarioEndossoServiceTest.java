package br.com.banestes.sgrs.registradorasusep.endosso.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsEndosso;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.IntermediarioEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.model.endosso.IntermediarioEndosso;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class IntermediarioEndossoServiceTest {

    @Mock
    private IntermediarioEndossoRepository intermediarioRepository;
    @InjectMocks
    private IntermediarioEndossoService intermediarioEndossoService;

    @Test
    void findAllByIdtCtlProcAndIdtCtrApolice() {
        final IntermediarioEndosso intermediarioEndosso = ConstantsEndosso.madeIntermediarioEndosso();
        when(intermediarioRepository.findAllByIdtCtlProcAndIdtEdsEndosso(1, 1L)).thenReturn(Collections.singletonList(intermediarioEndosso));

        List<IntermediarioEndosso> sut = intermediarioEndossoService.findAllByIdtCtlProcAndIdtEdsEndosso(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(intermediarioEndosso);
    }

}
