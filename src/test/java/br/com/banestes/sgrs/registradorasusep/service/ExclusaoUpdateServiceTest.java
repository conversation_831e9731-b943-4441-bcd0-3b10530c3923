package br.com.banestes.sgrs.registradorasusep.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;

import br.com.banestes.sgrs.registradorasusep.service.ExclusaoUpdateService;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

@ExtendWith(MockitoExtension.class)
public class ExclusaoUpdateServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplateObject;
    @InjectMocks
    private ExclusaoUpdateService exclusaoUpdateService;

    @Test
    void updateExclusaoRegistro() {
        ReflectionTestUtils.setField(exclusaoUpdateService, "modoSimulacao", false);
        assertDoesNotThrow(() -> exclusaoUpdateService.updateExclusaoRegistro(1, "", "R"));
    }

    @Test
    void updateExclusaoCadastroRegistro() {
        ReflectionTestUtils.setField(exclusaoUpdateService, "modoSimulacao", false);
        assertDoesNotThrow(() -> exclusaoUpdateService.updateExclusaoCadastroRegistro(1, "R"));
    }

}
