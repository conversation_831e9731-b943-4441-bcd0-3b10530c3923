package br.com.banestes.sgrs.registradorasusep.complementar.service.model;

import br.com.banestes.sgrs.registradorasusep.complementar.repository.ComplementarPessoaRepository;
import br.com.banestes.sgrs.registradorasusep.complementar.service.model.ComplementarPessoaService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsComplementar;
import br.com.banestes.sgrs.registradorasusep.model.complementar.ComplementarPessoa;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ComplementarPessoaServiceTest {

    @Mock
    private ComplementarPessoaRepository complementarPessoaRepository;
    @InjectMocks
    private ComplementarPessoaService complementarPessoaService;

    @Test
    void findAllByIdtCtlProcAndIdtCmpAuto() {
        final ComplementarPessoa complementarPessoa = ConstantsComplementar.madeComplementarPessoa();
        when(complementarPessoaRepository.findAllByIdtCtlProcAndIdtCmpAuto(1, 1L)).thenReturn(Collections.singletonList(complementarPessoa));

        List<ComplementarPessoa> sut = complementarPessoaService.findAllByIdtCtlProcAndIdtCmpAuto(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(complementarPessoa);
    }

}
