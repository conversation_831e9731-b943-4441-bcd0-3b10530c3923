package br.com.banestes.sgrs.registradorasusep.movimentosinistro.mapper;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsMovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.dto.movimentosinistro.MovimentoSinistroDto;
import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistroAdicional;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.mapper.MovimentoSinistroMapper;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;

@ExtendWith(MockitoExtension.class)
public class MovimentoSinistroMapperTest {

    @Test
    void toDto() {
        MovimentoSinistroMapper movimentoSinistroMapper = new MovimentoSinistroMapper();
        final MovimentoSinistro movimentoSinistro = ConstantsMovimentoSinistro.madeMovimentoSinistro();
        final MovimentoSinistroAdicional movimentoSinistroAdicional = ConstantsMovimentoSinistro.madeMovimentoSinistroAdicional();

        MovimentoSinistroDto sut = movimentoSinistroMapper.toDto(movimentoSinistro, Collections.singletonList(movimentoSinistroAdicional));
        Assertions.assertThat(sut.getIdentificadorMovimento()).isEqualTo(movimentoSinistro.getIdentificadorMovimento());
    }

}
