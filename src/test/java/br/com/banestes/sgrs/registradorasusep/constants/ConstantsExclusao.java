package br.com.banestes.sgrs.registradorasusep.constants;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoCadastro;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;

public class ConstantsExclusao {

    public static ExclusaoCadastro madeExclusaoCadastro() {
        ExclusaoCadastro exclusaoCadastro = new ExclusaoCadastro();
        exclusaoCadastro.setIdtExcCadastro(1);
        exclusaoCadastro.setIdcSitProc("S");
        exclusaoCadastro.setCodEmpresa(1);
        exclusaoCadastro.setCodRamo(1);
        return exclusaoCadastro;
    }

    public static ExclusaoLayout madeExclusaoLayout() {
        ExclusaoLayout exclusaoLayout = new ExclusaoLayout();
        exclusaoLayout.setIdtExcCadastro(1);
        exclusaoLayout.setIdtExcLeiaute(1);
        exclusaoLayout.setIdcSitProc("S");
        exclusaoLayout.setDesLeiaute("APOLICE");
        exclusaoLayout.setIdentificadorRegistro("111");
        exclusaoLayout.setIdcReenviarLeiaute("S");
        return exclusaoLayout;
    }

    public static HttpEntity<Void> madeHttpHeaders(String token) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("Authorization", "Bearer " + token);

        return new HttpEntity<>(httpHeaders);
    }

}
