package br.com.banestes.sgrs.registradorasusep.service;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

import br.com.banestes.sgrs.registradorasusep.service.MessageService;

import java.util.Locale;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MessageServiceTest {

    @Mock
    private MessageSource messageSource;
    @InjectMocks
    private MessageService messageService;

    @Test
    void message() {
        final String code = "message.test";
        final String message = "Mensagem Teste";
        when(messageSource.getMessage(code, null, new Locale("pt", "BR"))).thenReturn(message);

        String sut = messageService.message(code);
        Assertions.assertThat(sut).isEqualTo(message);
    }

    @Test
    void messageWithParams() {
        final String code = "message.test";
        final String message = "Mensagem Teste";
        final String[] params = {"teste"};

        when(messageSource.getMessage(code, params, new Locale("pt", "BR"))).thenReturn(message);
        String sut = messageService.messageWithParams(code, params);
        Assertions.assertThat(sut).isEqualTo(message);
    }

}
