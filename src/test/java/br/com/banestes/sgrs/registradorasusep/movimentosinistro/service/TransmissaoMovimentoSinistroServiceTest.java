package br.com.banestes.sgrs.registradorasusep.movimentosinistro.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsMovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.dto.movimentosinistro.MovimentoSinistroDto;
import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.service.ConstrutorMovimentoSinistroService;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.service.TransmissaoMovimentoSinistroService;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.service.model.MovimentoSinistroService;
import br.com.banestes.sgrs.registradorasusep.service.EnvioSusepService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpMethod;
import org.springframework.test.util.ReflectionTestUtils;
import java.util.Collections;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TransmissaoMovimentoSinistroServiceTest {

    @Mock
    private MovimentoSinistroService movimentoSinistroService;
    @Mock
    private ConstrutorMovimentoSinistroService construtorMovimentoSinistroService;
    @Mock
    private EnvioSusepService envioSusepService;
    @InjectMocks
    private TransmissaoMovimentoSinistroService transmissaoMovimentoSinistroService;

    @BeforeEach
    void before() {
        ReflectionTestUtils.setField(transmissaoMovimentoSinistroService, "numeroErrosPlataforma", 10);
    }

    @Test
    void transmitirMovimentoSinistro_inclusao() {
        final MovimentoSinistro movimentoSinistro = ConstantsMovimentoSinistro.madeMovimentoSinistro();
        final MovimentoSinistroDto movimentoSinistroDto = ConstantsMovimentoSinistro.madeMovimentoSinistroDto();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        when(movimentoSinistroService.listarMovimentosSinistroTransmissao(1)).thenReturn(Collections.singletonList(movimentoSinistro));
        when(movimentoSinistroService.atualizarStatusTransmissao(movimentoSinistro, responseDto, 'I')).thenReturn(true);
        when(envioSusepService.transmitir(movimentoSinistroDto, "movimento-sinistro", HttpMethod.POST)).thenReturn(responseDto);
        when(construtorMovimentoSinistroService.construir(movimentoSinistro)).thenReturn(movimentoSinistroDto);

        assertDoesNotThrow(() -> transmissaoMovimentoSinistroService.transmitirMovimentoSinistro(1, 'I'));
    }

    @Test
    void transmitirMovimentoSinistro_alteracao() {
        final MovimentoSinistro movimentoSinistro = ConstantsMovimentoSinistro.madeMovimentoSinistro();
        final MovimentoSinistroDto movimentoSinistroDto = ConstantsMovimentoSinistro.madeMovimentoSinistroDto();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        when(movimentoSinistroService.listarMovimentosSinistroTransmissao(1)).thenReturn(Collections.singletonList(movimentoSinistro));
        when(movimentoSinistroService.atualizarStatusTransmissao(movimentoSinistro, responseDto, 'A')).thenReturn(true);
        when(envioSusepService.transmitir(movimentoSinistroDto, "movimento-sinistro/" + movimentoSinistro.getIdentificadorRegistro(), HttpMethod.PUT)).thenReturn(responseDto);
        when(construtorMovimentoSinistroService.construir(movimentoSinistro)).thenReturn(movimentoSinistroDto);

        assertDoesNotThrow(() -> transmissaoMovimentoSinistroService.transmitirMovimentoSinistro(1, 'A'));
    }

}
