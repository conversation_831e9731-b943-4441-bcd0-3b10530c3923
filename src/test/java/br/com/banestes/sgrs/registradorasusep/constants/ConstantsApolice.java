package br.com.banestes.sgrs.registradorasusep.constants;

import br.com.banestes.sgrs.registradorasusep.dto.endosso.ContratoColetivoDto;
import br.com.banestes.sgrs.registradorasusep.model.apolice.*;
import com.google.gson.Gson;

import br.com.banestes.sgrs.registradorasusep.apolice.mapper.ApoliceMapper;
import br.com.banestes.sgrs.registradorasusep.dto.ErroDto;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.ApoliceDto;
import br.com.banestes.sgrs.registradorasusep.model.ControleApolice;
import br.com.banestes.sgrs.registradorasusep.model.ControleRotina;

import lombok.SneakyThrows;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ConstantsApolice {

    public static Apolice madeApolice() {
        Apolice apolice = new Apolice();
        apolice.setIdtCtrApolice(1L);
        apolice.setIdentificadorRegistro("1");
        apolice.setIdtCtlProc(1);
        apolice.setCodSeguradora(1);
        apolice.setCodEmissor(1);
        apolice.setCodRamo(1);
        apolice.setCodModalidade(1);
        apolice.setNumContrato(BigInteger.ONE);
        apolice.setNumCtrSusep(BigInteger.ONE);
        apolice.setIdtCtrApoliceAtu(1L);
        apolice.setCodigoSeguradora("1000");
        apolice.setApoliceCodigo("1234");
        apolice.setNumeroSusepApolice("123");
        apolice.setTipoDocumentoEmitido("TIPO");
        apolice.setDataEmissao("01/01/2023");
        apolice.setDataInicioDocumento("01/01/2023");
        apolice.setDataTerminoDocumento("01/01/2023");
        apolice.setCodigoSeguradoraLider("AAA");
        return apolice;
    }

    public static Parte madeParte() {
        Parte parte = new Parte();
        parte.setIdtCtrParte(1L);
        parte.setIdtCtlProc(1);
        parte.setIdtCtrApolice(1L);
        parte.setTipoParte("AAA");
        parte.setDocumento("AAA");
        parte.setTipoDocumento("AAA");
        parte.setNome("AAA");
        parte.setEmail("<EMAIL>");
        parte.setEndereco("AAAA");
        return parte;
    }

    public static Intermediario madeIntermediario() {
        Intermediario intermediario = new Intermediario();
        intermediario.setIdtCtrIntermediario(1L);
        intermediario.setIdtCtlProc(1);
        intermediario.setIdtCtrApolice(1L);
        intermediario.setTipo("AAA");
        intermediario.setDocumento("AAA");
        intermediario.setTipoDocumento("AAA");
        intermediario.setCodigo("AAA");
        intermediario.setNome("Teste");
        intermediario.setEmail("<EMAIL>");
        return intermediario;
    }

    public static ContratoColetivoApolice madeContratoColetivoApolice(){
        ContratoColetivoApolice contratoColetivoApolice = new ContratoColetivoApolice();
        contratoColetivoApolice.setTipoPlano("");
        contratoColetivoApolice.setIdtCtrColetivo(1L);
        contratoColetivoApolice.setIdtCtrApolice(1L);
        contratoColetivoApolice.setIdtCtlProc(1);
        return contratoColetivoApolice;
    }

    public static ObjetoSegurado madeObjetoSegurado() {
        ObjetoSegurado objetoSegurado = new ObjetoSegurado();
        objetoSegurado.setIdtCtrObjeto(1L);
        objetoSegurado.setIdtCtrApolice(1L);
        objetoSegurado.setIdtCtlProc(1);

        return objetoSegurado;
    }

    public static Cobertura madeCobertura() {
        Cobertura cobertura = new Cobertura();
        cobertura.setIdtCtrCobertura(1L);
        cobertura.setIdtCtlProc(1);
        cobertura.setIdtCtrObjeto(1L);
        return cobertura;
    }

    public static CoberturaCompleta maCoberturaCompleta() {
        CoberturaCompleta coberturaCompleta = new CoberturaCompleta();
        coberturaCompleta.setCobertura(madeCobertura());
        return coberturaCompleta;
    }

    public static ObjetoSeguradoCompleto madeObjetoSeguradoCompleto() {
        final ObjetoSegurado objetoSegurado = madeObjetoSegurado();

        ObjetoSeguradoCompleto objetoSeguradoCompleto = new ObjetoSeguradoCompleto();
        objetoSeguradoCompleto.setObjetoSegurado(objetoSegurado);
        objetoSeguradoCompleto.setCoberturas(Collections.singletonList(maCoberturaCompleta()));
        objetoSeguradoCompleto.setRamosPessoas(Collections.singletonList(madeRamoPessoaApolice()));
        return objetoSeguradoCompleto;
    }

    public static AutomovelApolice madeAutomovelApolice() {
        AutomovelApolice automovelApolice = new AutomovelApolice();
        automovelApolice.setIdtCtrApolice(1L);
        automovelApolice.setIdtCtlProc(1);
        automovelApolice.setIdtCtrApolice(1L);
        return automovelApolice;
    }

    public static ParcelaApolice madeParcelaApolice() {
        ParcelaApolice parcelaApolice = new ParcelaApolice();
        parcelaApolice.setIdtCtlProc(1);
        parcelaApolice.setIdtCtrParcela(1L);
        parcelaApolice.setIdtCtrApolice(1L);

        return parcelaApolice;
    }

    public static RamoPessoasApolice madeRamoPessoaApolice() {
        RamoPessoasApolice ramoPessoasApolice = new RamoPessoasApolice();
        ramoPessoasApolice.setIdtCtlProc(1);
        ramoPessoasApolice.setIdtCtrRmoPessoa(1L);
        ramoPessoasApolice.setIdtCtrObjeto(1L);

        PrestamistaApolice prestamistaApolice = new PrestamistaApolice();
        prestamistaApolice.setIdtCtlProc(1);
        prestamistaApolice.setIdtCtrRmoPessoa(1L);
        prestamistaApolice.setIdtCtrPrestamista(1L);

        PercentualPrestamistaApolice percentualPrestamistaApolice = new PercentualPrestamistaApolice();
        percentualPrestamistaApolice.setIdtCtlProc(1);
        percentualPrestamistaApolice.setIdtCtrPrestamista(1L);
        percentualPrestamistaApolice.setIdtCtrPctPrestamista(1L);
        prestamistaApolice.setPercentuais(Collections.singletonList(percentualPrestamistaApolice));

        ramoPessoasApolice.setPrestamistas(Collections.singletonList(prestamistaApolice));

        DependenteApolice dependenteApolice = new DependenteApolice();
        dependenteApolice.setIdtCtlProc(1);
        dependenteApolice.setIdtCtrRmoPessoa(1L);
        dependenteApolice.setIdtCtrDependente(1L);
        ramoPessoasApolice.setDependentes(Collections.singletonList(dependenteApolice));

        return ramoPessoasApolice;
    }

    public static ApoliceDto madeApoliceDto() {
        ApoliceMapper apoliceMapper = new ApoliceMapper();
        return apoliceMapper.toDto(madeApolice(),
                Collections.singletonList(madeParte()),
                Collections.singletonList(madeIntermediario()),
                Collections.singletonList(madeObjetoSeguradoCompleto()),
                Collections.singletonList(madeAutomovelApolice()),
                Collections.singletonList(madeParcelaApolice()),
                Collections.singletonList(madeContratoColetivoApolice()));
    }

    public static ResponseDto madeResponseDto() {
        ResponseDto responseDto = new ResponseDto();
        responseDto.setMessage("Cadastrado com sucesso!");
        responseDto.setCode(200);
        responseDto.setStatus("OK");

        return responseDto;
    }

    public static ResponseDto madeResponseDtoCreated() {
        return new ResponseDto(HttpStatus.OK.value(), "OK", "O registro foi registrado com sucesso.");
    }

    public static ResponseDto madeResponseDtoNoContent(String endPoint) {
        return new ResponseDto(HttpStatus.NO_CONTENT.value(), "sucess", String.format("O registro de %s foi excluido com sucesso", endPoint));
    }

    public static ResponseDto madeResponseDtoInesperada(String message) {
        ResponseDto response = new ResponseDto();
        response.setCode(600);
        response.setMessage("Resposta da MAPS não é um json de erro esperado: " + message);
        return response;
    }

    public static ResponseDto madeResponseDtoErro() {
        ResponseDto responseDto = new ResponseDto();
        responseDto.setMessage("Erro no cadastro!");
        responseDto.setCode(500);
        responseDto.setStatus("OK");

        List<ErroDto> erroDtoList = new ArrayList<>();
        ErroDto erroDto = new ErroDto();
        erroDto.setField("teste");
        erroDto.setMessage("Erro teste");
        erroDto.setParameter("teste");
        erroDtoList.add(erroDto);
        responseDto.setErrors(erroDtoList);

        return responseDto;
    }

    public static Adicional madeAdicional() {
        Adicional adicional = new Adicional();
        adicional.setIdtCtrCoberturaAdicional(1);
        adicional.setIdtCtlProc(1);
        adicional.setIdtCtrApolice(1);
        adicional.setIdtCtrObjeto(1L);

        return adicional;
    }

    public static Beneficiario madeBeneficiario() {
        Beneficiario beneficiario = new Beneficiario();
        beneficiario.setIdtCtrCoberturaBeneficiario(1);
        beneficiario.setIdtCtlProc(1);
        beneficiario.setIdtCtrObjeto(1L);
        return beneficiario;
    }

    public static Franquia madeFranquia() {
        Franquia franquia = new Franquia();
        franquia.setIdtCtrCoberturaFranquia(1);
        franquia.setIdtCtlProc(1);
        franquia.setIdtCtrApolice(1);
        franquia.setIdtCtrObjetoSeg(1);
        return franquia;
    }

    public static ControleApolice madeControleApolice() {
        ControleApolice apolice = new ControleApolice();
        apolice.setIdtCtlApolice(1L);
        apolice.setIdcSitProc("R");
        apolice.setIdtCtlProc(1);
        apolice.setIdtCtlApoliceAtu(1);
        return apolice;
    }

    public static ControleRotina madeControleRotina() {
        ControleRotina controleRotina = new ControleRotina();
        controleRotina.setIdtCtlProc(1);
        controleRotina.setIdcSitProc('R');
        controleRotina.setIdtCtlRtn(1);
        return controleRotina;
    }

    public static HttpEntity<?> madeHttpEntity(Object data, String token) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("Authorization", "Bearer " + token);

        return new HttpEntity<>(data, httpHeaders);
    }

    @SneakyThrows
    public static String madeResponseDtoJson(ResponseDto responseDto) {
        return new Gson().toJson(responseDto);
    }

    public static ObjetoPatrimonialApolice madeObjetoPatrimonialApolice() {
        return new ObjetoPatrimonialApolice(1L, 1, 1L, "Tipo", "1", "1", "1");
    }


}
