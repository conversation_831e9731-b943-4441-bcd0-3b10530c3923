package br.com.banestes.sgrs.registradorasusep.sinistro.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsSinistro;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroVistoria;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroVistoriaRepository;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroVistoriaService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SinistroVistoriaServiceTest {

    @Mock
    private SinistroVistoriaRepository sinistroVistoriaRepository;
    @InjectMocks
    private SinistroVistoriaService sinistroVistoriaService;

    @Test
    void getAllByIdtCtlProcAndIdtSntSinistro() {
        final SinistroVistoria sinistroVistoria = ConstantsSinistro.madeSinistroVistoria();
        when(sinistroVistoriaRepository.getAllByIdtCtlProcAndIdtSntSinistro(1, 1L)).thenReturn(Collections.singletonList(sinistroVistoria));

        List<SinistroVistoria> sut = sinistroVistoriaService.getAllByIdtCtlProcAndIdtSntSinistro(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(sinistroVistoria);
    }

}
