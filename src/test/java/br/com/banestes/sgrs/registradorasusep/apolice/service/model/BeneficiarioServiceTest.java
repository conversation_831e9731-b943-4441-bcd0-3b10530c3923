package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.BeneficiarioRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.BeneficiarioService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Beneficiario;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Cobertura;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class BeneficiarioServiceTest {

    @Mock
    private BeneficiarioRepository beneficiarioRepository;
    @InjectMocks
    private BeneficiarioService beneficiarioService;

    @Test
    void findAllByIdtCtlProcAndIdtCtrObjeto() {
        final Cobertura cobertura = ConstantsApolice.madeCobertura();
        final Beneficiario beneficiario = ConstantsApolice.madeBeneficiario();

        when(beneficiarioRepository.findAllByIdtCtlProcAndIdtCtrObjeto(cobertura.getIdtCtlProc(),
                cobertura.getIdtCtrObjeto())).thenReturn(Collections.singletonList(beneficiario));

        List<Beneficiario> sut = beneficiarioService.findAllByIdtCtlProcAndIdtCtrObjeto(cobertura);
        Assertions.assertThat(sut.get(0)).isEqualTo(beneficiario);
    }

}
