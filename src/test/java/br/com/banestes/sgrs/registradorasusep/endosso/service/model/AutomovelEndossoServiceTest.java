package br.com.banestes.sgrs.registradorasusep.endosso.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsEndosso;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.AutomovelEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.AutomovelEndossoService;
import br.com.banestes.sgrs.registradorasusep.model.endosso.AutomovelEndosso;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AutomovelEndossoServiceTest {

    @Mock
    private AutomovelEndossoRepository automovelEndossoRepository;
    @InjectMocks
    private AutomovelEndossoService automovelEndossoService;

    @Test
    void findAllByIdtCtlProcAndIdtEdsEndosso() {
        final AutomovelEndosso automovelEndosso = ConstantsEndosso.madeAutomovelEndosso();
        when(automovelEndossoRepository.findAllByIdtCtlProcAndIdtEdsEndosso(1, 1L)).thenReturn(Collections.singletonList(automovelEndosso));

        List<AutomovelEndosso> sut = automovelEndossoService.findAllByIdtCtlProcAndIdtEdsEndosso(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(automovelEndosso);
    }

}
