package br.com.banestes.sgrs.registradorasusep.sinistro.mapper;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsSinistro;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.DocumentosAfetadosDto;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.SinistroDto;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.Sinistro;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroAuto;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroBeneficiario;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroCobertura;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroDocumento;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroEvento;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroPessoa;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroVistoria;
import br.com.banestes.sgrs.registradorasusep.sinistro.mapper.SinistroMapper;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroCoberturaService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SinistroMapperTest {

    @Mock
    private SinistroCoberturaService sinistroCoberturaService;
    @InjectMocks
    private SinistroMapper sinistroMapper;

    @Test
    void toDto() {

        final Sinistro sinistro = ConstantsSinistro.madeSinistro();
        final SinistroPessoa sinistroPessoa = ConstantsSinistro.madeSinistroPessoa();
        final SinistroEvento sinistroEvento = ConstantsSinistro.madeSinistroEvento();
        final SinistroVistoria sinistroVistoria = ConstantsSinistro.madeSinistroVistoria();
        DocumentosAfetadosDto sinistroDocumento = ConstantsSinistro.madeDocumentosAfetadosDto();

        final SinistroAuto sinistroAuto = ConstantsSinistro.madeSinistroAuto();
        final SinistroBeneficiario sinistroBeneficiario = ConstantsSinistro.madeSinistroBeneficiario();

        SinistroDto sut = sinistroMapper.toDto(sinistro,
                Collections.singletonList(sinistroPessoa),
                Collections.singletonList(sinistroEvento),
                Collections.singletonList(sinistroVistoria),
                Collections.singletonList(sinistroDocumento),
                Collections.singletonList(sinistroAuto),
                Collections.singletonList(sinistroBeneficiario));

        Assertions.assertThat(sut.getIdentificadorRegistro()).isEqualTo(sinistro.getIdentificadorRegistro());
        Assertions.assertThat(sut.getPessoasAcidentadas().size()).isEqualTo(1);
        Assertions.assertThat(sut.getDadosEvento().size()).isEqualTo(1);
        Assertions.assertThat(sut.getDadosVistorias().size()).isEqualTo(1);
        Assertions.assertThat(sut.getDocumentosAfetados().size()).isEqualTo(1);
        Assertions.assertThat(sut.getDadosAuto().size()).isEqualTo(1);
        Assertions.assertThat(sut.getBeneficiariosFinais().size()).isEqualTo(1);

    }

}
