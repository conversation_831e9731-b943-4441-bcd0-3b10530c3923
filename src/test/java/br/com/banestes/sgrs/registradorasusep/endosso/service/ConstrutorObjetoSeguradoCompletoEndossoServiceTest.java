package br.com.banestes.sgrs.registradorasusep.endosso.service;

import br.com.banestes.sgrs.registradorasusep.endosso.repository.DependenteEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.PercentualPrestamistaEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.PrestamistaEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsEndosso;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.ObjetoPatrimonialEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.service.ConstrutorObjetoSeguradoCompletoEndossoService;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.CoberturaEndossoService;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.ObjetoSeguradoEndossoService;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.RamoPessoasEndossoService;
import br.com.banestes.sgrs.registradorasusep.model.endosso.*;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ConstrutorObjetoSeguradoCompletoEndossoServiceTest {

    @Mock
    private CoberturaEndossoService coberturaService;
    @Mock
    private ObjetoSeguradoEndossoService objetoSeguradoService;
    @Mock
    private ObjetoPatrimonialEndossoRepository objetoPatrimonialEndossoRepository;
    @Mock
    private PrestamistaEndossoRepository objetoPrestamistaEndossoRepository;
    @Mock
    private PercentualPrestamistaEndossoRepository objetoPercentualPrestamistaEndossoRepository;
    @Mock
    private DependenteEndossoRepository objetoDependenteEndossoRepository;
    @Mock
    private RamoPessoasEndossoService objetoRamoPessoasEndossoService;
    @InjectMocks
    private ConstrutorObjetoSeguradoCompletoEndossoService construtorObjetoSeguradoCompletoEndossoService;

    @Test
    void obterObjetosSeguradosCompletosEndosso() {
        final Endosso endosso = ConstantsEndosso.madeEndosso();
        final ObjetoSeguradoEndosso objetoSegurado = ConstantsEndosso.madeObjetoSeguradoEndosso();
        final CoberturaEndosso coberturaEndosso = ConstantsEndosso.madeCoberturaEndosso();
        final ObjetoPatrimonialEndosso objetoPatrimonialEndosso = ConstantsEndosso.madeObjetoPatrimonialEndosso();
        final RamoPessoasEndosso objetoRamoPessoasEndosso = ConstantsEndosso.madeRamoPessoaEndosso();

        when(objetoSeguradoService.findAllByIdtCtlProcAndIdtEdsEndosso(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()))
                .thenReturn(Collections.singletonList(objetoSegurado));

        when(coberturaService.findAllByIdtCtlProcAndIdtEdsEndossoAndIdtCtrObjeto(objetoSegurado.getIdtCtlProc(), objetoSegurado.getIdtEdsObjeto()))
                .thenReturn(Collections.singletonList(coberturaEndosso));

        when(objetoPatrimonialEndossoRepository.findAllByIdtEdsObjetoAndIdtCtlProc(objetoSegurado.getIdtEdsObjeto(), objetoSegurado.getIdtCtlProc()))
                .thenReturn(Collections.singletonList(objetoPatrimonialEndosso));

        when(objetoRamoPessoasEndossoService.findAllByIdtEdsObjetoAndIdtCtlProc(objetoSegurado.getIdtEdsObjeto(), objetoSegurado.getIdtCtlProc()))
                .thenReturn(Collections.singletonList(objetoRamoPessoasEndosso));

        when(objetoPrestamistaEndossoRepository.findAllByidtEdsRmoPessoaAndIdtCtlProc(1L, 1))
                .thenReturn(Collections.singletonList(objetoRamoPessoasEndosso.getPrestamistas().get(0)));
        when(objetoPercentualPrestamistaEndossoRepository.findAllByidtEdsPrestamistaAndIdtCtlProc(1L, 1))
                .thenReturn(Collections.singletonList(objetoRamoPessoasEndosso.getPrestamistas().get(0).getPercentuais().get(0)));
        when(objetoDependenteEndossoRepository.findAllByidtEdsRmoPessoaAndIdtCtlProc(1L, 1))
                .thenReturn(Collections.singletonList(objetoRamoPessoasEndosso.getDependentes().get(0)));

        List<ObjetoSeguradoCompleto> sut = construtorObjetoSeguradoCompletoEndossoService.obterObjetosSeguradosCompletosEndosso(endosso);

        Assertions.assertThat(sut.get(0).getObjetoSegurado()).isEqualTo(objetoSegurado);
        Assertions.assertThat(sut.get(0).getCoberturas().get(0)).isEqualTo(coberturaEndosso);

    }

}
