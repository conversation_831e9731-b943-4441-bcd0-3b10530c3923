package br.com.banestes.sgrs.registradorasusep.apolice.service;

import br.com.banestes.sgrs.registradorasusep.apolice.service.ConstrutorCoberturaCompletaService;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.CoberturaService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Cobertura;
import br.com.banestes.sgrs.registradorasusep.model.apolice.CoberturaCompleta;
import br.com.banestes.sgrs.registradorasusep.model.apolice.ObjetoSegurado;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ConstrutorCoberturaCompletaServiceTest {

    @Mock
    private CoberturaService coberturaService;
    @InjectMocks
    private ConstrutorCoberturaCompletaService construtorCoberturaCompletaService;

    @Test
    void obterCoberturasCompletasObjetoSegurado() {
        final Cobertura cobertura = ConstantsApolice.madeCobertura();
        final ObjetoSegurado objetoSegurado = ConstantsApolice.madeObjetoSegurado();
        when(coberturaService.findAllByIdtCtlProcAndIdtCtrApoliceAndIdtCtrObjeto(objetoSegurado.getIdtCtlProc(), objetoSegurado.getIdtCtrObjeto())).thenReturn(Collections.singletonList(cobertura));

        List<CoberturaCompleta> sut = construtorCoberturaCompletaService.obterCoberturasCompletasObjetoSegurado(objetoSegurado);
        Assertions.assertThat(sut.get(0).getCobertura()).isEqualTo(cobertura);
    }

}
