package br.com.banestes.sgrs.registradorasusep.apolice.service;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.ObjetoPatrimonialApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.repository.PrestamistaApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.repository.PercentualPrestamistaApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.repository.DependenteApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.ObjetoSeguradoService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Apolice;
import br.com.banestes.sgrs.registradorasusep.model.apolice.CoberturaCompleta;
import br.com.banestes.sgrs.registradorasusep.model.apolice.ObjetoPatrimonialApolice;
import br.com.banestes.sgrs.registradorasusep.model.apolice.ObjetoSegurado;
import br.com.banestes.sgrs.registradorasusep.model.apolice.RamoPessoasApolice;
import br.com.banestes.sgrs.registradorasusep.model.apolice.ObjetoSeguradoCompleto;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ConstrutorObjetoSeguradoCompletoServiceTest {

    @Mock
    private ConstrutorCoberturaCompletaService construtorCoberturaCompletaService;
    @Mock
    private ObjetoSeguradoService objetoSeguradoService;
    @Mock
    private ObjetoPatrimonialApoliceRepository objetoPatrimonialApoliceRepository;
    @Mock
    private PrestamistaApoliceRepository objetoPrestamistaApoliceRepository;
    @Mock
    private PercentualPrestamistaApoliceRepository objetoPercentualPrestamistaApoliceRepository;
    @Mock
    private DependenteApoliceRepository objetoDependenteApoliceRepository;
    @Mock
    private RamoPessoasApoliceService objetoRamoPessoasApoliceService;
    @InjectMocks
    private ConstrutorObjetoSeguradoCompletoService construtorObjetoSeguradoCompletoService;

    @Test
    void obterObjetosSeguradosCompletosApolice() {
        final CoberturaCompleta coberturaCompleta = ConstantsApolice.maCoberturaCompleta();
        final ObjetoSegurado objetoSegurado = ConstantsApolice.madeObjetoSegurado();
        final Apolice apolice = ConstantsApolice.madeApolice();
        final ObjetoPatrimonialApolice objetoPatrimonialApolice = ConstantsApolice.madeObjetoPatrimonialApolice();
        final RamoPessoasApolice ramoPessoasApolice = ConstantsApolice.madeRamoPessoaApolice();

        when(objetoSeguradoService.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice())).thenReturn(Collections.singletonList(objetoSegurado));
        when(construtorCoberturaCompletaService.obterCoberturasCompletasObjetoSegurado(objetoSegurado)).thenReturn(Collections.singletonList(coberturaCompleta));
        when(objetoRamoPessoasApoliceService.findAllByIdtCtrObjetoAndIdtCtlProc(1L, 1)).thenReturn(Collections.singletonList(ramoPessoasApolice));
        when(objetoPatrimonialApoliceRepository.findAllByIdtCtrObjetoAndIdtCtlProc(1L, 1)).thenReturn(Collections.singletonList(objetoPatrimonialApolice));

        when(objetoPrestamistaApoliceRepository.findAllByidtCtrRmoPessoaAndIdtCtlProc(1L, 1)).thenReturn(Collections.singletonList(ramoPessoasApolice.getPrestamistas().get(0)));
        when(objetoPercentualPrestamistaApoliceRepository.findAllByidtCtrPrestamistaAndIdtCtlProc(1L, 1)).thenReturn(Collections.singletonList(ramoPessoasApolice.getPrestamistas().get(0).getPercentuais().get(0)));
        when(objetoDependenteApoliceRepository.findAllByidtCtrRmoPessoaAndIdtCtlProc(1L, 1)).thenReturn(Collections.singletonList(ramoPessoasApolice.getDependentes().get(0)));

        List<ObjetoSeguradoCompleto> sut = construtorObjetoSeguradoCompletoService.obterObjetosSeguradosCompletosApolice(apolice);
        Assertions.assertThat(sut.get(0).getObjetoSegurado()).isEqualTo(objetoSegurado);

    }

}
