package br.com.banestes.sgrs.registradorasusep.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsExclusao;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsPremio;
import br.com.banestes.sgrs.registradorasusep.model.ControlePremio;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.repository.ControlePremioRepository;
import br.com.banestes.sgrs.registradorasusep.service.ControlePremioService;
import br.com.banestes.sgrs.registradorasusep.service.PremioUpdateService;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ControlePremioServiceTest {

    @Mock
    private ControlePremioRepository controlePremioRepository;
    @Mock
    private PremioUpdateService premioUpdateService;
    @InjectMocks
    private ControlePremioService controlePremioService;

    @Test
    void atualizar() {
        final ControlePremio controlePremio = ConstantsPremio.madeControlePremio();
        when(controlePremioRepository.findById(1L)).thenReturn(Optional.of(controlePremio));

        assertDoesNotThrow(() -> controlePremioService.atualizar(1L, "R", "Erro Teste", 'I'));
    }

    @Test
    void atualizarRegistroExclusao() {
        final ExclusaoLayout exclusaoLayout = ConstantsExclusao.madeExclusaoLayout();
        final ControlePremio controlePremio = ConstantsPremio.madeControlePremio();
        when(controlePremioRepository.findById(exclusaoLayout.getIdtLeiaute())).thenReturn(Optional.of(controlePremio));

        assertDoesNotThrow(() -> controlePremioService.atualizarRegistroExclusao(exclusaoLayout, "R"));
    }

}
