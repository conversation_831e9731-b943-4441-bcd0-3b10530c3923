package br.com.banestes.sgrs.registradorasusep.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsEndosso;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsExclusao;
import br.com.banestes.sgrs.registradorasusep.model.ControleEndosso;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.repository.ControleEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.service.ControleEndossoService;
import br.com.banestes.sgrs.registradorasusep.service.EndossoUpdateService;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ControleEndossoServiceTest {

    @Mock
    private ControleEndossoRepository controleEndossoRepository;
    @Mock
    private EndossoUpdateService endossoUpdateService;
    @InjectMocks
    private ControleEndossoService controleEndossoService;

    @Test
    void atualizar() {
        final ControleEndosso controleEndosso = ConstantsEndosso.madeControleEndosso();
        when(controleEndossoRepository.findById(1L)).thenReturn(Optional.of(controleEndosso));

        assertDoesNotThrow(() -> controleEndossoService.atualizar(1L, "R", "Erro Teste", 'I'));
    }

    @Test
    void atualizarRegistroExclusao() {
        final ExclusaoLayout exclusaoLayout = ConstantsExclusao.madeExclusaoLayout();
        final ControleEndosso controleEndosso = ConstantsEndosso.madeControleEndosso();
        when(controleEndossoRepository.findById(exclusaoLayout.getIdtLeiaute())).thenReturn(Optional.of(controleEndosso));

        assertDoesNotThrow(() -> controleEndossoService.atualizarRegistroExclusao(exclusaoLayout, "R"));
    }

}
