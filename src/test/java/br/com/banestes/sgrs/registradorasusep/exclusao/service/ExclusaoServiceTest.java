package br.com.banestes.sgrs.registradorasusep.exclusao.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsExclusao;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.exclusao.service.EnvioExclusaoService;
import br.com.banestes.sgrs.registradorasusep.exclusao.service.ExclusaoService;
import br.com.banestes.sgrs.registradorasusep.exclusao.service.model.ExclusaoLayoutService;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoCadastro;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.service.ExclusaoUpdateService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ExclusaoServiceTest {

    @Mock
    private ExclusaoLayoutService exclusaoLayoutService;
    @Mock
    private EnvioExclusaoService envioExclusaoService;
    @Mock
    private ExclusaoUpdateService exclusaoUpdateService;
    @InjectMocks
    private ExclusaoService exclusaoService;

    @Test
    void excluirRegistros() {
        final ExclusaoCadastro exclusaoCadastro = ConstantsExclusao.madeExclusaoCadastro();
        final ExclusaoLayout exclusaoLayout = ConstantsExclusao.madeExclusaoLayout();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        when(exclusaoLayoutService.listarRegistrosExcluir(1)).thenReturn(Collections.singletonList(exclusaoLayout));
        when(envioExclusaoService.transmitir(any(), anyString())).thenReturn(responseDto);
        when(exclusaoLayoutService.atualizarStatusTransmissao(any(), any())).thenReturn(true);

        boolean sut = exclusaoService.excluirRegistros(exclusaoCadastro);

        Assertions.assertThat(sut).isEqualTo(true);
    }

}
