package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.IntermediarioRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.IntermediarioService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Intermediario;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class IntermediarioServiceTest {

    @Mock
    private IntermediarioRepository intermediarioRepository;
    @InjectMocks
    private IntermediarioService intermediarioService;

    @Test
    void findAllByIdtCtlProcAndIdtCtrApolice() {
        final Intermediario intermediario = ConstantsApolice.madeIntermediario();
        when(intermediarioRepository.findAllByIdtCtlProcAndIdtCtrApolice(1, 1L)).thenReturn(Collections.singletonList(intermediario));

        List<Intermediario> sut = intermediarioService.findAllByIdtCtlProcAndIdtCtrApolice(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(intermediario);
    }

}
