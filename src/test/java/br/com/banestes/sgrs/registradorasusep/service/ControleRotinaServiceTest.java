package br.com.banestes.sgrs.registradorasusep.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.model.ControleRotina;
import br.com.banestes.sgrs.registradorasusep.repository.ControleRotinaRepository;
import br.com.banestes.sgrs.registradorasusep.service.ControleRotinaService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ControleRotinaServiceTest {

    @Mock
    private ControleRotinaRepository controleRotinaRepository;
    @InjectMocks
    private ControleRotinaService controleRotinaService;

    @Test
    void findByIdtCtlRtn() {
        final ControleRotina controleRotina = ConstantsApolice.madeControleRotina();
        when(controleRotinaRepository.findByIdtCtlRtn(1)).thenReturn(controleRotina);

        final ControleRotina sut = controleRotinaService.findByIdtCtlRtn(1);
        Assertions.assertThat(sut).isEqualTo(controleRotina);
    }

}
