package br.com.banestes.sgrs.registradorasusep.endosso.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsEndosso;
import br.com.banestes.sgrs.registradorasusep.dto.endosso.EndossoDto;
import br.com.banestes.sgrs.registradorasusep.endosso.mapper.EndossoMapper;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.ContratoColetivoEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.ParcelaEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.AutomovelEndossoService;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.IntermediarioEndossoService;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.ParteEndossoService;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.RamoPessoasEndossoService;
import br.com.banestes.sgrs.registradorasusep.model.endosso.*;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import java.util.Collections;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ConstrutorEndossoServiceTest {

    @Mock
    private ConstrutorObjetoSeguradoCompletoEndossoService construtorObjetoSeguradoCompletoService;
    @Mock
    private EndossoMapper endossoMapper;
    @Mock
    private IntermediarioEndossoService intermediarioService;
    @Mock
    private ParteEndossoService parteService;
    @Mock
    private AutomovelEndossoService automovelEndossoService;
    @Mock
    private ParcelaEndossoRepository parcelaEndossoRepository;
    @Mock
    private ContratoColetivoEndossoRepository contratoColetivoEndossoRepository;
    @Mock
    private RamoPessoasEndossoService ramoPessoasEndossoService;
    @InjectMocks
    private ConstrutorEndossoService construtorEndossoService;

    @BeforeEach
    void before() {
        ReflectionTestUtils.setField(construtorEndossoService, "grupoRamo", "AUTO");
    }

    @Test
    void construirAuto() {

        ReflectionTestUtils.setField(construtorEndossoService, "grupoRamo", "AUTO");

        final Endosso endosso = ConstantsEndosso.madeEndosso();
        final AutomovelEndosso automovelEndosso = ConstantsEndosso.madeAutomovelEndosso();
        final ParteEndosso parteEndosso = ConstantsEndosso.madeParteEndosso();
        final IntermediarioEndosso intermediarioEndosso = ConstantsEndosso.madeIntermediarioEndosso();
        final ObjetoSeguradoCompleto objetoSeguradoCompleto = ConstantsEndosso.madeObjetoSeguradoCompleto();
        final ParcelaEndosso parcelaEndosso = ConstantsEndosso.madeParcelaEndosso();
        final EndossoDto endossoDto = ConstantsEndosso.madeEndossoDto();

        when(automovelEndossoService.findAllByIdtCtlProcAndIdtEdsEndosso(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()))
                .thenReturn(Collections.singletonList(automovelEndosso));

        when(parteService.findAllByIdtCtlProcAndIdtCtrApolice(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()))
                .thenReturn(Collections.singletonList(parteEndosso));

        when(intermediarioService.findAllByIdtCtlProcAndIdtEdsEndosso(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()))
                .thenReturn(Collections.singletonList(intermediarioEndosso));

        when(construtorObjetoSeguradoCompletoService.obterObjetosSeguradosCompletosEndosso(endosso))
                .thenReturn(Collections.singletonList(objetoSeguradoCompleto));

        when(parcelaEndossoRepository.findAllByIdtCtlProcAndIdtEdsEndosso(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()))
                .thenReturn(Collections.singletonList(parcelaEndosso));

        when(endossoMapper.toDto(any(), any(), any(), any(), any(), any(), any())).thenReturn(endossoDto);

        EndossoDto sut = construtorEndossoService.construir(endosso);

        Assertions.assertThat(sut.getIdentificadorRegistro()).isEqualTo(endosso.getIdentificadorRegistro());

    }

    @Test
    void construirRe() {

        ReflectionTestUtils.setField(construtorEndossoService, "grupoRamo", "RE");

        final Endosso endosso = ConstantsEndosso.madeEndosso();
        final ParteEndosso parteEndosso = ConstantsEndosso.madeParteEndosso();
        final IntermediarioEndosso intermediarioEndosso = ConstantsEndosso.madeIntermediarioEndosso();
        final ObjetoSeguradoCompleto objetoSeguradoCompleto = ConstantsEndosso.madeObjetoSeguradoCompleto();
        final ParcelaEndosso parcelaEndosso = ConstantsEndosso.madeParcelaEndosso();
        final EndossoDto endossoDto = ConstantsEndosso.madeEndossoDto();

        when(parteService.findAllByIdtCtlProcAndIdtCtrApolice(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()))
                .thenReturn(Collections.singletonList(parteEndosso));

        when(intermediarioService.findAllByIdtCtlProcAndIdtEdsEndosso(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()))
                .thenReturn(Collections.singletonList(intermediarioEndosso));

        when(construtorObjetoSeguradoCompletoService.obterObjetosSeguradosCompletosEndosso(endosso))
                .thenReturn(Collections.singletonList(objetoSeguradoCompleto));

        when(parcelaEndossoRepository.findAllByIdtCtlProcAndIdtEdsEndosso(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()))
                .thenReturn(Collections.singletonList(parcelaEndosso));

        when(parcelaEndossoRepository.findAllByIdtCtlProcAndIdtEdsEndosso(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()))
                .thenReturn(Collections.singletonList(parcelaEndosso));

        when(endossoMapper.toDto(any(), any(), any(), any(), any(), any(), any())).thenReturn(endossoDto);

        EndossoDto sut = construtorEndossoService.construir(endosso);

        Assertions.assertThat(sut.getIdentificadorRegistro()).isEqualTo(endosso.getIdentificadorRegistro());

    }

    @Test
    void construirVida() {

        ReflectionTestUtils.setField(construtorEndossoService, "grupoRamo", "VIDA");

        final Endosso endosso = ConstantsEndosso.madeEndosso();
        final AutomovelEndosso automovelEndosso = ConstantsEndosso.madeAutomovelEndosso();
        final ParteEndosso parteEndosso = ConstantsEndosso.madeParteEndosso();
        final IntermediarioEndosso intermediarioEndosso = ConstantsEndosso.madeIntermediarioEndosso();
        final ObjetoSeguradoCompleto objetoSeguradoCompleto = ConstantsEndosso.madeObjetoSeguradoCompleto();
        final ParcelaEndosso parcelaEndosso = ConstantsEndosso.madeParcelaEndosso();
        final RamoPessoasEndosso ramoPessoasEndosso = ConstantsEndosso.madeRamoPessoaEndosso();
        final EndossoDto endossoDto = ConstantsEndosso.madeEndossoDto();
        final ContratoColetivoEndosso contratoColetivoEndosso = ConstantsEndosso.madeContratoColetivoEndosso();

        when(parteService.findAllByIdtCtlProcAndIdtCtrApolice(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()))
                .thenReturn(Collections.singletonList(parteEndosso));

        when(intermediarioService.findAllByIdtCtlProcAndIdtEdsEndosso(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()))
                .thenReturn(Collections.singletonList(intermediarioEndosso));

        when(construtorObjetoSeguradoCompletoService.obterObjetosSeguradosCompletosEndosso(endosso))
                .thenReturn(Collections.singletonList(objetoSeguradoCompleto));

        when(parcelaEndossoRepository.findAllByIdtCtlProcAndIdtEdsEndosso(endosso.getIdtCtlProc(), endosso.getIdtEdsEndosso()))
                .thenReturn(Collections.singletonList(parcelaEndosso));

        when(contratoColetivoEndossoRepository.findAllByidtEdsEndossoAndIdtCtlProc(endosso.getIdtEdsEndosso(), endosso.getIdtCtlProc()))
                .thenReturn(Collections.singletonList(contratoColetivoEndosso));

        when(endossoMapper.toDto(any(), any(), any(), any(), any(), any(), any())).thenReturn(endossoDto);

        EndossoDto sut = construtorEndossoService.construir(endosso);

        Assertions.assertThat(sut.getIdentificadorRegistro()).isEqualTo(endosso.getIdentificadorRegistro());

    }
}
