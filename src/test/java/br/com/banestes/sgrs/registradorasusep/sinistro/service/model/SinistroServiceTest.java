package br.com.banestes.sgrs.registradorasusep.sinistro.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsSinistro;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.Sinistro;
import br.com.banestes.sgrs.registradorasusep.service.ControleSinistroService;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroRepository;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SinistroServiceTest {

    @Mock
    private SinistroRepository sinistroRepository;
    @Mock
    private ControleSinistroService controleSinistroService;
    @InjectMocks
    private SinistroService sinistroService;

    @Test
    void listarSinistrosTransmissao() {
        final Sinistro sinistro = ConstantsSinistro.madeSinistro();
        when(sinistroRepository.listarSinistrosTransmissao(1)).thenReturn(Collections.singletonList(sinistro));

        List<Sinistro> sut = sinistroService.listarSinistrosTransmissao(1);
        Assertions.assertThat(sut.get(0)).isEqualTo(sinistro);
    }

    @Test
    void atualizarStatusTransmissaoSucesso() {
        final Sinistro sinistro = ConstantsSinistro.madeSinistro();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        boolean sut = sinistroService.atualizarStatusTransmissao(sinistro, responseDto, 'I');
        Assertions.assertThat(sut).isEqualTo(true);

    }

    @Test
    void atualizarStatusTransmissaoErro() {
        final Sinistro sinistro = ConstantsSinistro.madeSinistro();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDtoErro();

        boolean sut = sinistroService.atualizarStatusTransmissao(sinistro, responseDto, 'A');
        Assertions.assertThat(sut).isEqualTo(false);

    }


}
