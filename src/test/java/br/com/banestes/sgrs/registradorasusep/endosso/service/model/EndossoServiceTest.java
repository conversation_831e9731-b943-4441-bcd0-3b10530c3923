package br.com.banestes.sgrs.registradorasusep.endosso.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsEndosso;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.EndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.EndossoService;
import br.com.banestes.sgrs.registradorasusep.model.endosso.Endosso;
import br.com.banestes.sgrs.registradorasusep.service.ControleEndossoService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class EndossoServiceTest {

    @Mock
    private EndossoRepository endossoRepository;
    @Mock
    private ControleEndossoService controleEndossoService;
    @InjectMocks
    private EndossoService endossoService;

    @Test
    void listarEndossosTransmissao() {
        final Endosso endosso = ConstantsEndosso.madeEndosso();
        when(endossoRepository.listarEndossosTransmissao(1)).thenReturn(Collections.singletonList(endosso));

        List<Endosso> sut = endossoService.listarEndossosTransmissao(1);
        Assertions.assertThat(sut.get(0)).isEqualTo(endosso);
    }

    @Test
    void atualizarStatusTransmissaoSucesso() {
        final Endosso endosso = ConstantsEndosso.madeEndosso();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        boolean sut = endossoService.atualizarStatusTransmissao(endosso, responseDto, 'I');
        Assertions.assertThat(sut).isEqualTo(true);
    }

    @Test
    void atualizarStatusTransmissaoErro() {
        final Endosso endosso = ConstantsEndosso.madeEndosso();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDtoErro();

        boolean sut = endossoService.atualizarStatusTransmissao(endosso, responseDto, 'A');
        Assertions.assertThat(sut).isEqualTo(false);
    }

}
