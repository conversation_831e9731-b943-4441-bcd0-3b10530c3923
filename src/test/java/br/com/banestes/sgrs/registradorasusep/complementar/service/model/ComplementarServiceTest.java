package br.com.banestes.sgrs.registradorasusep.complementar.service.model;

import br.com.banestes.sgrs.registradorasusep.complementar.repository.ComplementarRepository;
import br.com.banestes.sgrs.registradorasusep.complementar.service.model.ComplementarService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsComplementar;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.model.complementar.Complementar;
import br.com.banestes.sgrs.registradorasusep.service.ControleComplementarAutoService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ComplementarServiceTest {

    @Mock
    private ComplementarRepository complementarRepository;
    @Mock
    private ControleComplementarAutoService controleComplementarAutoService;
    @InjectMocks
    private ComplementarService complementarService;

    @BeforeEach
    void before() {
        ReflectionTestUtils.setField(complementarService, "complemento", "CTR");
    }

    @Test
    void listarComplementarTransmissao() {
        final Complementar complementar = ConstantsComplementar.madeComplementar();
        when(complementarRepository.listarComplementarTransmissao(1, "CTR")).thenReturn(Collections.singletonList(complementar));

        List<Complementar> sut = complementarService.listarComplementarTransmissao(1);
        Assertions.assertThat(sut.get(0)).isEqualTo(complementar);
    }

    @Test
    void atualizarStatusTransmissaoSucesso() {
        final Complementar complementar = ConstantsComplementar.madeComplementar();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        boolean sut = complementarService.atualizarStatusTransmissao(complementar, responseDto, 'I');
        Assertions.assertThat(sut).isEqualTo(true);

    }

    @Test
    void atualizarStatusTransmissaoErro() {
        final Complementar complementar = ConstantsComplementar.madeComplementar();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDtoErro();

        boolean sut = complementarService.atualizarStatusTransmissao(complementar, responseDto, 'I');
        Assertions.assertThat(sut).isEqualTo(false);

    }

}
