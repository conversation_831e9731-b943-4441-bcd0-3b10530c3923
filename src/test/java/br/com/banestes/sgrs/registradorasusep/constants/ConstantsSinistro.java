package br.com.banestes.sgrs.registradorasusep.constants;

import org.apache.commons.collections4.CollectionUtils;

import br.com.banestes.sgrs.registradorasusep.dto.sinistro.CoberturaAfetadaDto;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.DocumentosAfetadosDto;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.SinistroDto;
import br.com.banestes.sgrs.registradorasusep.model.ControleSinistro;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.Sinistro;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroAuto;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroBeneficiario;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroCobertura;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroDocumento;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroEvento;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroPessoa;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroVistoria;

import java.util.Collections;

public class ConstantsSinistro {

    public static Sinistro madeSinistro() {
        Sinistro sinistro = new Sinistro();
        sinistro.setIdtSntSinistro(1L);
        sinistro.setIdtPcsSinistro(1);
        sinistro.setIdtCtlProc(1);
        sinistro.setIdentificadorRegistro("111");
        sinistro.setCodigoSinistro("111");
        return sinistro;
    }

    public static SinistroDto madeSinistroDto() {
        SinistroDto sinistroDto = new SinistroDto();
        sinistroDto.setIdentificadorRegistro("111");
        sinistroDto.setCodigoSinistro("111");
        sinistroDto.setCodigoSeguradora("123");
        sinistroDto.setDataEntrega("01/01/2023");
        sinistroDto.setStatus("A");
        return sinistroDto;
    }

    public static SinistroCobertura madeSinistroCobertura() {
        SinistroCobertura sinistroCobertura = new SinistroCobertura();
        sinistroCobertura.setIdtSntCobertura(1L);
        sinistroCobertura.setIdtSntCobertura(1L);
        sinistroCobertura.setIdtCtlProc(1);
        sinistroCobertura.setIdtSntDocumento(1L);
        return sinistroCobertura;
    }

    public static SinistroPessoa madeSinistroPessoa() {
        SinistroPessoa sinistroPessoa = new SinistroPessoa();
        sinistroPessoa.setIdtSntPessoa(1L);
        sinistroPessoa.setIdtCtlProc(1);
        sinistroPessoa.setIdtSntSinistro(1L);
        sinistroPessoa.setNome("Teste Teste");
        return sinistroPessoa;
    }

    public static SinistroEvento madeSinistroEvento() {
        SinistroEvento sinistroEvento = new SinistroEvento();
        sinistroEvento.setIdtCtlProc(1);
        sinistroEvento.setIdtSntSinistro(1L);
        sinistroEvento.setIdtSntEvento(1L);
        return sinistroEvento;
    }

    public static SinistroVistoria madeSinistroVistoria() {
        SinistroVistoria sinistroVistoria = new SinistroVistoria();
        sinistroVistoria.setIdtSntVistoria(1L);
        sinistroVistoria.setIdtCtlProc(1);
        sinistroVistoria.setIdtSntVistoria(1L);
        sinistroVistoria.setDataVistoria("01/01/2023");
        return sinistroVistoria;
    }

    public static SinistroDocumento madeSinistroDocumento() {
        SinistroDocumento sinistroDocumento = new SinistroDocumento();
        sinistroDocumento.setIdtSntDocumento(1L);
        sinistroDocumento.setIdtCtlProc(1);
        sinistroDocumento.setIdtSntSinistro(1L);
        return sinistroDocumento;
    }

    public static SinistroAuto madeSinistroAuto() {
        SinistroAuto sinistroAuto = new SinistroAuto();
        sinistroAuto.setIdtCtlProc(1);
        sinistroAuto.setIdtSntSinistro(1L);
        sinistroAuto.setIdtSntAuto(1L);
        return sinistroAuto;
    }

    public static SinistroBeneficiario madeSinistroBeneficiario() {
        SinistroBeneficiario sinistroBeneficiario = new SinistroBeneficiario();
        sinistroBeneficiario.setIdtCtlProc(1);
        sinistroBeneficiario.setIdtSntSinistro(1L);
        sinistroBeneficiario.setIdtSntBeneficiario(1L);
        sinistroBeneficiario.setTipoDocumento("A");
        return sinistroBeneficiario;
    }

    public static ControleSinistro madeControleSinistro() {
        ControleSinistro controleSinistro = new ControleSinistro();
        controleSinistro.setIdtPcsSinistro(1);
        controleSinistro.setIdtCtlSinistro(1L);
        controleSinistro.setIdcSitProc("R");
        controleSinistro.setIdtCtlProc(1);
        controleSinistro.setIdtCtlSinistroAtu(1);
        return controleSinistro;
    }

    public static DocumentosAfetadosDto madeDocumentosAfetadosDto() {
        DocumentosAfetadosDto dto = new DocumentosAfetadosDto();
        dto.setApoliceCodigo("111");
        dto.setCertificadoCodigo("23123123");
        dto.setNumeroEndosso("121345");
        dto.setCoberturasAfetadas(Collections.singletonList(madeCoberturaAfetadaDto()));
        return dto;
    }

    public static CoberturaAfetadaDto madeCoberturaAfetadaDto() {
        CoberturaAfetadaDto coberturaAfetadaDto = new CoberturaAfetadaDto();
        coberturaAfetadaDto.setCodigoObjeto("111");
        coberturaAfetadaDto.setGrupo("AAA");
        coberturaAfetadaDto.setRamo("Ramo");

        return coberturaAfetadaDto;
    }

}
