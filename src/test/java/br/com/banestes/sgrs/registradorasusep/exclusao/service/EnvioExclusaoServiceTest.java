package br.com.banestes.sgrs.registradorasusep.exclusao.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsExclusao;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.error.ResponseUtils;
import br.com.banestes.sgrs.registradorasusep.exclusao.service.EnvioExclusaoService;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.service.KeycloakService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class EnvioExclusaoServiceTest {

    @Mock
    private RestTemplate restTemplate;
    @Mock
    private KeycloakService keycloakService;
    @Mock
    private ResponseUtils responseUtils;
    @InjectMocks
    private EnvioExclusaoService exclusaoService;

    @BeforeEach
    void before() {
        ReflectionTestUtils.setField(exclusaoService, "apiBaseUrl", "teste.com");
    }

    @Test
    void transmitir() {
        final ExclusaoLayout exclusaoLayout = ConstantsExclusao.madeExclusaoLayout();
        final ResponseEntity<String> retorno = new ResponseEntity<>(HttpStatus.OK);
        final String token = "AAA";
        final String endpoint = "exclusao";
        final HttpEntity<Void> httpHeader = ConstantsExclusao.madeHttpHeaders(token);
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        when(restTemplate.exchange("teste.com/exclusao/111", HttpMethod.DELETE, httpHeader, String.class)).thenReturn(retorno);
        when(keycloakService.getAccessToken("")).thenReturn(token);
        when(responseUtils.gerarResponseDtoExclusao(any(), any(), any())).thenReturn(responseDto);
        ResponseDto sut = exclusaoService.transmitir(exclusaoLayout, endpoint);

        Assertions.assertThat(sut.getCode()).isEqualTo(200);
    }

}
