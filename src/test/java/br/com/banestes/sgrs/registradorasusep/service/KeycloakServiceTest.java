package br.com.banestes.sgrs.registradorasusep.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import br.com.banestes.sgrs.registradorasusep.config.SsoConfig;
import br.com.banestes.sgrs.registradorasusep.service.KeycloakService;
import br.com.banestes.sgrs.registradorasusep.service.MessageService;

import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
public class KeycloakServiceTest {

    @Mock
    private SsoConfig config;
    @Mock
    private MessageService messageService;
    @InjectMocks
    private KeycloakService keycloakService;

    @Test
    void getAccessToken() {
        assertThrows(Exception.class, () -> keycloakService.getAccessToken("teste"));
    }

}
