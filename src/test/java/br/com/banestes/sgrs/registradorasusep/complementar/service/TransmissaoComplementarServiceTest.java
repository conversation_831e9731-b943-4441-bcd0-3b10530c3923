package br.com.banestes.sgrs.registradorasusep.complementar.service;

import br.com.banestes.sgrs.registradorasusep.complementar.service.ConstrutorComplementarService;
import br.com.banestes.sgrs.registradorasusep.complementar.service.TransmissaoComplementarService;
import br.com.banestes.sgrs.registradorasusep.complementar.service.model.ComplementarService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsComplementar;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.dto.complementar.ComplementarDto;
import br.com.banestes.sgrs.registradorasusep.model.complementar.Complementar;
import br.com.banestes.sgrs.registradorasusep.service.EnvioSusepService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpMethod;
import org.springframework.test.util.ReflectionTestUtils;
import java.util.Collections;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TransmissaoComplementarServiceTest {

    @Mock
    private ComplementarService complementarService;
    @Mock
    private ConstrutorComplementarService construtorComplementarService;
    @Mock
    private EnvioSusepService envioSusepService;
    @InjectMocks
    private TransmissaoComplementarService transmissaoComplementarService;

    @BeforeEach
    void before() {
        ReflectionTestUtils.setField(transmissaoComplementarService, "numeroErrosPlataforma", 10);
        ReflectionTestUtils.setField(transmissaoComplementarService, "complemento", "CTR");
    }

    @Test
    void transmitirComplementares_inclusao() {
        final Complementar complementar = ConstantsComplementar.madeComplementar();
        final ComplementarDto complementarDto = ConstantsComplementar.madeComplementarDto();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        when(complementarService.listarComplementarTransmissao(1)).thenReturn(Collections.singletonList(complementar));
        when(complementarService.atualizarStatusTransmissao(complementar, responseDto, 'I')).thenReturn(true);
        when(envioSusepService.transmitir(complementarDto, "complementar-auto", HttpMethod.POST)).thenReturn(responseDto);
        when(construtorComplementarService.construir(complementar)).thenReturn(complementarDto);

        assertDoesNotThrow(() -> transmissaoComplementarService.transmitirComplementares(1, 'I'));
    }

    @Test
    void transmitirComplementares_alteracao() {
        final Complementar complementar = ConstantsComplementar.madeComplementar();
        final ComplementarDto complementarDto = ConstantsComplementar.madeComplementarDto();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        when(complementarService.listarComplementarTransmissao(1)).thenReturn(Collections.singletonList(complementar));
        when(complementarService.atualizarStatusTransmissao(complementar, responseDto, 'A')).thenReturn(true);
        when(envioSusepService.transmitir(complementarDto, "complementar-auto/" + complementar.getIdentificadorRegistro(), HttpMethod.PUT)).thenReturn(responseDto);
        when(construtorComplementarService.construir(complementar)).thenReturn(complementarDto);

        assertDoesNotThrow(() -> transmissaoComplementarService.transmitirComplementares(1, 'A'));
    }

}
