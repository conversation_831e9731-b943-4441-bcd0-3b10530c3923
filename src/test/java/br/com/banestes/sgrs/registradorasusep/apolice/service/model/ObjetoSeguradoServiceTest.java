package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.ObjetoSeguradoRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.ObjetoSeguradoService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.model.apolice.ObjetoSegurado;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ObjetoSeguradoServiceTest {

    @Mock
    private ObjetoSeguradoRepository objetoSeguradoRepository;
    @InjectMocks
    private ObjetoSeguradoService objetoSeguradoService;

    @Test
    void findAllByIdtCtlProcAndIdtCtrApolice() {
        final ObjetoSegurado objetoSegurado = ConstantsApolice.madeObjetoSegurado();
        when(objetoSeguradoRepository.findAllByIdtCtlProcAndIdtCtrApolice(1, 1L)).thenReturn(Collections.singletonList(objetoSegurado));

        List<ObjetoSegurado> sut = objetoSeguradoService.findAllByIdtCtlProcAndIdtCtrApolice(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(objetoSegurado);
    }

}
