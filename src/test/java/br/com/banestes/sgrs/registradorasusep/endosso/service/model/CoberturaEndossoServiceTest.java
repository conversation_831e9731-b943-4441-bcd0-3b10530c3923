package br.com.banestes.sgrs.registradorasusep.endosso.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsEndosso;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.CoberturaEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.CoberturaEndossoService;
import br.com.banestes.sgrs.registradorasusep.model.endosso.CoberturaEndosso;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CoberturaEndossoServiceTest {

    @Mock
    private CoberturaEndossoRepository coberturaRepository;
    @InjectMocks
    private CoberturaEndossoService coberturaEndossoService;

    @Test
    void findAllByIdtCtlProcAndIdtEdsEndossoAndIdtCtrObjeto() {
        final CoberturaEndosso coberturaEndosso = ConstantsEndosso.madeCoberturaEndosso();
        when(coberturaRepository.findAllByIdtCtlProcAndIdtEdsObjeto(1, 1L)).thenReturn(Collections.singletonList(coberturaEndosso));

        List<CoberturaEndosso> sut = coberturaEndossoService.findAllByIdtCtlProcAndIdtEdsEndossoAndIdtCtrObjeto(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(coberturaEndosso);
    }

}
