package br.com.banestes.sgrs.registradorasusep.complementar.service;

import br.com.banestes.sgrs.registradorasusep.complementar.mapper.ComplementarMapper;
import br.com.banestes.sgrs.registradorasusep.complementar.service.ConstrutorComplementarService;
import br.com.banestes.sgrs.registradorasusep.complementar.service.model.CoberturaAutoService;
import br.com.banestes.sgrs.registradorasusep.complementar.service.model.ComplementarPessoaService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsComplementar;
import br.com.banestes.sgrs.registradorasusep.dto.complementar.ComplementarDto;
import br.com.banestes.sgrs.registradorasusep.model.complementar.Complementar;
import br.com.banestes.sgrs.registradorasusep.model.complementar.ComplementarCobertura;
import br.com.banestes.sgrs.registradorasusep.model.complementar.ComplementarPessoa;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ConstrutorComplementarServiceTest {

    @Mock
    private ComplementarMapper complementarMapper;
    @Mock
    private CoberturaAutoService coberturaAutoService;
    @Mock
    private ComplementarPessoaService complementarPessoaService;
    @InjectMocks
    private ConstrutorComplementarService construtorComplementarService;

    @Test
    void construir() {
        final Complementar complementar = ConstantsComplementar.madeComplementar();
        final ComplementarCobertura complementarCobertura = ConstantsComplementar.madeComplementarCobertura();
        final ComplementarPessoa complementarPessoa = ConstantsComplementar.madeComplementarPessoa();
        final ComplementarDto complementarDto = ConstantsComplementar.madeComplementarDto();

        when(coberturaAutoService.findAllByIdtCtlProcAndIdtCmpAuto(complementar.getIdtCtlProc(),
                complementar.getIdtCmpAuto())).thenReturn(Collections.singletonList(complementarCobertura));

        when(complementarPessoaService.findAllByIdtCtlProcAndIdtCmpAuto(complementar.getIdtCtlProc(),
                complementar.getIdtCmpAuto())).thenReturn(Collections.singletonList(complementarPessoa));

        when(complementarMapper.toDto(any(), any(), any())).thenReturn(complementarDto);

        ComplementarDto sut = construtorComplementarService.construir(complementar);

        Assertions.assertThat(sut.getIdentificadorRegistro()).isEqualTo(complementar.getIdentificadorRegistro());
    }

}
