package br.com.banestes.sgrs.registradorasusep.apolice.service;

import br.com.banestes.sgrs.registradorasusep.apolice.service.ConstrutorApoliceService;
import br.com.banestes.sgrs.registradorasusep.apolice.service.TransmissaoApoliceService;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.ApoliceService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.ApoliceDto;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Apolice;
import br.com.banestes.sgrs.registradorasusep.service.EnvioSusepService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpMethod;
import org.springframework.test.util.ReflectionTestUtils;
import java.util.Collections;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TransmissaoApoliceServiceTest {

    @Mock
    private ApoliceService apoliceService;
    @Mock
    private ConstrutorApoliceService construtorApoliceService;
    @Mock
    private EnvioSusepService envioSusepService;
    @InjectMocks
    private TransmissaoApoliceService transmissaoApoliceService;

    @BeforeEach
    void before() {
        ReflectionTestUtils.setField(transmissaoApoliceService, "numeroErrosPlataforma", 10);
    }

    @Test
    void transmitirApolices_inclusao() {
        final Apolice apolice = ConstantsApolice.madeApolice();
        final ApoliceDto apoliceDto = ConstantsApolice.madeApoliceDto();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        when(apoliceService.listarApolicesTransmissao(1)).thenReturn(Collections.singletonList(apolice));
        when(apoliceService.atualizarStatusTransmissao(apolice, responseDto,'I')).thenReturn(true);
        when(envioSusepService.transmitir(apoliceDto, "apolice", HttpMethod.POST)).thenReturn(responseDto);
        when(construtorApoliceService.construir(apolice)).thenReturn(apoliceDto);

        assertDoesNotThrow(() -> transmissaoApoliceService.transmitirApolices(1, 'I'));
    }

    @Test
    void transmitirApolices_alteracao() {
        final Apolice apolice = ConstantsApolice.madeApolice();
        final ApoliceDto apoliceDto = ConstantsApolice.madeApoliceDto();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        when(apoliceService.listarApolicesTransmissao(1)).thenReturn(Collections.singletonList(apolice));
        when(apoliceService.atualizarStatusTransmissao(apolice, responseDto, 'A')).thenReturn(true);
        when(envioSusepService.transmitir(apoliceDto, "apolice/" + apolice.getIdentificadorRegistro(), HttpMethod.PUT)).thenReturn(responseDto);
        when(construtorApoliceService.construir(apolice)).thenReturn(apoliceDto);

        assertDoesNotThrow(() -> transmissaoApoliceService.transmitirApolices(1, 'A'));
    }

}
