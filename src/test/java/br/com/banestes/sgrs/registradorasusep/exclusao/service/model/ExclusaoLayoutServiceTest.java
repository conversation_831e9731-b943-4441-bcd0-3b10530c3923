package br.com.banestes.sgrs.registradorasusep.exclusao.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsExclusao;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.exclusao.repository.ExclusaoLayoutRepository;
import br.com.banestes.sgrs.registradorasusep.exclusao.service.model.ExclusaoLayoutService;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.service.ControleApoliceService;
import br.com.banestes.sgrs.registradorasusep.service.ControleComplementarAutoService;
import br.com.banestes.sgrs.registradorasusep.service.ControleEndossoService;
import br.com.banestes.sgrs.registradorasusep.service.ControleMovimentoSinistroService;
import br.com.banestes.sgrs.registradorasusep.service.ControlePremioService;
import br.com.banestes.sgrs.registradorasusep.service.ControleSinistroService;
import br.com.banestes.sgrs.registradorasusep.service.ExclusaoUpdateService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ExclusaoLayoutServiceTest {

    @Mock
    private ExclusaoLayoutRepository exclusaoLayoutRepository;
    @Mock
    private ControleApoliceService controleApoliceService;
    @Mock
    private ControleComplementarAutoService controleComplementarAutoService;
    @Mock
    private ControleEndossoService controleEndossoService;
    @Mock
    private ControlePremioService controlePremioService;
    @Mock
    private ControleSinistroService controleSinistroService;
    @Mock
    private ControleMovimentoSinistroService controleMovimentoSinistroService;
    @Mock
    private ExclusaoUpdateService exclusaoUpdateService;
    @InjectMocks
    private ExclusaoLayoutService exclusaoLayoutService;

    @Test
    void listarRegistrosExcluir() {
        final ExclusaoLayout exclusaoLayout = ConstantsExclusao.madeExclusaoLayout();
        when(exclusaoLayoutRepository.listarRegistrosExcluir(1)).thenReturn(Collections.singletonList(exclusaoLayout));

        List<ExclusaoLayout> sut = exclusaoLayoutService.listarRegistrosExcluir(1);
        Assertions.assertThat(sut.get(0)).isEqualTo(exclusaoLayout);
    }

    @Test
    void atualizarStatusTransmissaoSucesso() {
        final ExclusaoLayout exclusaoLayout = ConstantsExclusao.madeExclusaoLayout();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        boolean sut = exclusaoLayoutService.atualizarStatusTransmissao(exclusaoLayout, responseDto);
        Assertions.assertThat(sut).isEqualTo(true);

    }

    @Test
    void atualizarStatusTransmissaoErro() {
        final ExclusaoLayout exclusaoLayout = ConstantsExclusao.madeExclusaoLayout();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDtoErro();

        boolean sut = exclusaoLayoutService.atualizarStatusTransmissao(exclusaoLayout, responseDto);
        Assertions.assertThat(sut).isEqualTo(false);

    }

}
