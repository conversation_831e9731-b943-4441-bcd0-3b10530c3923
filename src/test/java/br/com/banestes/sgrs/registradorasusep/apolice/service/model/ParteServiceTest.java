package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.ParteRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.ParteService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Parte;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ParteServiceTest {

    @Mock
    private ParteRepository parteRepository;
    @InjectMocks
    private ParteService parteService;

    @Test
    void findAllByIdtCtlProcAndIdtCtrApolice() {
        final Parte parte = ConstantsApolice.madeParte();
        when(parteRepository.findAllByIdtCtlProcAndIdtCtrApolice(1, 1L)).thenReturn(Collections.singletonList(parte));

        List<Parte> sut = parteService.findAllByIdtCtlProcAndIdtCtrApolice(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(parte);
    }

}
