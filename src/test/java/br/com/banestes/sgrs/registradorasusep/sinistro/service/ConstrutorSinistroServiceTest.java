package br.com.banestes.sgrs.registradorasusep.sinistro.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsSinistro;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.SinistroDto;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.Sinistro;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroAuto;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroBeneficiario;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroCobertura;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroDocumento;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroEvento;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroPessoa;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroVistoria;
import br.com.banestes.sgrs.registradorasusep.sinistro.mapper.SinistroMapper;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroAutoRepository;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroBeneficiarioRepository;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroDocumentoRepository;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.ConstrutorSinistroService;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroCoberturaService;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroEventoService;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroPessoaService;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroVistoriaService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ConstrutorSinistroServiceTest {

    @Mock
    private SinistroMapper sinistroMapper;
    @Mock
    private SinistroCoberturaService sinistroCoberturaService;
    @Mock
    private SinistroPessoaService sinistroPessoaService;
    @Mock
    private SinistroEventoService sinistroEventoService;
    @Mock
    private SinistroVistoriaService sinistroVistoriaService;
    @Mock
    private SinistroDocumentoRepository sinistroDocumentoRepository;
    @Mock
    private SinistroAutoRepository sinistroAutoRepository;
    @Mock
    private SinistroBeneficiarioRepository sinistroBeneficiarioRepository;
    @InjectMocks
    private ConstrutorSinistroService construtorSinistroService;

    @Test
    void construir() {
        final Sinistro sinistro = ConstantsSinistro.madeSinistro();
        final SinistroPessoa sinistroPessoa = ConstantsSinistro.madeSinistroPessoa();
        final SinistroEvento sinistroEvento = ConstantsSinistro.madeSinistroEvento();
        final SinistroVistoria sinistroVistoria = ConstantsSinistro.madeSinistroVistoria();
        final SinistroDocumento sinistroDocumento = ConstantsSinistro.madeSinistroDocumento();
        final SinistroAuto sinistroAuto = ConstantsSinistro.madeSinistroAuto();
        final SinistroBeneficiario sinistroBeneficiario = ConstantsSinistro.madeSinistroBeneficiario();
        final SinistroDto sinistroDto = ConstantsSinistro.madeSinistroDto();

        when(sinistroPessoaService.getAllByIdtCtlProcAndIdtSntSinistro(1, 1L)).thenReturn(Collections.singletonList(sinistroPessoa));
        when(sinistroEventoService.getAllByIdtCtlProcAndIdtSntSinistro(1, 1L)).thenReturn(Collections.singletonList(sinistroEvento));
        when(sinistroVistoriaService.getAllByIdtCtlProcAndIdtSntSinistro(1, 1L)).thenReturn(Collections.singletonList(sinistroVistoria));
        when(sinistroDocumentoRepository.getAllByIdtCtlProcAndIdtSntSinistro(1, 1L)).thenReturn(Collections.singletonList(sinistroDocumento));
        when(sinistroAutoRepository.getAllByIdtCtlProcAndIdtSntSinistro(1, 1L)).thenReturn(Collections.singletonList(sinistroAuto));
        when(sinistroBeneficiarioRepository.getAllByIdtCtlProcAndIdtSntSinistro(1, 1L)).thenReturn(Collections.singletonList(sinistroBeneficiario));
        when(sinistroMapper.toDto(any(), any(), any(), any(), any(), any(), any())).thenReturn(sinistroDto);

        SinistroDto construir = construtorSinistroService.construir(sinistro);
        Assertions.assertThat(construir.getIdentificadorRegistro()).isEqualTo(sinistro.getIdentificadorRegistro());
    }

}
