package br.com.banestes.sgrs.registradorasusep.liquidacaopremio.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsPremio;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.liquidacaopremio.repository.PremioRepository;
import br.com.banestes.sgrs.registradorasusep.liquidacaopremio.service.model.LiquidacaoPremioService;
import br.com.banestes.sgrs.registradorasusep.model.premio.Premio;
import br.com.banestes.sgrs.registradorasusep.service.ControlePremioService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class LiquidacaoPremioServiceTest {

    @Mock
    private PremioRepository premioRepository;
    @Mock
    private ControlePremioService controlePremioService;
    @InjectMocks
    private LiquidacaoPremioService liquidacaoPremioService;

    @Test
    void listarLiquidacoesPremioTransmissao() {
        final Premio premio = ConstantsPremio.madePremio();
        when(premioRepository.listarLiquidacoesPremioTransmissao(1)).thenReturn(Collections.singletonList(premio));

        List<Premio> sut = liquidacaoPremioService.listarLiquidacoesPremioTransmissao(1);
        Assertions.assertThat(sut.get(0)).isEqualTo(premio);
    }

    @Test
    void atualizarStatusTransmissaoSucesso() {
        final Premio premio = ConstantsPremio.madePremio();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        boolean sut = liquidacaoPremioService.atualizarStatusTransmissao(premio, responseDto, 'I');
        Assertions.assertThat(sut).isEqualTo(true);
    }

    @Test
    void atualizarStatusTransmissaoErro() {
        final Premio premio = ConstantsPremio.madePremio();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDtoErro();

        boolean sut = liquidacaoPremioService.atualizarStatusTransmissao(premio, responseDto, 'I');
        Assertions.assertThat(sut).isEqualTo(false);
    }
}
