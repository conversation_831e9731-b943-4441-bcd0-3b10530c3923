package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.CoberturaRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.CoberturaService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Cobertura;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CoberturaServiceTest {

    @Mock
    private CoberturaRepository coberturaRepository;
    @InjectMocks
    private CoberturaService coberturaService;

    @Test
    void findAllByIdtCtlProcAndIdtCtrApoliceAndIdtCtrObjeto() {
        final Cobertura cobertura = ConstantsApolice.madeCobertura();
        when(coberturaRepository.findAllByIdtCtlProcAndIdtCtrObjeto(1, 1)).thenReturn(Collections.singletonList(cobertura));

        List<Cobertura> sut = coberturaService.findAllByIdtCtlProcAndIdtCtrApoliceAndIdtCtrObjeto(1, 1);
        Assertions.assertThat(sut.get(0)).isEqualTo(cobertura);
    }

}
