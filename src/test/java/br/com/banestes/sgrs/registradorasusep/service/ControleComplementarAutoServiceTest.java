package br.com.banestes.sgrs.registradorasusep.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsComplementar;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsExclusao;
import br.com.banestes.sgrs.registradorasusep.model.ControleComplementarAuto;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.repository.ControleComplementarAutoRepository;
import br.com.banestes.sgrs.registradorasusep.service.ControleComplementarAutoService;
import br.com.banestes.sgrs.registradorasusep.service.ControleComplementarAutoUpdateService;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ControleComplementarAutoServiceTest {

    @Mock
    private ControleComplementarAutoRepository controleComplementarAutoRepository;
    @Mock
    private ControleComplementarAutoUpdateService complementarAutoUpdateService;
    @InjectMocks
    private ControleComplementarAutoService controleComplementarAutoService;

    @Test
    void atualizar() {
        final ControleComplementarAuto controleComplementarAuto = ConstantsComplementar.madeControleComplementarAuto();
        when(controleComplementarAutoRepository.findById(1L)).thenReturn(Optional.of(controleComplementarAuto));

        assertDoesNotThrow(() -> controleComplementarAutoService.atualizar(1L, "R", "", 'I'));
    }

    @Test
    void atualizarRegistroExclusao() {
        final ExclusaoLayout exclusaoLayout = ConstantsExclusao.madeExclusaoLayout();
        final ControleComplementarAuto controleComplementarAuto = ConstantsComplementar.madeControleComplementarAuto();
        when(controleComplementarAutoRepository.findById(exclusaoLayout.getIdtLeiaute())).thenReturn(Optional.of(controleComplementarAuto));

        assertDoesNotThrow(() -> controleComplementarAutoService.atualizarRegistroExclusao(exclusaoLayout, "R"));
    }

}
