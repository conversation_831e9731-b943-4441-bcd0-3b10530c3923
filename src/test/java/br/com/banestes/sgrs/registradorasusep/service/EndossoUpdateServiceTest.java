package br.com.banestes.sgrs.registradorasusep.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsEndosso;
import br.com.banestes.sgrs.registradorasusep.service.EndossoUpdateService;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import javax.sql.DataSource;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

@ExtendWith(MockitoExtension.class)
public class EndossoUpdateServiceTest {

    @Mock
    private DataSource dataSource;
    @Mock
    private JdbcTemplate jdbcTemplateObject;
    @InjectMocks
    private EndossoUpdateService endossoUpdateService;

    @Test
    void atualizarSituacaoSucesso() {
        ReflectionTestUtils.setField(endossoUpdateService, "modoSimulacao", false);
        assertDoesNotThrow(() -> endossoUpdateService.atualizarSituacao(1L, 1, "R", "", 'I'));
    }

    @Test
    void atualizarSituacaoErroNegocio() {
        ReflectionTestUtils.setField(endossoUpdateService, "modoSimulacao", false);
        assertDoesNotThrow(() -> endossoUpdateService.atualizarSituacao(1L, 1, "E", "Erro", 'I'));
    }

    @Test
    void atualizarSituacaoErroPlataforma() {
        ReflectionTestUtils.setField(endossoUpdateService, "modoSimulacao", false);
        assertDoesNotThrow(() -> endossoUpdateService.atualizarSituacao(1L, 1, "P", "Erro", 'I'));
    }

    @Test
    void updateRegistroCorrecaoIngnorar() {
        ReflectionTestUtils.setField(endossoUpdateService, "modoSimulacao", false);
        assertDoesNotThrow(() -> endossoUpdateService.updateRegistroCorrecaoIngnorar(ConstantsEndosso.madeControleEndosso()));
    }

}
