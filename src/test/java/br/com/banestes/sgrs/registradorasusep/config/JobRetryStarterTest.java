package br.com.banestes.sgrs.registradorasusep.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JobRetryStarterTest {

    @Mock
    private JobLauncher jobLauncher;

    @Mock
    private ApplicationContext context;

    @Mock
    private Job job;

    @Mock
    private JobExecution jobExecution;

    private JobRetryStarter jobRetryStarter;

    @BeforeEach
    void setUp() {
        jobRetryStarter = new JobRetryStarter(jobLauncher, context);
        
        // Configurar propriedades para teste
        ReflectionTestUtils.setField(jobRetryStarter, "layout", "apolice");
        ReflectionTestUtils.setField(jobRetryStarter, "grupoRamo", "VIDA");
        ReflectionTestUtils.setField(jobRetryStarter, "complemento", "");
        ReflectionTestUtils.setField(jobRetryStarter, "modoSimulacao", false);
        ReflectionTestUtils.setField(jobRetryStarter, "maxRetryAttempts", 3);
        ReflectionTestUtils.setField(jobRetryStarter, "retryDelaySeconds", 1); // Reduzido para teste
        ReflectionTestUtils.setField(jobRetryStarter, "retryEnabled", true);
    }

    @Test
    void testJobSuccessOnFirstAttempt() throws Exception {
        // Arrange
        when(context.getBean("apoliceJob", Job.class)).thenReturn(job);
        when(jobLauncher.run(eq(job), any(JobParameters.class))).thenReturn(jobExecution);
        when(jobExecution.getExitStatus()).thenReturn(ExitStatus.COMPLETED);
        when(jobExecution.getStartTime()).thenReturn(new java.util.Date());
        when(jobExecution.getEndTime()).thenReturn(new java.util.Date());
        when(jobExecution.getAllFailureExceptions()).thenReturn(java.util.Collections.emptyList());

        // Act
        ExitStatus result = jobRetryStarter.jobSelectorWithRetry();

        // Assert
        assertEquals(ExitStatus.COMPLETED, result);
        verify(jobLauncher, times(1)).run(eq(job), any(JobParameters.class));
    }

    @Test
    void testJobSuccessOnSecondAttempt() throws Exception {
        // Arrange
        when(context.getBean("apoliceJob", Job.class)).thenReturn(job);
        
        // Primeira tentativa falha
        JobExecution failedExecution = mock(JobExecution.class);
        when(failedExecution.getExitStatus()).thenReturn(ExitStatus.FAILED);
        when(failedExecution.getStartTime()).thenReturn(new java.util.Date());
        when(failedExecution.getEndTime()).thenReturn(new java.util.Date());
        when(failedExecution.getAllFailureExceptions()).thenReturn(java.util.Collections.emptyList());
        
        // Segunda tentativa sucede
        when(jobExecution.getExitStatus()).thenReturn(ExitStatus.COMPLETED);
        when(jobExecution.getStartTime()).thenReturn(new java.util.Date());
        when(jobExecution.getEndTime()).thenReturn(new java.util.Date());
        when(jobExecution.getAllFailureExceptions()).thenReturn(java.util.Collections.emptyList());
        
        when(jobLauncher.run(eq(job), any(JobParameters.class)))
                .thenReturn(failedExecution)
                .thenReturn(jobExecution);

        // Act
        ExitStatus result = jobRetryStarter.jobSelectorWithRetry();

        // Assert
        assertEquals(ExitStatus.COMPLETED, result);
        verify(jobLauncher, times(2)).run(eq(job), any(JobParameters.class));
    }

    @Test
    void testJobFailsAfterMaxAttempts() throws Exception {
        // Arrange
        when(context.getBean("apoliceJob", Job.class)).thenReturn(job);
        when(jobLauncher.run(eq(job), any(JobParameters.class))).thenReturn(jobExecution);
        when(jobExecution.getExitStatus()).thenReturn(ExitStatus.FAILED);
        when(jobExecution.getStartTime()).thenReturn(new java.util.Date());
        when(jobExecution.getEndTime()).thenReturn(new java.util.Date());
        when(jobExecution.getAllFailureExceptions()).thenReturn(java.util.Collections.emptyList());

        // Act
        ExitStatus result = jobRetryStarter.jobSelectorWithRetry();

        // Assert
        assertEquals(ExitStatus.FAILED, result);
        verify(jobLauncher, times(3)).run(eq(job), any(JobParameters.class));
    }

    @Test
    void testRetryDisabled() throws Exception {
        // Arrange
        ReflectionTestUtils.setField(jobRetryStarter, "retryEnabled", false);
        when(context.getBean("apoliceJob", Job.class)).thenReturn(job);
        when(jobLauncher.run(eq(job), any(JobParameters.class))).thenReturn(jobExecution);
        when(jobExecution.getExitStatus()).thenReturn(ExitStatus.FAILED);
        when(jobExecution.getStartTime()).thenReturn(new java.util.Date());
        when(jobExecution.getEndTime()).thenReturn(new java.util.Date());
        when(jobExecution.getAllFailureExceptions()).thenReturn(java.util.Collections.emptyList());

        // Act
        ExitStatus result = jobRetryStarter.jobSelectorWithRetry();

        // Assert
        assertEquals(ExitStatus.FAILED, result);
        verify(jobLauncher, times(1)).run(eq(job), any(JobParameters.class));
    }

    @Test
    void testInvalidLayout() {
        // Arrange
        ReflectionTestUtils.setField(jobRetryStarter, "layout", "invalidLayout");

        // Act
        ExitStatus result = jobRetryStarter.jobSelectorWithRetry();

        // Assert
        assertEquals(ExitStatus.FAILED, result);
        verify(jobLauncher, never()).run(any(Job.class), any(JobParameters.class));
    }
}
