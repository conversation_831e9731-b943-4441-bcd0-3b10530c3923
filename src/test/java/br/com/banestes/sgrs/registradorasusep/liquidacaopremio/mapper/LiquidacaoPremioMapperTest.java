package br.com.banestes.sgrs.registradorasusep.liquidacaopremio.mapper;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsPremio;
import br.com.banestes.sgrs.registradorasusep.dto.premio.LiquidacaoPremioDto;
import br.com.banestes.sgrs.registradorasusep.liquidacaopremio.mapper.LiquidacaoPremioMapper;
import br.com.banestes.sgrs.registradorasusep.model.premio.Premio;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class LiquidacaoPremioMapperTest {

    @Test
    void toDto() {
        LiquidacaoPremioMapper liquidacaoPremioMapper = new LiquidacaoPremioMapper();
        final Premio premio = ConstantsPremio.madePremio();

        LiquidacaoPremioDto sut = liquidacaoPremioMapper.toDto(premio);
        Assertions.assertThat(premio.getIdentificadorRegistro()).isEqualTo(sut.getIdentificadorRegistro());
    }

}
