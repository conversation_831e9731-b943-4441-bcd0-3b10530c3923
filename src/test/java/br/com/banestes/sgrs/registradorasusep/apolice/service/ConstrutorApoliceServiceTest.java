package br.com.banestes.sgrs.registradorasusep.apolice.service;

import br.com.banestes.sgrs.registradorasusep.apolice.mapper.ApoliceMapper;
import br.com.banestes.sgrs.registradorasusep.apolice.repository.ContratoColetivoApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.repository.ParcelaApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.AutomovelApoliceService;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.IntermediarioService;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.ParteService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.ApoliceDto;
import br.com.banestes.sgrs.registradorasusep.model.apolice.*;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import java.util.Collections;
import java.util.List;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ConstrutorApoliceServiceTest {

    @Mock
    private ApoliceMapper apoliceMapper;
    @Mock
    private ConstrutorObjetoSeguradoCompletoService construtorObjetoSeguradoCompletoService;
    @Mock
    private IntermediarioService intermediarioService;
    @Mock
    private ParteService parteService;
    @Mock
    private AutomovelApoliceService automovelApoliceService;
    @Mock
    private ParcelaApoliceRepository parcelaApoliceRepository;
    @Mock
    private ContratoColetivoApoliceRepository contratoColetivoApoliceRepository;
    @InjectMocks
    private ConstrutorApoliceService construtorApoliceService;

    @BeforeEach
    void before() {
        ReflectionTestUtils.setField(construtorApoliceService, "grupoRamo", "AUTO");
    }

    @Test
    void construirAuto() {

        ReflectionTestUtils.setField(construtorApoliceService, "grupoRamo", "AUTO");

        final Apolice apolice = ConstantsApolice.madeApolice();
        final List<Parte> partes = Collections.singletonList(ConstantsApolice.madeParte());
        final List<Intermediario> intermediarios = Collections.singletonList(ConstantsApolice.madeIntermediario());
        final List<ObjetoSeguradoCompleto> objetosSeguradoCompleto = Collections.singletonList(ConstantsApolice.madeObjetoSeguradoCompleto());
        final List<AutomovelApolice> automoveisApolice = Collections.singletonList(ConstantsApolice.madeAutomovelApolice());
        final List<ParcelaApolice> parcelasApolice = Collections.singletonList(ConstantsApolice.madeParcelaApolice());
        final ApoliceDto apoliceDto = ConstantsApolice.madeApoliceDto();

        when(automovelApoliceService.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice())).thenReturn(automoveisApolice);
        when(parteService.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice())).thenReturn(partes);
        when(intermediarioService.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice())).thenReturn(intermediarios);
        when(construtorObjetoSeguradoCompletoService.obterObjetosSeguradosCompletosApolice(apolice)).thenReturn(objetosSeguradoCompleto);
        when(parcelaApoliceRepository.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice())).thenReturn(parcelasApolice);
        when(apoliceMapper.toDto(any(), any(), any(), any(), any(), any(), any())).thenReturn(apoliceDto);

        ApoliceDto sut = construtorApoliceService.construir(apolice);
        Assertions.assertThat(sut.getIdentificadorRegistro()).isEqualTo(apolice.getIdentificadorRegistro());
    }

    @Test
    void construirRe() {

        ReflectionTestUtils.setField(construtorApoliceService, "grupoRamo", "RE");

        final Apolice apolice = ConstantsApolice.madeApolice();
        final List<Parte> partes = Collections.singletonList(ConstantsApolice.madeParte());
        final List<Intermediario> intermediarios = Collections.singletonList(ConstantsApolice.madeIntermediario());
        final List<ObjetoSeguradoCompleto> objetosSeguradoCompleto = Collections.singletonList(ConstantsApolice.madeObjetoSeguradoCompleto());
        final List<ParcelaApolice> parcelasApolice = Collections.singletonList(ConstantsApolice.madeParcelaApolice());
        final ApoliceDto apoliceDto = ConstantsApolice.madeApoliceDto();
        when(apoliceMapper.toDto(any(), any(), any(), any(), any(), any(), any())).thenReturn(apoliceDto);

        when(parteService.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice())).thenReturn(partes);
        when(intermediarioService.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice())).thenReturn(intermediarios);
        when(construtorObjetoSeguradoCompletoService.obterObjetosSeguradosCompletosApolice(apolice)).thenReturn(objetosSeguradoCompleto);
        when(parcelaApoliceRepository.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice())).thenReturn(parcelasApolice);

        ApoliceDto sut = construtorApoliceService.construir(apolice);
        Assertions.assertThat(sut.getIdentificadorRegistro()).isEqualTo(apolice.getIdentificadorRegistro());
    }

    @Test
    void construirVida() {

        ReflectionTestUtils.setField(construtorApoliceService, "grupoRamo", "VIDA");

        final Apolice apolice = ConstantsApolice.madeApolice();
        final List<Parte> partes = Collections.singletonList(ConstantsApolice.madeParte());
        final List<Intermediario> intermediarios = Collections.singletonList(ConstantsApolice.madeIntermediario());
        final List<ObjetoSeguradoCompleto> objetosSeguradoCompleto = Collections.singletonList(ConstantsApolice.madeObjetoSeguradoCompleto());
        final List<ParcelaApolice> parcelasApolice = Collections.singletonList(ConstantsApolice.madeParcelaApolice());
        final ApoliceDto apoliceDto = ConstantsApolice.madeApoliceDto();
        final List<ContratoColetivoApolice> contratosColetivoApolice = Collections.singletonList(ConstantsApolice.madeContratoColetivoApolice());
        when(apoliceMapper.toDto(any(), any(), any(), any(), any(), any(), any())).thenReturn(apoliceDto);

        when(parteService.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice())).thenReturn(partes);
        when(intermediarioService.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice())).thenReturn(intermediarios);
        when(construtorObjetoSeguradoCompletoService.obterObjetosSeguradosCompletosApolice(apolice)).thenReturn(objetosSeguradoCompleto);
        when(parcelaApoliceRepository.findAllByIdtCtlProcAndIdtCtrApolice(apolice.getIdtCtlProc(), apolice.getIdtCtrApolice())).thenReturn(parcelasApolice);
        when(contratoColetivoApoliceRepository.findAllByidtCtrApoliceAndIdtCtlProc(apolice.getIdtCtrApolice(), apolice.getIdtCtlProc())).thenReturn(contratosColetivoApolice);

        ApoliceDto sut = construtorApoliceService.construir(apolice);
        Assertions.assertThat(sut.getIdentificadorRegistro()).isEqualTo(apolice.getIdentificadorRegistro());
    }

}
