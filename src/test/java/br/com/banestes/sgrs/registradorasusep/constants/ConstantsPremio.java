package br.com.banestes.sgrs.registradorasusep.constants;

import java.math.BigDecimal;

import br.com.banestes.sgrs.registradorasusep.dto.premio.LiquidacaoPremioDto;
import br.com.banestes.sgrs.registradorasusep.model.ControlePremio;
import br.com.banestes.sgrs.registradorasusep.model.premio.Premio;

public class ConstantsPremio {

    public static Premio madePremio() {
        Premio premio = new Premio();
        premio.setIdtPrmPremio(1L);
        premio.setIdtCtlProc(1);
        premio.setIdentificadorRegistro("111");
        premio.setIdentificadorMovimento("1");
        premio.setValorSegurado(BigDecimal.TEN);
        premio.setValorEstipulante(BigDecimal.TEN);
        return premio;
    }

    public static LiquidacaoPremioDto madeLiquidacaoPremioDto() {
        LiquidacaoPremioDto liquidacaoPremioDto = new LiquidacaoPremioDto();
        liquidacaoPremioDto.setIdentificadorMovimento("111");
        liquidacaoPremioDto.setCodigoSeguradora("12");
        liquidacaoPremioDto.setApoliceCodigo("123");
        return liquidacaoPremioDto;
    }

    public static ControlePremio madeControlePremio() {
        ControlePremio controlePremio = new ControlePremio();
        controlePremio.setIdtCtlPremio(1L);
        controlePremio.setIdcSitProc("R");
        controlePremio.setIdtCtlPremioAtu(1L);
        controlePremio.setIdtCtlProc(1);
        return controlePremio;
    }


}
