package br.com.banestes.sgrs.registradorasusep.sinistro.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsSinistro;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroCobertura;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroCoberturaRepository;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroCoberturaService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SinistroCoberturaServiceTest {

    @Mock
    private SinistroCoberturaRepository sinistroCoberturaRepository;
    @InjectMocks
    private SinistroCoberturaService sinistroCoberturaService;

    @Test
    void getAllByIdtSntDocumento() {
        final SinistroCobertura sinistroCobertura = ConstantsSinistro.madeSinistroCobertura();
        when(sinistroCoberturaRepository.getAllByIdtSntDocumento(1L)).thenReturn(Collections.singletonList(sinistroCobertura));

        List<SinistroCobertura> sut = sinistroCoberturaService.getAllByIdtSntDocumento(1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(sinistroCobertura);
    }

}
