package br.com.banestes.sgrs.registradorasusep.constants;

import br.com.banestes.sgrs.registradorasusep.dto.complementar.ComplementarDto;
import br.com.banestes.sgrs.registradorasusep.model.ControleComplementarAuto;
import br.com.banestes.sgrs.registradorasusep.model.complementar.Complementar;
import br.com.banestes.sgrs.registradorasusep.model.complementar.ComplementarCobertura;
import br.com.banestes.sgrs.registradorasusep.model.complementar.ComplementarPessoa;

public class ConstantsComplementar {

    public static Complementar madeComplementar() {
        Complementar complementar = new Complementar();
        complementar.setIdtCmpAuto(1L);
        complementar.setIdtCtlProc(1);
        complementar.setIdcSelProc("1");
        complementar.setCodEmpresa(1);
        complementar.setIdentificadorRegistro("1");
        return complementar;
    }

    public static ComplementarCobertura madeComplementarCobertura() {
        ComplementarCobertura complementarCobertura = new ComplementarCobertura();
        complementarCobertura.setIdtCmpCobertura(1L);
        complementarCobertura.setIdtCmpAuto(1L);
        complementarCobertura.setIdtCtlProc(1);
        complementarCobertura.setCodCobertura(1);
        return complementarCobertura;
    }

    public static ComplementarPessoa madeComplementarPessoa() {
        ComplementarPessoa complementarPessoa = new ComplementarPessoa();
        complementarPessoa.setIdtCmpAuto(1L);
        complementarPessoa.setIdtCmpPessoa(1L);
        complementarPessoa.setIdtCtlProc(1);
        return complementarPessoa;
    }

    public static ComplementarDto madeComplementarDto() {
        ComplementarDto complementarDto = new ComplementarDto();
        complementarDto.setIdentificadorRegistro("1");
        complementarDto.setApoliceCodigo("11111");
        return complementarDto;
    }

    public static ControleComplementarAuto madeControleComplementarAuto() {
        ControleComplementarAuto controleComplementarAuto = new ControleComplementarAuto();
        controleComplementarAuto.setIdtCtlProc(1);
        controleComplementarAuto.setIdtCtlCmpAuto(1L);
        controleComplementarAuto.setIdtCtlCmpAuto(1L);
        controleComplementarAuto.setIdcSitProc("R");
        return controleComplementarAuto;
    }

}
