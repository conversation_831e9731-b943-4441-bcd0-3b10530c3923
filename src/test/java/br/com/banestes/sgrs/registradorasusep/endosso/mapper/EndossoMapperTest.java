package br.com.banestes.sgrs.registradorasusep.endosso.mapper;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsEndosso;
import br.com.banestes.sgrs.registradorasusep.dto.endosso.EndossoDto;
import br.com.banestes.sgrs.registradorasusep.endosso.mapper.EndossoMapper;
import br.com.banestes.sgrs.registradorasusep.model.endosso.*;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import java.util.Collections;

public class EndossoMapperTest {

    @Test
    void toDto() {
        EndossoMapper endossoMapper = new EndossoMapper();
        final Endosso endosso = ConstantsEndosso.madeEndosso();
        final ParteEndosso parteEndosso = ConstantsEndosso.madeParteEndosso();
        final IntermediarioEndosso intermediarioEndosso = ConstantsEndosso.madeIntermediarioEndosso();
        final ObjetoSeguradoCompleto objetoSeguradoCompleto = ConstantsEndosso.madeObjetoSeguradoCompleto();
        final AutomovelEndosso automovelEndosso = ConstantsEndosso.madeAutomovelEndosso();
        final ParcelaEndosso parcelaEndosso = ConstantsEndosso.madeParcelaEndosso();
        final ContratoColetivoEndosso contratoColetivoEndosso = ConstantsEndosso.madeContratoColetivoEndosso();

        EndossoDto sut = endossoMapper.toDto(endosso,
                Collections.singletonList(parteEndosso),
                Collections.singletonList(intermediarioEndosso),
                Collections.singletonList(objetoSeguradoCompleto),
                Collections.singletonList(automovelEndosso),
                Collections.singletonList(parcelaEndosso),
                Collections.singletonList(contratoColetivoEndosso));

        Assertions.assertThat(sut.getIdentificadorRegistro()).isEqualTo(endosso.getIdentificadorRegistro());
    }

}
