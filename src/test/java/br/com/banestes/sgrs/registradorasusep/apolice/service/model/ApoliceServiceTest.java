package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.ApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.ApoliceService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Apolice;
import br.com.banestes.sgrs.registradorasusep.service.ControleApoliceService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ApoliceServiceTest {

    @Mock
    private ApoliceRepository apoliceRepository;
    @Mock
    private ControleApoliceService controleApoliceService;
    @InjectMocks
    private ApoliceService apoliceService;

    @Test
    void listarApolicesTransmissao() {
        final Apolice apolice = ConstantsApolice.madeApolice();
        when(apoliceRepository.listarApolicesTransmissao(1)).thenReturn(Collections.singletonList(apolice));

        List<Apolice> sut = apoliceService.listarApolicesTransmissao(1);
        Assertions.assertThat(sut.get(0)).isEqualTo(apolice);
    }

    @Test
    void atualizarStatusTransmissaoSucesso() {
        final Apolice apolice = ConstantsApolice.madeApolice();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        boolean sut = apoliceService.atualizarStatusTransmissao(apolice, responseDto, 'I');
        Assertions.assertThat(sut).isEqualTo(true);

    }

    @Test
    void atualizarStatusTransmissaoErro() {
        final Apolice apolice = ConstantsApolice.madeApolice();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDtoErro();

        boolean sut = apoliceService.atualizarStatusTransmissao(apolice, responseDto, 'I');
        Assertions.assertThat(sut).isEqualTo(false);

    }


}
