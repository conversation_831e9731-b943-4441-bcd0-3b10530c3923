package br.com.banestes.sgrs.registradorasusep.apolice.mapper;

import br.com.banestes.sgrs.registradorasusep.apolice.mapper.ApoliceMapper;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.dto.apolice.ApoliceDto;
import br.com.banestes.sgrs.registradorasusep.model.apolice.*;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import java.util.Collections;
import java.util.List;

public class ApoliceMapperTest {

    private final ApoliceMapper apoliceMapper = new ApoliceMapper();

    @Test
    void toDto() {
        final Apolice apolice = ConstantsApolice.madeApolice();
        final List<Parte> partes = Collections.singletonList(ConstantsApolice.madeParte());
        final List<Intermediario> intermediarios = Collections.singletonList(ConstantsApolice.madeIntermediario());
        final List<ObjetoSeguradoCompleto> objetosSeguradoCompleto = Collections.singletonList(ConstantsApolice.madeObjetoSeguradoCompleto());
        final List<AutomovelApolice> automoveisApolice = Collections.singletonList(ConstantsApolice.madeAutomovelApolice());
        final List<ParcelaApolice> parcelasApolice = Collections.singletonList(ConstantsApolice.madeParcelaApolice());
        final List<ContratoColetivoApolice> contratoColetivoApolice = Collections.singletonList(ConstantsApolice.madeContratoColetivoApolice());


        ApoliceDto sut = apoliceMapper.toDto(apolice, partes, intermediarios, objetosSeguradoCompleto, automoveisApolice, parcelasApolice, contratoColetivoApolice);
        Assertions.assertThat(sut.getIdentificadorRegistro()).isEqualTo(apolice.getIdentificadorRegistro());
    }

}
