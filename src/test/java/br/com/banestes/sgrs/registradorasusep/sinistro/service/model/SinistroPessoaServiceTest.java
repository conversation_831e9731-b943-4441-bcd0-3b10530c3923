package br.com.banestes.sgrs.registradorasusep.sinistro.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsSinistro;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroPessoa;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroPessoaRepository;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroPessoaService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SinistroPessoaServiceTest {

    @Mock
    private SinistroPessoaRepository sinistroPessoaRepository;
    @InjectMocks
    private SinistroPessoaService sinistroPessoaService;

    @Test
    void getAllByIdtCtlProcAndIdtSntSinistro() {
        final SinistroPessoa sinistroPessoa = ConstantsSinistro.madeSinistroPessoa();
        when(sinistroPessoaRepository.getAllByIdtCtlProcAndIdtSntSinistro(1, 1L)).thenReturn(Collections.singletonList(sinistroPessoa));

        List<SinistroPessoa> sut = sinistroPessoaService.getAllByIdtCtlProcAndIdtSntSinistro(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(sinistroPessoa);
    }

}
