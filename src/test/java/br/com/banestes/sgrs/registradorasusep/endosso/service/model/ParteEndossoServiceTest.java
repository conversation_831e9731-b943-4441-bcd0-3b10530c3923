package br.com.banestes.sgrs.registradorasusep.endosso.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsEndosso;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.ParteEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.ParteEndossoService;
import br.com.banestes.sgrs.registradorasusep.model.endosso.ParteEndosso;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ParteEndossoServiceTest {

    @Mock
    private ParteEndossoRepository parteRepository;
    @InjectMocks
    private ParteEndossoService parteEndossoService;

    @Test
    void findAllByIdtCtlProcAndIdtCtrApolice() {
        final ParteEndosso parteEndosso = ConstantsEndosso.madeParteEndosso();
        when(parteRepository.findAllByIdtCtlProcAndIdtEdsEndosso(1, 1L)).thenReturn(Collections.singletonList(parteEndosso));

        List<ParteEndosso> sut = parteEndossoService.findAllByIdtCtlProcAndIdtCtrApolice(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(parteEndosso);
    }

}
