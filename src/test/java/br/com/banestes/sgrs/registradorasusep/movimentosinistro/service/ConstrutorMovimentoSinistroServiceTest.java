package br.com.banestes.sgrs.registradorasusep.movimentosinistro.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsMovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.dto.movimentosinistro.MovimentoSinistroDto;
import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistroAdicional;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.mapper.MovimentoSinistroMapper;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.service.ConstrutorMovimentoSinistroService;
import br.com.banestes.sgrs.registradorasusep.movimentosinistro.service.model.MovimentoSinistroAdicionalService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ConstrutorMovimentoSinistroServiceTest {

    @Mock
    private MovimentoSinistroMapper movimentoSinistroMapper;
    @Mock
    private MovimentoSinistroAdicionalService movimentoSinistroAdicionalService;
    @InjectMocks
    private ConstrutorMovimentoSinistroService construtorMovimentoSinistroService;

    @Test
    void construir() {
        final MovimentoSinistro movimentoSinistro = ConstantsMovimentoSinistro.madeMovimentoSinistro();
        final MovimentoSinistroDto movimentoSinistroDto = ConstantsMovimentoSinistro.madeMovimentoSinistroDto();
        final MovimentoSinistroAdicional movimentoSinistroAdicional = ConstantsMovimentoSinistro.madeMovimentoSinistroAdicional();

        when(movimentoSinistroAdicionalService.getAllByIdtCtlProcAndIdtSntMovSinistro(1, 1L)).thenReturn(Collections.singletonList(movimentoSinistroAdicional));
        when(movimentoSinistroMapper.toDto(any(), any())).thenReturn(movimentoSinistroDto);

        MovimentoSinistroDto sut = construtorMovimentoSinistroService.construir(movimentoSinistro);
        Assertions.assertThat(sut.getIdentificadorRegistro()).isEqualTo(movimentoSinistroDto.getIdentificadorRegistro());
    }

}
