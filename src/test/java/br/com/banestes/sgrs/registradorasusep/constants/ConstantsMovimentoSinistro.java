package br.com.banestes.sgrs.registradorasusep.constants;

import br.com.banestes.sgrs.registradorasusep.dto.movimentosinistro.MovimentoSinistroDto;
import br.com.banestes.sgrs.registradorasusep.model.ControleMovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistro;
import br.com.banestes.sgrs.registradorasusep.model.movimentosinistro.MovimentoSinistroAdicional;

public class ConstantsMovimentoSinistro {

    public static MovimentoSinistro madeMovimentoSinistro() {
        MovimentoSinistro movimentoSinistro = new MovimentoSinistro();
        movimentoSinistro.setIdtSntMovSinistro(1L);
        movimentoSinistro.setIdtCtlProc(1);
        movimentoSinistro.setIdentificadorMovimento("1");
        movimentoSinistro.setIdentificadorRegistro("111");
        return movimentoSinistro;
    }

    public static MovimentoSinistroAdicional madeMovimentoSinistroAdicional() {
        MovimentoSinistroAdicional movimentoSinistroAdicional = new MovimentoSinistroAdicional();
        movimentoSinistroAdicional.setIdtCtlProc(1);
        movimentoSinistroAdicional.setIdtSntMovSinistro(1L);
        movimentoSinistroAdicional.setIdtSntMovAdicional(1L);
        return movimentoSinistroAdicional;
    }

    public static MovimentoSinistroDto madeMovimentoSinistroDto() {
        MovimentoSinistroDto movimentoSinistroDto = new MovimentoSinistroDto();
        movimentoSinistroDto.setIdentificadorMovimento("111");
        movimentoSinistroDto.setCodigoSeguradora("1");
        movimentoSinistroDto.setApoliceCodigo("111");
        return movimentoSinistroDto;
    }

    public static ControleMovimentoSinistro madeControleMovimentoSinistro() {
        ControleMovimentoSinistro controleMovimentoSinistro = new ControleMovimentoSinistro();
        controleMovimentoSinistro.setIdtPcsSinistro(1);
        controleMovimentoSinistro.setIdtCtlMovSinistro(1L);
        controleMovimentoSinistro.setIdtCtlMovSinistroAtu(1L);
        controleMovimentoSinistro.setIdtCtlProc(1);
        return controleMovimentoSinistro;
    }

}
