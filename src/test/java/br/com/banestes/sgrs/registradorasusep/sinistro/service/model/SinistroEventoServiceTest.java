package br.com.banestes.sgrs.registradorasusep.sinistro.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsSinistro;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.SinistroEvento;
import br.com.banestes.sgrs.registradorasusep.sinistro.repository.SinistroEventoRepository;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroEventoService;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SinistroEventoServiceTest {

    @Mock
    private SinistroEventoRepository sinistroEventoRepository;
    @InjectMocks
    private SinistroEventoService sinistroEventoService;

    @Test
    void getAllByIdtCtlProcAndIdtSntSinistro() {
        final SinistroEvento sinistroEvento = ConstantsSinistro.madeSinistroEvento();
        when(sinistroEventoRepository.getAllByIdtCtlProcAndIdtSntSinistro(1, 1L)).thenReturn(Collections.singletonList(sinistroEvento));

        List<SinistroEvento> sut = sinistroEventoService.getAllByIdtCtlProcAndIdtSntSinistro(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(sinistroEvento);
    }

}
