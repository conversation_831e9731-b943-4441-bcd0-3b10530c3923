package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.FranquiaRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.FranquiaService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Cobertura;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Franquia;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class FranquiaServiceTest {

    @Mock
    private FranquiaRepository franquiaRepository;
    @InjectMocks
    private FranquiaService franquiaService;

    @Test
    void findAllByIdtCtlProcAndIdtCtrObjetoSeg() {
        final Cobertura cobertura = ConstantsApolice.madeCobertura();
        final Franquia franquia = ConstantsApolice.madeFranquia();
        when(franquiaRepository.findAllByIdtCtlProcAndIdtCtrObjetoSeg(cobertura.getIdtCtlProc(),
                cobertura.getIdtCtrObjeto())).thenReturn(Collections.singletonList(franquia));

        List<Franquia> sut = franquiaService.findAllByIdtCtlProcAndIdtCtrObjetoSeg(cobertura);
        Assertions.assertThat(sut.get(0)).isEqualTo(franquia);
    }

}
