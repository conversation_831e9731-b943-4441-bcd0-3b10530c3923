package br.com.banestes.sgrs.registradorasusep.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsExclusao;
import br.com.banestes.sgrs.registradorasusep.model.ControleApolice;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.repository.ControleApoliceRepository;
import br.com.banestes.sgrs.registradorasusep.service.ApoliceUpdateService;
import br.com.banestes.sgrs.registradorasusep.service.ControleApoliceService;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ControleApoliceServiceTest {

    @Mock
    private ControleApoliceRepository controleApoliceRepository;
    @Mock
    private ApoliceUpdateService apoliceUpdateService;
    @InjectMocks
    private ControleApoliceService controleApoliceService;

    @Test
    void atualizar() {
        final ControleApolice apolice = ConstantsApolice.madeControleApolice();
        when(controleApoliceRepository.findById(1L)).thenReturn(Optional.of(apolice));

        assertDoesNotThrow(() -> controleApoliceService.atualizar(1L, "R", "Erro Teste", 'I'));
    }

    @Test
    void atualizarRegistroExclusao() {
        final ExclusaoLayout exclusaoLayout = ConstantsExclusao.madeExclusaoLayout();
        final ControleApolice apolice = ConstantsApolice.madeControleApolice();
        when(controleApoliceRepository.findById(exclusaoLayout.getIdtLeiaute())).thenReturn(Optional.of(apolice));

        assertDoesNotThrow(() -> controleApoliceService.atualizarRegistroExclusao(exclusaoLayout, "R"));
    }

}
