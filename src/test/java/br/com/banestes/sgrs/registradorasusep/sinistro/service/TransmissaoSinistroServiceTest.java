package br.com.banestes.sgrs.registradorasusep.sinistro.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsSinistro;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.dto.sinistro.SinistroDto;
import br.com.banestes.sgrs.registradorasusep.model.sinistro.Sinistro;
import br.com.banestes.sgrs.registradorasusep.service.EnvioSusepService;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.ConstrutorSinistroService;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.TransmissaoSinistroService;
import br.com.banestes.sgrs.registradorasusep.sinistro.service.model.SinistroService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpMethod;
import org.springframework.test.util.ReflectionTestUtils;
import java.util.Collections;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TransmissaoSinistroServiceTest {

    @Mock
    private SinistroService sinistroService;
    @Mock
    private ConstrutorSinistroService construtorSinistroService;
    @Mock
    private EnvioSusepService envioSusepService;
    @InjectMocks
    private TransmissaoSinistroService transmissaoSinistroService;

    @BeforeEach
    void before() {
        ReflectionTestUtils.setField(transmissaoSinistroService, "numeroErrosPlataforma", 10);
    }

    @Test
    void transmitirSinistros_inclusao() {
        final Sinistro sinistro = ConstantsSinistro.madeSinistro();
        final SinistroDto sinistroDto = ConstantsSinistro.madeSinistroDto();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        when(sinistroService.listarSinistrosTransmissao(1)).thenReturn(Collections.singletonList(sinistro));
        when(sinistroService.atualizarStatusTransmissao(sinistro, responseDto, 'I')).thenReturn(true);
        when(envioSusepService.transmitir(sinistroDto, "sinistro", HttpMethod.POST)).thenReturn(responseDto);
        when(construtorSinistroService.construir(sinistro)).thenReturn(sinistroDto);

        assertDoesNotThrow(() -> transmissaoSinistroService.transmitirSinistros(1, 'I'));
    }

    @Test
    void transmitirSinistros_alteracao() {
        final Sinistro sinistro = ConstantsSinistro.madeSinistro();
        final SinistroDto sinistroDto = ConstantsSinistro.madeSinistroDto();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        when(sinistroService.listarSinistrosTransmissao(1)).thenReturn(Collections.singletonList(sinistro));
        when(sinistroService.atualizarStatusTransmissao(sinistro, responseDto, 'A')).thenReturn(true);
        when(envioSusepService.transmitir(sinistroDto, "sinistro/" + sinistro.getIdentificadorRegistro(), HttpMethod.PUT)).thenReturn(responseDto);
        when(construtorSinistroService.construir(sinistro)).thenReturn(sinistroDto);

        assertDoesNotThrow(() -> transmissaoSinistroService.transmitirSinistros(1, 'A'));
    }


}
