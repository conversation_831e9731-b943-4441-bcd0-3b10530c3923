package br.com.banestes.sgrs.registradorasusep.apolice.service.model;

import br.com.banestes.sgrs.registradorasusep.apolice.repository.AdicionalRepository;
import br.com.banestes.sgrs.registradorasusep.apolice.service.model.AdicionalService;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Adicional;
import br.com.banestes.sgrs.registradorasusep.model.apolice.Cobertura;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AdicionalServiceTest {

    @Mock
    private AdicionalRepository adicionalRepository;
    @InjectMocks
    private AdicionalService adicionalService;

    @Test
    void findAllByIdtCtlProcAndIdtCtrApoliceAndIdtCtrObjeto() {
        final Cobertura cobertura = ConstantsApolice.madeCobertura();
        final Adicional adicional = ConstantsApolice.madeAdicional();

        when(adicionalRepository.findAllByIdtCtlProcAndIdtCtrObjeto(cobertura.getIdtCtlProc(),
                cobertura.getIdtCtrObjeto())).thenReturn(Collections.singletonList(adicional));

        List<Adicional> sut = adicionalService.findAllByIdtCtlProcAndIdtCtrApoliceAndIdtCtrObjeto(cobertura);
        Assertions.assertThat(sut.get(0)).isEqualTo(adicional);
    }
}
