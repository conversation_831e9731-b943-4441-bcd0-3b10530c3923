package br.com.banestes.sgrs.registradorasusep.liquidacaopremio.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsPremio;
import br.com.banestes.sgrs.registradorasusep.dto.premio.LiquidacaoPremioDto;
import br.com.banestes.sgrs.registradorasusep.liquidacaopremio.mapper.LiquidacaoPremioMapper;
import br.com.banestes.sgrs.registradorasusep.liquidacaopremio.service.ConstrutorLiquidacaoPremioService;
import br.com.banestes.sgrs.registradorasusep.model.premio.Premio;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ConstrutorLiquidacaoPremioServiceTest {

    @Mock
    private LiquidacaoPremioMapper liquidacaoPremioMapper;
    @InjectMocks
    private ConstrutorLiquidacaoPremioService construtorLiquidacaoPremioService;

    @Test
    void construir() {
        final Premio premio = ConstantsPremio.madePremio();
        final LiquidacaoPremioDto liquidacaoPremioDto = ConstantsPremio.madeLiquidacaoPremioDto();
        when(liquidacaoPremioMapper.toDto(premio)).thenReturn(liquidacaoPremioDto);
        LiquidacaoPremioDto sut = construtorLiquidacaoPremioService.construir(premio);

        Assertions.assertThat(sut.getIdentificadorRegistro()).isEqualTo(liquidacaoPremioDto.getIdentificadorRegistro());
    }

}
