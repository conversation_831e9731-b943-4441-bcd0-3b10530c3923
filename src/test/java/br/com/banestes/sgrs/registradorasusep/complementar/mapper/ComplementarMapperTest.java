package br.com.banestes.sgrs.registradorasusep.complementar.mapper;

import br.com.banestes.sgrs.registradorasusep.complementar.mapper.ComplementarMapper;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsComplementar;
import br.com.banestes.sgrs.registradorasusep.dto.complementar.ComplementarDto;
import br.com.banestes.sgrs.registradorasusep.model.complementar.Complementar;
import br.com.banestes.sgrs.registradorasusep.model.complementar.ComplementarCobertura;
import br.com.banestes.sgrs.registradorasusep.model.complementar.ComplementarPessoa;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import java.util.Collections;

public class ComplementarMapperTest {

    private final ComplementarMapper complementarMapper = new ComplementarMapper();

    @Test
    void toDto() {
        final Complementar complementar = ConstantsComplementar.madeComplementar();
        final ComplementarCobertura complementarCobertura = ConstantsComplementar.madeComplementarCobertura();
        final ComplementarPessoa complementarPessoa = ConstantsComplementar.madeComplementarPessoa();

        ComplementarDto sut = complementarMapper.toDto(complementar,
                Collections.singletonList(complementarCobertura),
                Collections.singletonList(complementarPessoa));

        Assertions.assertThat(sut.getIdentificadorRegistro()).isEqualTo(complementar.getIdentificadorRegistro());
    }

}
