package br.com.banestes.sgrs.registradorasusep.endosso.service.model;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsEndosso;
import br.com.banestes.sgrs.registradorasusep.endosso.repository.ObjetoSeguradoEndossoRepository;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.ObjetoSeguradoEndossoService;
import br.com.banestes.sgrs.registradorasusep.model.endosso.ObjetoSeguradoEndosso;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;
import java.util.List;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ObjetoSeguradoEndossoServiceTest {

    @Mock
    private ObjetoSeguradoEndossoRepository objetoSeguradoRepository;
    @InjectMocks
    private ObjetoSeguradoEndossoService objetoSeguradoEndossoService;

    @Test
    void findAllByIdtCtlProcAndIdtEdsEndosso() {
        final ObjetoSeguradoEndosso objetoSeguradoEndosso = ConstantsEndosso.madeObjetoSeguradoEndosso();
        when(objetoSeguradoRepository.findAllByIdtCtlProcAndIdtEdsEndosso(1, 1L)).thenReturn(Collections.singletonList(objetoSeguradoEndosso));

        List<ObjetoSeguradoEndosso> sut = objetoSeguradoEndossoService.findAllByIdtCtlProcAndIdtEdsEndosso(1, 1L);
        Assertions.assertThat(sut.get(0)).isEqualTo(objetoSeguradoEndosso);
    }

}
