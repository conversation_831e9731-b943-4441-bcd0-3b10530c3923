package br.com.banestes.sgrs.registradorasusep.endosso.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsApolice;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsEndosso;
import br.com.banestes.sgrs.registradorasusep.dto.ResponseDto;
import br.com.banestes.sgrs.registradorasusep.dto.endosso.EndossoDto;
import br.com.banestes.sgrs.registradorasusep.endosso.service.model.EndossoService;
import br.com.banestes.sgrs.registradorasusep.model.endosso.Endosso;
import br.com.banestes.sgrs.registradorasusep.service.EnvioSusepService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpMethod;
import org.springframework.test.util.ReflectionTestUtils;
import java.util.Collections;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TransmissaoEndossoServiceTest {

    @Mock
    private ConstrutorEndossoService construtorEndossoService;
    @Mock
    private EndossoService endossoService;
    @Mock
    private EnvioSusepService envioSusepService;
    @InjectMocks
    private TransmissaoEndossoService transmissaoEndossoService;

    @BeforeEach
    void before() {
        ReflectionTestUtils.setField(transmissaoEndossoService, "numeroErrosPlataforma", 10);
    }

    @Test
    void transmitirComplementares_inclusao() {
        final Endosso endosso = ConstantsEndosso.madeEndosso();
        final EndossoDto endossoDto = ConstantsEndosso.madeEndossoDto();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        when(endossoService.listarEndossosTransmissao(1)).thenReturn(Collections.singletonList(endosso));
        when(endossoService.atualizarStatusTransmissao(endosso, responseDto, 'I')).thenReturn(true);
        when(envioSusepService.transmitir(endossoDto, "endosso", HttpMethod.POST)).thenReturn(responseDto);
        when(construtorEndossoService.construir(endosso)).thenReturn(endossoDto);

        assertDoesNotThrow(() -> transmissaoEndossoService.transmitirEndossos(1, 'I'));
    }

    @Test
    void transmitirComplementares_alteracao() {
        final Endosso endosso = ConstantsEndosso.madeEndosso();
        final EndossoDto endossoDto = ConstantsEndosso.madeEndossoDto();
        final ResponseDto responseDto = ConstantsApolice.madeResponseDto();

        when(endossoService.listarEndossosTransmissao(1)).thenReturn(Collections.singletonList(endosso));
        when(endossoService.atualizarStatusTransmissao(endosso, responseDto, 'A')).thenReturn(true);
        when(envioSusepService.transmitir(endossoDto, "endosso/" + endosso.getIdentificadorRegistro(), HttpMethod.PUT)).thenReturn(responseDto);
        when(construtorEndossoService.construir(endosso)).thenReturn(endossoDto);

        assertDoesNotThrow(() -> transmissaoEndossoService.transmitirEndossos(1, 'A'));
    }

}
