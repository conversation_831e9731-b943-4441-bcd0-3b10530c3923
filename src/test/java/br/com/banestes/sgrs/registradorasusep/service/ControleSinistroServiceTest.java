package br.com.banestes.sgrs.registradorasusep.service;

import br.com.banestes.sgrs.registradorasusep.constants.ConstantsExclusao;
import br.com.banestes.sgrs.registradorasusep.constants.ConstantsSinistro;
import br.com.banestes.sgrs.registradorasusep.model.ControleSinistro;
import br.com.banestes.sgrs.registradorasusep.model.exclusao.ExclusaoLayout;
import br.com.banestes.sgrs.registradorasusep.repository.ControleSinistroRepository;
import br.com.banestes.sgrs.registradorasusep.service.ControleSinistroService;
import br.com.banestes.sgrs.registradorasusep.service.SinistroUpdateService;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ControleSinistroServiceTest {

    @Mock
    private ControleSinistroRepository controleSinistroRepository;
    @Mock
    private SinistroUpdateService sinistroUpdateService;
    @InjectMocks
    private ControleSinistroService controleSinistroService;

    @Test
    void atualizar() {
        final ControleSinistro controleSinistro = ConstantsSinistro.madeControleSinistro();
        when(controleSinistroRepository.findById(1L)).thenReturn(Optional.of(controleSinistro));

        assertDoesNotThrow(() -> controleSinistroService.atualizar(1L, "R", "Erro Teste", 'I'));
    }

    @Test
    void atualizarRegistroExclusao() {
        final ExclusaoLayout exclusaoLayout = ConstantsExclusao.madeExclusaoLayout();
        final ControleSinistro controleSinistro = ConstantsSinistro.madeControleSinistro();
        when(controleSinistroRepository.findById(exclusaoLayout.getIdtLeiaute())).thenReturn(Optional.of(controleSinistro));

        assertDoesNotThrow(() -> controleSinistroService.atualizarRegistroExclusao(exclusaoLayout, "R"));
    }

}
