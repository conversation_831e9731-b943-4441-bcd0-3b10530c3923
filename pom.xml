<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <name>seguros-registradorasusep-batch</name>
    <description>Aplicação transmissão de dados para a registradora susep</description>

    <groupId>br.com.banestes.sgrs</groupId>
    <artifactId>seguros-registradorasusep-batch</artifactId>
    <version>0.0.1-SNAPSHOT</version>

    <properties>
        <java.version>1.8</java.version>
        <sonar.coverage.exclusions>
            src/main/java/br/com/banestes/sgrs/Application.java,
            src/main/java/br/com/banestes/sgrs/registradorasusep/apolice/batch/*,
            src/main/java/br/com/banestes/sgrs/registradorasusep/complementar/batch/*,
            src/main/java/br/com/banestes/sgrs/registradorasusep/endosso/batch/*,
            src/main/java/br/com/banestes/sgrs/registradorasusep/exclusao/batch/*,
            src/main/java/br/com/banestes/sgrs/registradorasusep/liquidacaopremio/batch/*,
            src/main/java/br/com/banestes/sgrs/registradorasusep/movimentosinistro/batch/*,
            src/main/java/br/com/banestes/sgrs/registradorasusep/sinistro/batch/*,
            src/main/java/br/com/banestes/sgrs/registradorasusep/model/**,
            src/main/java/br/com/banestes/sgrs/registradorasusep/dto/**,
            src/main/java/br/com/banestes/sgrs/registradorasusep/config/*,
            src/main/java/br/com/banestes/sgrs/registradorasusep/exception/*
        </sonar.coverage.exclusions>
    </properties>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath />
    </parent>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-batch</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.batch</groupId>
            <artifactId>spring-batch-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>10.2.2.jre8</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>br.com.banestes.common.auth</groupId>
            <artifactId>auth-sso-common</artifactId>
            <version>2.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>
        <dependency>
            <groupId>br.com.banestes.sgrs</groupId>
            <artifactId>banseg-logger-lib</artifactId>
            <version>0.2.0</version>
        </dependency>
        <dependency>
            <groupId>br.com.banestes.sgrs</groupId>
            <artifactId>banseg-common-lib</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>